<!-- 业务组件：部门架构树+成员搜索 -->
<!-- 引用位置 业务组件：Companyfull-->
<template>
    <div class="company-dept-tree">
        <div v-if="showMemberSearch" @keyup.enter.prevent.stop="handleSearchMember">
            <el-input :placeholder="$t('companyDeptTree.searchTip')" size="small" class="ssq-search department-search" @blur="handleSearchMember" v-model.trim="memberAccount">
                <i slot="suffix" class="el-icon-ssq-sousuo" @click="handleSearchMember"></i>
            </el-input>
        </div>
        <div v-if="showDeptSearch" @keyup.enter.prevent.stop="handleDeptSearch">
            <el-input v-if="type === 'group'" placeholder="请输入企业名称" size="small" class="ssq-search department-search" v-model.trim="groupSearchParams.entName">
                <i slot="suffix" class="el-icon-ssq-sousuo" @click="handleDeptSearch"></i>
            </el-input>
            <el-input placeholder="请输入部门名称" size="small" class="ssq-search department-search" v-model.trim="groupSearchParams.departName">
                <i slot="suffix" class="el-icon-ssq-sousuo" @click="handleDeptSearch"></i>
            </el-input>
        </div>
        <el-tree
            v-loading="treeLoading"
            :data="treeData"
            :props="defaultProps"
            :check-strictly="selectAble"
            @node-click="handleNodeClick"
            @node-expand="handleNodeExpand"
            @check-change="handleCheckChange"
            :highlight-current="true"
            node-key="deptId"
            :default-expanded-keys="expandedKeys"
            class="ssq-tree dept-tree"
            :show-checkbox="selectAble"
            ref="companyDeptTree"
            :filter-node-method="filterNode"
        >
        </el-tree>
        <div v-if="showLoadMore" class="load-more cur-pointer" @click="handleLoadMore">
            {{ $t('companyDeptTree.loadMore') }}</div>
    </div>

</template>

<script>
const TREE_NODE_LENGTH = 50;
export default {
    props: {
        selectAble: {
            type: Boolean,
            default: false,
        },
        showMemberSearch: {
            type: Boolean,
            default: true,
        },
        showDeptSearch: {
            type: Boolean,
            default: false,
        },
        companySelectAble: {
            type: Boolean,
            default: true,
        },
        onlyActive: {
            type: Boolean,
            default: true,
        },
        // 企业部门的企业ID
        entId: {
            type: String,
            default: '',
        },
        // 集团ID
        entGroupId: {
            type: String,
            default: '',
        },
        // 部门树类型：企业'company' || 集团'group'
        type: {
            type: String,
            default: 'company',
        },
    },
    data() {
        return {
            expandedKeys: [],
            // 组织架构树数据
            departmentData: [

            ],
            // 设置子节点和节点名称的参数名
            defaultProps: {
                children: 'childDeptVOS',
                label: 'deptName',
            },
            memberAccount: '',
            curDept: null,
            treeLoading: false,
            showEndIndex: TREE_NODE_LENGTH,
            groupSearchParams: {
                entName: '',
                departName: '',
                pageSize: 50,
                pageNum: 1,
            },
            total: 0,
        };
    },
    computed: {
        showLoadMore() {
            if (this.type === 'group') {
                return this.departmentData.length < this.total;
            }
            return this.departmentData.length > TREE_NODE_LENGTH && this.showEndIndex < this.departmentData.length;
        },
        treeData() {
            if (this.type === 'group') {
                return this.departmentData;
            }
            return this.showLoadMore ? this.departmentData.slice(0, this.showEndIndex)
                : this.departmentData;
        },
    },
    methods: {
        handleLoadMore() {
            if (this.type === 'group') {
                this.groupSearchParams.pageNum += 1;
                this.getDepartments({ type: 'update' });
            }
            this.showEndIndex += TREE_NODE_LENGTH;
        },
        getDepartmentsAjax() {
            if (this.type === 'group') {
                return this.$http.get(`/ents/ent-with-departs/${this.entGroupId}/paging`, {
                    params: this.groupSearchParams,
                });
            } else {
                return this.$http.get(`/ents/depts${this.entId ? `?entId=${this.entId}` : ''}`);
            }
        },
        handleDepartments(option, data) {
            let resData;
            if (this.type === 'group') {
                this.total = data.total;
                resData = data.records || [];
                resData.forEach(item => {
                    if (item.bizName) {
                        item.deptName = `${item.deptName}_${item.bizName}`;
                    }
                    item.disabled = !this.companySelectAble;
                    item.type = option.type;
                });
                this.departmentData = option.type === 'init' ? resData : [...this.departmentData, ...resData];
                this.groupSearchParams.pageNum === 1 && this.handleCurDept(option, resData[0]);
                this.$nextTick(function() {
                    this.$refs.companyDeptTree.filter(this.groupSearchParams.departName);
                });
            } else  {
                resData = data || {};
                if (resData.bizName) {
                    resData.deptName = `${resData.deptName}_${resData.bizName}`;
                }
                // 设置根节点的 disabled 为 true，则禁用选中
                resData.disabled = !this.companySelectAble;
                resData.type = option.type;
                this.departmentData = [resData];
                this.handleCurDept(option, resData);
            }
        },
        handleCurDept(option, curDept) {
            this.expandedKeys = [];
            this.expandedKeys.push(curDept.deptId);
            if (option.expandedKey) {
                this.expandedKeys.push(option.expandedKey);
            }
            this.curDept = curDept;
            this.$emit('setTopDeptId', curDept.deptId);
            this.$emit('getCurrentDept', curDept);
            this.$emit('getWholeCompanyDept', curDept);
            this.$nextTick(function() {
                this.handleSetCurrentKey(this.expandedKeys[0]);
            });
        },
        /**
         * * 获取所有部门列表
         * * option.type 用于在成员管理页判断是否需要获取部门成员
         * * 如果 type = update 则只更新当前部门的数据
         * @param  option {type: 'init','update'}
         *
         */
        getDepartments(option) {
            this.treeLoading = true;
            this.getDepartmentsAjax()
                .then((res) => {
                    this.handleDepartments(option, res.data);
                })
                .finally(() => {
                    this.treeLoading = false;
                });
        },
        // 搜索成员，并将数据返回给父组件
        handleSearchMember() {
            const searchAllPath = `/ents/employees/search?searchContent=${this.memberAccount}&deptId=${this.curDept.deptId}`;
            const searchActivePath = `/ents/employees/active/search?searchContent=${this.memberAccount}&deptId=${this.curDept.deptId}`;
            const searchPath = this.onlyActive ? searchActivePath : searchAllPath;
            if (this.memberAccount === '') {
                this.$emit('getCurrentDept', this.curDept);
            } else {
                this.$http.get(searchPath,
                               {
                                   params: {
                                       entId: this.entId,
                                   },
                               })
                    .then(res => {
                        this.$emit('seachResult', {
                            keyword: this.memberAccount,
                            data: res.data,
                        });
                    });
            }
            return false;
        },
        // 点击节点时，父组件可以获取当前节点的数据
        handleNodeClick(data) {
            this.curDept = data;
            this.$emit('getCurrentDept', data);
        },
        // 展开节点，返回当前节点的数据
        handleNodeExpand() {
            // this.$emit('getCurrentDept', data);

        },
        // 选中节点时，父组件可以获取当前节点的数据
        handleCheckChange(data, checked) {
            if (!this.companySelectAble && data.deptLevel === 1) {
                return false;
            }

            data.checked = checked;

            if (checked === true) {
                this.$emit('getCheckedDept', data);
            } else {
                this.$emit('getDisCheckDept', data);
            }
            // if (data.deptLevel != 1){
            // data.checked = checked;

            // }
        },
        // 通过 deptId[] 选择节点
        handleCheckNode(list) {
            this.$refs.companyDeptTree.setCheckedKeys(list);
        },
        // 通过 deptId 取消选择节点
        handleUnCheckNode(key) {
            this.$refs.companyDeptTree.setChecked(key, false);
        },
        handleSetCurrentKey(deptId) {
            this.$refs.companyDeptTree.setCurrentKey(deptId);
        },
        handleDeptSearch() {
            if (this.type === 'group') {
                this.departmentData = [];
                this.total = 0;
                this.groupSearchParams.pageNum = 1;
                this.getDepartments({ type: 'init' });
            } else {
                this.$refs.companyDeptTree.filter(this.groupSearchParams.departName);
            }
        },
        filterNode(value, data) {
            if (!value) {
                return true;
            }
            return data.deptName.includes(value);
        },
    },
    beforeMount() {
        this.getDepartments({ type: 'init' });
    },
};
</script>
<style lang="scss">
.company-dept-tree{
    .el-tree.dept-tree{
        overflow: hidden;
        .el-tree-node__content{
            height: auto;
            .el-tree-node__label::before{
                height: 0;
            }
        }
        .el-tree-node__label{
            white-space: pre-wrap;
            padding: 10px 25px 10px 0;
            line-height: 20px;
            margin-left: 0;
        }
        [dir=rtl] & {
            .el-tree-node__label {
                padding: 10px 0 10px 25px;
            }

            .el-tree-node:not(.is-expanded) .el-icon-caret-right {
                transform: rotate(180deg);
            }

            .el-tree-node__children > .el-tree-node {
                & > .el-tree-node__content{
                     padding-left: 0 !important;
                     padding-right: 18px;
                }
                .el-tree-node .el-tree-node__content{
                    padding-left: 0 !important;
                    padding-right: 36px;

                    .el-tree-node .el-tree-node__content{
                        padding-left: 0 !important;
                        padding-right: 54px;
                    }
                }
            }
        }
    }
    .load-more {
        width: 100%;
        text-align: center;
        padding: 5px 0 10px;
        font-size: 14px;
        color: $--color-primary;
    }
}

</style>
