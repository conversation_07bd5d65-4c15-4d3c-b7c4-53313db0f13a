<template>
    <div v-if="!isNewGroupPath && hasManager" class="ent-notice">
        <div class="ent-notice__title">
            <div>
                {{ $t('CSSetting.setNotifyEmail') }}
            </div>
            <div class="ent-notice__btn" @click="handleShowSlide">{{ $t('CSSetting.setNoticeMethod') }}</div>
        </div>
        <div class="ent-notice__number" v-if="contractAmountsCanEdit">
            <span>{{ $t('CSSetting.notifyEmailTip1') }}</span>
            <span v-for="(item, index) in contractAmounts" :key="index">
                <el-input  v-model.number="contractAmounts[index]"></el-input>
                <span v-if="index !== contractAmounts.length - 1">{{ $i18n.locale === 'en' ? ',' : ( $i18n.locale === 'ar'? 'أو' : '、') }}</span>
            </span>
            
            <span>{{ $t('CSSetting.notifyEmailTip2') }}</span>
            <el-button type="primary" @click="saveContractAmounts">{{ $t('CSCommon.save') }}</el-button>
        </div>
        <div v-else>
            <span>{{ $t('CSSetting.notifyEmailTip1') }}</span>
            <span v-for="(item, index) in contractAmounts" :key="index">
                {{ item }}
                <span v-if="index !== contractAmounts.length - 1">{{ $i18n.locale === 'en' ? ',' : ( $i18n.locale === 'ar'? 'أو' : '、') }}</span>
            </span>
            <span>{{ $t('CSSetting.notifyEmailTip2') }}</span>
            <el-button type="primary" @click="contractAmountsCanEdit = true">{{ $t('CSCommon.edit') }}</el-button>
        </div>
        <div class="ent-notice__detail">     
            <span v-for="notice in noticeList" :key="notice">{{ notice }}</span>
        </div>
        <SlidePopModel class="ent-notice__slide" :outsideShow.sync="showSlide">
            <div class="ent-notice__slide-box" slot="slide-pop-content">
                <div class="ent-notice__slide-title">{{ $t('CSSetting.setNoticeMethod') }}</div>
                <div class="ent-notice__slide-content" v-show="!addType">
                    <template v-if="mobile && !getIsForeignVersion">
                        <div class="ent-notice__slide-label">
                            <span>通知手机号</span>
                            <span v-if="mobile" @click="handleDeleteMobile" class="operate">删除</span>
                        </div>
                        <div class="ent-notice__slide-item">
                            <span>{{ mobile }}</span><span class="operate" @click="addType = 'phone'">修改</span>
                        </div>
                    </template>
                    <template v-if="emails.length">
                        <div class="ent-notice__slide-label"><span>{{ $t('CSSetting.noticeEmail') }}</span></div>
                        <div class="ent-notice__slide-item" v-for="(email, i) in emails" :key="i">
                            <el-input :class="{error: email.error}" v-model="email.value" clearable></el-input>
                            <span class="operate" @click="emails.splice(i, 1)">{{ $t('CSCommon.delete') }}</span>
                            <span class="error-msg" v-show="email.error">{{ email.error }}</span>
                        </div>
                    </template>
                    <div class="add">
                        <template v-if="!mobile && !getIsForeignVersion">
                            <span class="operate" @click="addType = 'phone'">+添加通知手机号</span>&nbsp;&nbsp;|&nbsp;&nbsp;
                        </template>
                        <span class="operate" @click="emails.push({ value: '', error: false })">{{ $t('CSSetting.addEmail') }}</span>
                    </div>
                </div>
                <el-form class="ent-notice__slide-content" v-show="addType && !getIsForeignVersion">
                    <el-form-item label="通知手机号">
                        <el-input v-model="addMobile"></el-input>
                    </el-form-item>
                    <el-form-item label="手机号验证码">
                        <el-input v-model="verifyCode" maxlength="6">
                            <template slot="append">
                                <CountDown class="countDown"
                                    :clickedFn="handleClickSendCode"
                                    :disabled="countDownDisabled"
                                    ref="btn"
                                    :second="60"
                                ></CountDown>
                            </template>
                        </el-input>
                    </el-form-item>
                </el-form>
                <div class="ent-notice__slide-btn">
                    <el-button type="primary" @click="handleSave">{{ $t('CSCommon.save') }}</el-button>
                    <el-button v-show="addType" @click="handleCancel">{{ $t('CSCommon.cancel') }}</el-button>
                </div>
            </div>
        </SlidePopModel>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import SlidePopModel from 'pub-components/slidePop/index.vue';
import CountDown from 'pub-components/countDown';
import resRules from 'pub-utils/regs.js';
export default {
    components: {
        SlidePopModel,
        CountDown,
    },
    data() {
        return {
            emails: [],
            mobile: '',
            addType: '',
            showSlide: false,
            showAddSlide: false,
            addEmail: '',
            addMobile: '',
            verifyCode: '',
            verifyKey: '',
            countDownDisabled: false,
            noticeList: [],
            contractAmountsCanEdit: false,
            contractAmounts: [],
        };
    },
    computed: {
        ...mapGetters([
            'isNewGroupPath',
            'hasManager',
            'getIsForeignVersion',
        ]),
    },
    watch: {
        showSlide(val) {
            if (!val) {
                this.handleCancel();
            }
        },
    },
    methods: {
        handleShowSlide() {
            this.getNoticeConfig();
            this.showSlide = true;
        },
        getNoticeConfig() {
            return this.$http('/ents/charging/query-notice-list').then(({ data: { emails, mobile } }) => {
                this.emails = (emails || []).map(item => {
                    return {
                        value: item,
                        error: false,
                    };
                });
                this.mobile = mobile;
                this.noticeList = (mobile ? [mobile] : []).concat(emails || []);
            });
        },
        handleDeleteMobile() {
            this.$http.delete('/ents/charging/notice-mobile').then(async() => {
                await this.getNoticeConfig();
                this.$MessageToast.success('删除成功');
            });
        },
        validMobile() {
            return new Promise((resolve, reject) => {
                if (!resRules.userPhone.test(this.addMobile)) {
                    this.$MessageToast.error(this.$t('validationMsg.enterCorrectPhoneNum'));
                    return reject();
                }
                return resolve();
            });
        },
        async handleClickSendCode() {
            await this.validMobile();
            this.countDownDisabled = true;
            setTimeout(() => {
                this.$refs.btn.run();
                this.countDownDisabled = false;
            }, 0);
            this.$http.sendVerCode({
                code: 'B004',
                sendType: 'S',
                target: this.addMobile,
            }).then((res) => {
                this.verifyKey = res.data.value;
                this.$MessageToast.success(this.$t('autoSeal.SentSuccessfully'));
            }).catch(() => {
                this.$refs.btn.reset();
            });
        },
        handleSave() {
            if (this.addType === 'phone') {
                return this.saveNoticeMobile();
            }
            this.saveNoticeEmail();
        },
        saveContractAmounts() {
            if(this.contractAmounts.some((item)=> !item)) {
                return this.$MessageToast.error(this.$t('CSSetting.balanceRemind'));
            }
            this.$confirm(this.$t('CSSetting.saveTip'), this.$t('CSCommon.tip'), {
                confirmButtonText: this.$t('CSCommon.confirm'),
                cancelButtonText: this.$t('CSCommon.cancel'),
            }).then(()=> {
                this.$http.put('/ents/configs/wallet-balance-threshold', {
                    tieredAmounts: this.contractAmounts,
                }).then(()=> {
                    this.contractAmountsCanEdit = false;
                })
            })
        },
        getContractAmounts() {
            this.$http.get('/ents/configs/wallet-balance-threshold')
                .then(res => {
                    this.contractAmounts = res.data.tieredAmounts || [4000, 1000, 100, 10];
                })
        },
        async saveNoticeMobile() {
            await this.validMobile();
            if (!this.verifyCode) {
                this.$MessageToast.error('请输入验证码');
                return;
            }
            return this.$http.post('/ents/charging/notice-mobile', {
                mobile: this.addMobile,
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
            }).then(async() => {
                await this.getNoticeConfig();
                this.$MessageToast.success('保存成功');
                this.handleCancel();
            });
        },
        async saveNoticeEmail() {
            await this.validEmails();
            const emails = this.emails.map(item => item.value);
            return this.$http.post('/ents/configs/NOTICE_EMAIL', {
                name: 'NOTICE_EMAIL',
                value: emails.join(','),
            }).then(async() => {
                await this.getNoticeConfig();
                this.$MessageToast.success('保存成功');
                this.showSlide = false;
            });
        },
        validEmails() {
            return new Promise((resolve, reject) => {
                let bool = true;
                this.emails.forEach(item => {
                    if (!item.value) {
                        item.error = this.$t('CSSetting.emptyNotifyEmail');
                        bool = false;
                    } else if (!resRules.userEmail.test(item.value)) {
                        item.error = this.$t('CSSetting.invalidNotifyEmail');
                        bool = false;
                    }
                });
                return bool ? resolve() : reject();
            });
        },
        handleCancel() {
            this.addType = '';
            this.addMobile = '';
            this.verifyCode = '';
            this.verifyKey = '';
            this.$refs.btn.reset();
        },
    },
    created() {
        this.getNoticeConfig();
        this.getContractAmounts();
    },
};
</script>

<style lang="scss">
.ent-notice {
    font-size: 12px;
    padding: 15px 20px;
    background: $--background-color-regular;
    &__title{
        display: flex;
        justify-content: space-between;
        margin-bottom: 5px;
    }
    &__number {
        .el-input {
            width: 66px;
            margin-right: 10px;
        }
    }
    &__detail{
        margin-top: 5px;
        display: flex;
        flex-wrap: wrap;
        margin-left: -10px;
        span{
            padding: 0 10px;
            &:not(:last-child){
                border-right: 1px solid $--border-color-base;
            }
        }
    }
    &__tooltip{
        color: $--color-info;
    }
    &__btn{
        color: $--color-primary;
        cursor: pointer;
    }
    &__slide{
        &-box{
            height: 100%;
            display: flex;
            flex-direction: column;
        }
        &-title{
            font-size: 14px;
            line-height: 54px;
            padding: 0 20px;
            border-bottom: 1px solid $--border-color-lighter;
        }
        &-content{
            display: flex;
            flex-direction: column;
            flex: 1;
            padding: 20px;
            .operate{
                color: $--color-primary;
                cursor: pointer;
                &:hover{
                    color: $--color-primary-light-2;
                }
            }
            .add{
                color: $--color-info;
                text-align: center;
            }
            .el-input-group__append{
                width: 112px;
                text-align: center;
                .countDown{
                    border: none;
                    background: transparent;
                }
            }
        }
        &-btn{
            background: $--background-color-regular;
            padding: 25px 0;
            text-align: center;
        }
        &-label, &-item{
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            position: relative;
            .el-input{
                width: unset;
                &.error input{
                    border-color: $--color-danger;
                }
            }
            .error-msg{
                color: $--color-danger;
                position: absolute;
                bottom: -16px;
            }
        }
    }
}
</style>
