<template>
    <div>
        <!-- 购买弹窗 -->
        <QuickPay
            v-if="showPayDialog"
            :dialogVisible="showPayDialog"
            type="hubble"
            :hubbleOrderInfo="orderInfo"
            @handlePaySuccess="handlePaySuccess"
            @handlePayCancel="handleClose"
        >
        </QuickPay>
        <el-dialog
            v-else
            class="hubble-component__package"
            :visible.sync="value"
            title="我的版本"
            :before-close="handleClose"
            :close-on-click-modal="false"
        >
            <ul class="hubble-component__package-list">
                <li class="hubble-component__package-item" v-if="showFreePackage">
                    <div class="hubble-component__package-header free">
                        <div class="hubble-component__package-header__title">体验版</div>
                        <span class="hubble-component__package-header__price">Free / 免费</span>
                    </div>
                    <div class="hubble-component__package-content">
                        <span>套餐包含内容为：</span><br>
                        <span> - {{ freePackage.documentCount }}份文档</span><br>
                        <span> - {{ freePackage.chatCount }}次对话</span><br>
                        <span> - 300页/文档</span><br>
                        <span> - 20MB/文档</span><br>
                    </div>
                    <div class="hubble-component__package-status" v-if="freePackage.isOpen">
                        <b>已开通</b>
                        有效期至 {{ freePackage.expireTime }}
                    </div>
                    <el-button v-else :disabled="turnonLoading" type="primary" @click="handleTurnon('free')">立即开通</el-button>
                </li>
                <li class="hubble-component__package-item">
                    <div class="hubble-component__package-header person">
                        <div class="hubble-component__package-header__title">个人版</div>
                        <s>￥{{ personPackage.originPrice }}</s>&nbsp;
                        <span class="hubble-component__package-header__price">￥{{ personPackage.currentPrice }} / 月</span>
                    </div>
                    <div class="hubble-component__package-content">
                        <span>套餐包含内容为：</span><br>
                        <span> - {{ personPackage.documentCount }}份文档</span><br>
                        <span> - {{ personPackage.chatCount }}次对话</span><br>
                        <span> - 300页/文档</span><br>
                        <span> - 20MB/文档</span><br>
                    </div>
                    <div class="hubble-component__package-status" v-if="personPackage.isOpen">
                        <b>已开通</b>
                        有效期至 {{ personPackage.expireTime }}
                    </div>
                    <el-button v-else :disabled="turnonLoading" type="primary" @click="handleTurnon('person')">立即开通</el-button>
                </li>
                <li class="hubble-component__package-item">
                    <div class="hubble-component__package-header group">
                        <div class="hubble-component__package-header__title">团队版</div>
                        <span class="hubble-component__package-header__price">联系我们</span>
                    </div>
                    <div class="hubble-component__package-content">
                        可以联系我们的专业人员，我们会根据您的情况提供专属方案。
                    </div>
                    <el-button type="primary" @click="handleContact">联系我们</el-button>
                </li>
            </ul>
            <template v-if="hasAuthorized">
                <el-checkbox v-model="agreeAuthorize">我已阅读并同意</el-checkbox> <a class="instruction" target="_blank" href="/哈勃产品使用须知.pdf">《文档授权协议》</a>
            </template>
        </el-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import JSConfetti from 'js-confetti';
import QuickPay from 'pub-businessComponents/dialog/QuickPay';

export default {
    components: {
        QuickPay,
    },
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        currentPackage: {
            type: Object,
            default: () => {},
        },
    },
    data() {
        return {
            loading: false,
            packageData: {},
            hasAuthorized: false,
            agreeAuthorize: false,
            showPayDialog: false,
            turnonLoading: false,
            orderInfo: {}, // 支付订单信息
        };
    },
    computed: {
        freePackage() {
            return this.packageData.trialPlanInfo || {};
        },
        personPackage() {
            return this.packageData.personBasicPlanInfo || {};
        },
        showFreePackage() {
            return !this.personPackage.isOpen;
        },
    },
    watch: {
        async currentPackage(val) {
            await this.initPackageList();
            Object.keys(this.packageData).forEach(key => {
                if (this.packageData[key].planType === val.planType) {
                    this.packageData[key] = {
                        ...this.packageData[key],
                        ...val,
                        isOpen: true,
                        expireTime: dayjs(val.expireTime).format('YYYY-MM-DD'),
                    };
                }
            });
        },
    },
    methods: {
        initPackageList() {
            return this.$http('/web/hubble/users/aggregation/plan-basic-detail').then(res => {
                this.packageData = res.data;
            });
        },
        getAuthorizeStatus() {
            this.$http('/web/hubble/users/permissions').then(({ data: { ifHubbleChatAuthorized } }) => {
                this.hasAuthorized = ifHubbleChatAuthorized;
            });
        },
        handleClose() {
            this.$emit('input', false);
            this.interval && clearInterval(this.interval);
            this.showPayDialog = false;
        },
        handleContact() {
            this.$featureSupport('hubble');
        },
        async handleTurnon(type) {
            if (!this.hasAuthorized && !this.agreeAuthorize) {
                return this.$MessageToast.error('请先阅读并同意《文档授权协议》');
            }
            this.turnonLoading = true;
            if (type === 'free') {
                await this.turnonFree();
            } else {
                await this.turnonPerson();
            }
            !this.hasAuthorized && await this.handleAuthorize();
            this.getAuthorizeStatus();
        },
        handleAuthorize() {
            return this.$http.post('/web/hubble/users/permissions/agree-authorization-letter');
        },
        turnonFree() {
            return this.$http.post('/web/hubble/users/trial-account-init').then(() => {
                this.handlePaySuccess(true);
            }).finally(() => {
                this.turnonLoading = false;
            });
        },
        turnonPerson() {
            return this.$http.post(`ents/charging/advancedFeatures/ordering`, {
                ids: [this.personPackage.productPackageId],
                isNewGroup: false,
            }).then(async res => {
                this.orderInfo = res.data;
                this.turnonLoading = false;
                this.showPayDialog = true;
            }).catch(() => {
                this.turnonLoading = false;
            });
        },
        handlePaySuccess(isShow) {
            this.handleClose();
            this.$emit('handleNext');
            if (isShow) {
                const confetti = new JSConfetti();
                confetti.addConfetti().then(() => {
                    this.$MessageToast.success('开通成功');
                });
            }
        },
    },
    created() {
        this.getAuthorizeStatus();
    },
};
</script>

<style lang="scss">
.hubble-component__package{
    .el-dialog{
        width: unset !important;
        border-radius: 4px !important;
        position: absolute;
        left: 50%;
        transform: translateX(-50%);
        .el-dialog__body{
            padding: 30px !important;
        }
    }
    &-pay{
        display: flex;
        flex-direction: row;
        &__img{
            width: 180px;
            height: 180px;
            margin-right: 30px;
            border: 1px solid #ccc;
        }
        &__tips li{
            list-style-type: auto;
        }
    }
    &-list{
        display: flex;
        margin-bottom: 35px;
    }
    &-item{
        width: 220px;
        height: 320px;
        box-sizing: border-box;
        border: 1px solid rgba(234,234,234,1);
        border-radius: 8px;
        overflow: hidden;
        position: relative;
        cursor: default;
        & + .hubble-component__package-item{
            margin-left: 20px;
        }
        &:hover{
            border-color: #0C8AEE;
            box-shadow: 0px 0px 10px 0px rgba(12,138,238,0.2);
        }
    }
    &-header{
        height: 88px;
        padding: 25px 25px 0;
        box-sizing: border-box;
        &.free{
            background-image: linear-gradient(116deg, #FBFBFB 0%, #D3D3D3 100%);
        }
        &.person{
            background-image: linear-gradient(113deg, #C5E1FF 0%, #80BCFF 100%);
        }
        &.group{
            background-image: linear-gradient(112deg, #696969 0%, #2C2C2D 100%);
            .hubble-component__package-header__title, .hubble-component__package-header__price{
                color: #FFFFFF;
            }
        }
        &__title{
            font-size: 18px;
        }
        &__price{
            color: #0C8AEE;
        }
    }
    &-content{
        font-size: 12px;
        color: #666666;
        padding: 25px;
        line-height: 24px;
    }
    &-status{
        font-size: 12px;
        text-align: center;
        margin-top: -10px;
        b{
            display: block;
            font-size: 14px;
            margin-bottom: 10px;
        }
    }
    .el-button{
        width: 180px;
        line-height: 38px;
        padding: 0;
        position: absolute;
        bottom: 25px;
        left: calc((100% - 180px) / 2);
        text-align: center;
        border-radius: 4px;
    }
    a.instruction:hover{
        text-decoration: underline;
    }

}
.quick-pay .pay-dialog-pay .wechat-pay-qrcode .qrcode-img-ali{
    width: 130px !important;
    height: 130px !important;
}
</style>
