<template>
    <div v-if="ifDisplayHubbleLogo">
        <div class="hubble-component__entry" @click="checkPackage">
            <img src="~pub-images/AIBot.png" alt="">
            <span>Hubble</span>
        </div>
        <PackageDialog
            @handleNext="handleChat"
            v-model="showPackageDialog"
            :currentPackage="currentPackage"
        ></PackageDialog>
    </div>
</template>

<script>
import PackageDialog from './package/index.vue';
export default {
    components: {
        PackageDialog,
    },
    data() {
        return {
            showPackageDialog: false,
            currentPackage: {},
            ifDisplayHubbleLogo: false,
        };
    },
    methods: {
        handleChat() {
            this.$emit('handleChat');
        },
        initAccount() {
            return this.$http.post('/web/hubble/users/beta-plan-init');
        },
        async checkPackage() {
            await this.initAccount();
            this.$http('web/hubble/users/overview-plan-detail').then(res => {
                this.currentPackage = res.data.currentUserPlan;
                if (this.currentPackage && this.currentPackage.planType) {
                    this.handleChat();
                } else {
                    this.showPackageDialog = true;
                }
            });
        },
    },
    created() {
        this.$http('web/hubble/users/can-display-hubble-logo').then(({ data: { ifDisplayHubbleLogo } }) => {
            this.ifDisplayHubbleLogo = ifDisplayHubbleLogo;
        });
    },
};
</script>

<style lang="scss">
.hubble-component__entry{
    @keyframes rotate {
        0% {
            transform: rotate(0deg);
        }
        25% {
            transform: rotate(15deg);
        }
        50% {
            transform: rotate(0deg);
        }
        75% {
            transform: rotate(-15deg);
        }
        100% {
            transform: rotate(0deg);
        }
    }
    position: fixed;
    top: 70px;
    right: -30px;
    font-size: 14px;
    height: 32px;
    width: 118px;
    line-height: 32px;
    padding: 0 12px;
    color: #fff;
    background: $theme-color;
    box-shadow: 0px 2px 8px 0px rgba(12,138,238,0.2);
    border-radius: 100px;
    cursor: pointer;
    img{
        width: 20px;
        height: 20px;
        margin-top: 6px;
        margin-right: 6px;
        float: left;
        animation: rotate 1s linear infinite;
    }
    i{
        font-size: 12px;
        margin-left: 6px;
    }
}

</style>
