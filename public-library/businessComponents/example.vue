<template>
    <div>
        <!-- 通用头部 -->
        <CommonHeader></CommonHeader>
        <!-- 组织架构对话框 -->
        <CompanyFullDialog
            :selectType="dialogShow.departmentDialog.type"
            :onlyActive="dialogShow.departmentDialog.onlyActive"
            :state="dialogShow.departmentDialog.show"
            @close="companyFullDialogClose"
            @choose="companyFullDialogClose"
        ></CompanyFullDialog>
        <!-- 实名认证-所有图标认证部分 -->
        <!-- <AllIconsCon
            title="点亮更多实名图标"
            :bankcard-icons-status="certificationStatusInfo.bankCard"
            :phone-icons-status="certificationStatusInfo.phone"
            :face-icons-status="certificationStatusInfo.face"
            :inhand-icons-status="certificationStatusInfo.inHand"
            :notmainland-icons-status="certificationStatusInfo.notMainland"
        >
        </AllIconsCon> -->
        <OutOfCourt
            type="1"
            :name="userBaseInfo.realName"
            :id-number="userBaseInfo.idNumber"
            :rejectInfo="userBaseInfo.rejectInfo"
        >
        </OutOfCourt>
        <PermissionNoticeCon>
        </PermissionNoticeCon>

        <!-- 选择业务线 -->
        <SelectBizLine
            :visible.sync="selectLine.show"
            :selectedLineEntId.sync="selectLine.selEntId"
            :bizLineList="selectLine.bizLineList"
            :callBack="selectLine.callBack"
        ></SelectBizLine>
        <!-- 通用底部 -->
        <CommonFooter></CommonFooter>
    </div>
</template>
<script>
import CommonHeader from 'pub-businessComponents/header';
import CommonFooter from 'pub-businessComponents/footer';
import CompanyFullDialog from 'pub-businessComponents/dialog/CompanyFull.vue';
// import AllIconsCon from 'pub-businessComponents/certificationAllIconsStatic';
import OutOfCourt from 'pub-businessComponents/certificationOutOfCourt';
import PermissionNoticeCon from 'pub-businessComponents/certificationPermissionNoticeStatic';
import SelectBizLine from 'pub-businessComponents/selectBizLine';
export default {
    components: {
        CommonHeader,
        CommonFooter,
        CompanyFullDialog,
        // AllIconsCon,
        OutOfCourt,
        PermissionNoticeCon,
        SelectBizLine,
    },
    data() {
        return {
            dialogVisible: false,
            dialogShow: {
                departmentDialog: {
                    show: true,
                    type: 'checkBox',
                    onlyActive: true, // 是否只显示可使用的员工
                },
            },
            selectLine: {
                show: false,
                selEntId: '',
                bizLineList: [],
                callBack: () => {},
            },
            certificationStatusInfo: {
                bankCard: { authinfoStatus: 2, auditInfo: '' },
                phone: { authinfoStatus: 2, auditInfo: '' },
                face: { authinfoStatus: 1, auditInfo: '' },
                inHand: { authinfoStatus: 1, auditInfo: '' },
                notMainland: { authinfoStatus: 0, auditInfo: '' },
            },
            userBaseInfo: {
                name: 'dew',
                idNumber: '333',
                rejectInfo: [{
                    authinfoType: 'allInfo',
                }],
            },

        };
    },
    methods: {
        handleClose(done) {
            this.$confirm(this.$t('common.confirmClose')) // 确认关闭？
                .then(() => {
                    done();
                })
                .catch(() => {});
        },
        companyFullDialogClose() {
            this.dialogShow.departmentDialog.show = false;
        },
    },
    mounted() {

    },
};
</script>
