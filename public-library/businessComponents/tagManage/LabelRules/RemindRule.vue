<!-- 第三步：提醒规则 -->
<template>
    <div>
        <div class="tip">{{ $t('TagManage.remindRule.tip') }}</div>
        <el-form :model="remindRuleForm" ref="remindRuleForm" label-width="100px" label-position="top">
            <el-form-item
                prop="days"
                :label="$t('TagManage.remindRule.remindDays')"
            >
                <el-input class="remind-day-input"
                    v-model.trim="remindRuleForm.days"
                    :placeholder="$t('TagManage.remindRule.remindDayTip')"
                    @change="((val) => { handlePutInterval(val) })"
                ></el-input>
            </el-form-item>
            <el-form-item
                v-for="(mail, index) in remindRuleForm.mails"
                :label="$t('TagManage.remindRule.remindPerson') + (index+1)"
                :key="mail.key"
                :prop="'mails.' + index"
                :rules="{ validator: validateAccout, trigger: 'blur' }"
            >
                <el-input v-model.trim="mail.value" :placeholder="$t('TagManage.remindRule.remindPersonTip')"></el-input>
                <i v-show="remindRuleForm.mails.length > 1" class="el-icon-ssq-guanbi1" @click="removeMail(index)"></i>
            </el-form-item>
            <el-form-item v-if="couldAddMail" class="rule-remind__operate_add">
                <span @click="addDMail" class="console-link"><i class="el-icon-ssq-jia"></i> {{ $t('TagManage.remindRule.addRemindPerson') }}</span>
            </el-form-item>
        </el-form>
    </div>
</template>
<script>
import regRules from 'pub-utils/regs.js';
export default {
    props: {
        label: {
            type: Object,
            default: () => {
                return {};
            },
        },
        setTimeMaxValue: {
            type: Number,
            default: 90,
        },
    },
    data() {
        return {
            regRules: regRules,
            remindRuleForm: {
                days: '',
                mails: [],
            },
        };
    },
    computed: {
        couldAddMail() {
            return this.remindRuleForm.mails.length < 15;
        },
        validEmails() {
            const mails = [];
            this.remindRuleForm.mails.forEach((item) => {
                if (item.value !== '' && this.regRules.userEmail.test(item.value)) {
                    mails.push(item.value);
                }
            });
            return mails;
        },
    },
    watch: {
        label: {
            handler(v) {
                this.remindRuleForm.days = +v.notifyConfig.delayedDaysAfterSticking;
                this.remindRuleForm.mails = v.notifyConfig.notifiedEmails.length ? v.notifyConfig.notifiedEmails.map((item) => {
                    return {
                        value: item,
                        key: Date.now(),
                    };
                }) : [{
                    value: '',
                    key: Date.now(),
                }];
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        handlePutInterval(val) {
            if (val < 0) {
                this.$nextTick(() => {
                    this.remindRuleForm.days  = 0;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.minSet')}0${this.$t('TagManage.putLabelRules.day')}`);
            }
            if (val > this.setTimeMaxValue) {
                this.$nextTick(() => {
                    this.remindRuleForm.days = this.setTimeMaxValue;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.maxSet')}${this.setTimeMaxValue}${this.$t('TagManage.putLabelRules.day')}`);
            }
        },
        validateAccout(rule, mail, callback) {
            if (mail.value !== '' && !this.regRules.userEmail.test(mail.value)) {
                callback(new Error(this.$t('TagManage.remindRule.remindEmailTip') + ''));
            } else {
                callback();
            }
        },
        addDMail() {
            this.remindRuleForm.mails.push({
                value: '',
                key: Date.now(),
            });
        },
        removeMail(index) {
            if (this.remindRuleForm.mails.length === 1) {
                return false;
            }
            this.remindRuleForm.mails.splice(index, 1);
        },
        passStepData() {
            this.$emit('passData', 'notifyConfig', {
                delayedDaysAfterSticking: this.remindRuleForm.days,
                notifiedEmails: this.validEmails,
            });
        },
    },
    deactivated() {
        this.passStepData();
    },
};
</script>
