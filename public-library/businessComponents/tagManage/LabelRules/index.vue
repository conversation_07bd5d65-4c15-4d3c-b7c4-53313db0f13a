<!-- 设置标签规则抽屉 -->
<template>
    <div class="set-label-rules__drawer">
        <h1 class="slide-pop-head">
            <p class="slide-pop-head__title">
                {{ $t('TagManage.labelOperate.setLblRuleTip',{name:curOperateLabel.name}) }}
            </p>
        </h1>
        <div class="slide-pop-body">
            <el-tabs v-model="curComponent">
                <el-tab-pane :label="$t('TagManage.putLabelRules.name')" name="PutLabelRule"></el-tab-pane>
                <el-tab-pane :label="$t('TagManage.dropLabelRules.name')" name="DropLabelRule" v-if="!isEntDoc"></el-tab-pane>
                <el-tab-pane :label="$t('TagManage.labelOperate.remind')" name="RemindRule"></el-tab-pane>
            </el-tabs>
            <keep-alive>
                <component
                    class="rules-drawer__content"
                    :is="curComponent"
                    :label="curOperateLabel"
                    @passData="handleStepData"
                    ref="childrens"
                    :putLabelTimeMap="putLabelTimeMap"
                    :dropLabelTimeMap="dropLabelTimeMap"
                    :dropType="dropType"
                    :putType="putType"
                ></component>
            </keep-alive>
        </div>
        <div class="slide-pop-foot">
            <slot name="slide-pop-footer-tip"></slot>
            <el-button
                class="console-btn console-btn-primary"
                @click="handleUpdate"
            >
                {{ $t('TagManage.labelOperate.done') }}
            </el-button>
        </div>
    </div>
</template>
<script>
import PutLabelRule from './PutLabelRule.vue';
import DropLabelRule from './DropLabelRule.vue';
import RemindRule from './RemindRule.vue';

const COMPONENT_MAP = {
    1: 'PutLabelRule',
    2: 'DropLabelRule',
    3: 'RemindRule',
};
export default {
    components: {
        PutLabelRule,
        DropLabelRule,
        RemindRule,
    },
    props: {
        step: {
            type: Number,
            default: 1,
        },
        curOperateLabel: {
            type: Object,
            default: function() {
                return {};
            },
        },
        putLabelTimeMap: {
            type: Array,
            default: () => {
                return [];
            },
        },
        dropLabelTimeMap: {
            type: Array,
            default: () => {
                return [];
            },
        },
        dropType: {
            type: Array,
            default: () => {
                return [];
            },
        },
        putType: {
            type: Array,
            default: () => {
                return [];
            },
        },
        isEntDoc: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            curComponent: '',
            // 三个步骤提交的数据，同步到这里
            stepsData: {
                notifyConfig: {
                    delayedDaysAfterSticking: this.curOperateLabel.notifyConfig.delayedDaysAfterSticking,
                    notifiedEmails: this.curOperateLabel.notifyConfig.notifiedEmails,
                },
                stickRule: {
                    eventDelayDays: this.curOperateLabel.stickRule.eventDelayDays,
                    triggerEvent: this.curOperateLabel.stickRule.triggerEvent,
                },
                tearRule: {
                    eventDelayDays: this.curOperateLabel.tearRule.eventDelayDays,
                    triggerEvent: this.curOperateLabel.tearRule.triggerEvent,
                },
            },
        };
    },
    watch: {
        step: {
            handler(v) {
                this.curComponent = COMPONENT_MAP[v] || 'PutLabelRule';
            },
            immediate: true,
        },
    },
    methods: {
        handleStepData(step, val) {
            this.stepsData[step] = val;
        },
        handleUpdate() {
            // 让当前步骤的子组件先同步数据
            this.$refs.childrens.passStepData();
            this.$emit('update', { ...this.curOperateLabel, ...this.stepsData });
            this.handleClose();
        },
        handleClose() {
            this.$emit('close');
        },
    },
};
</script>
<style lang="scss">
    .slide-pop-head__title {
        width: 85%;
        overflow: hidden;
        white-space: nowrap;
        text-overflow: ellipsis;
    }
    .set-label-rules__drawer{
        .el-tabs__header{
            margin-bottom: 20px;
            .el-tabs__item{
                font-size: 12px;
                color: #1C1D1E;

                &.is-active{
                    color: $theme-color;
                }
            }
            .el-tabs__active-bar{
                height: 2px;
                background-color: $theme-color;
            }
        }
        .el-tabs__nav-wrap {
            padding-left: 15px;
            [dir=rtl] & {
                padding-right: 15px;
                padding-left: 0;
            }
            &::after {
                height: 1px;
                background-color: #E6E6E6;
            }
        }
        .rules-drawer__content{
            padding: 0 20px;

            .tip{
                margin-bottom: 20px;
                line-height: 18px;
                font-size: 12px;
                color: #1C1D1E;
            }

            .el-radio-group{
                .el-form-item{
                    margin-bottom: 0;
                }
                .rule-put__input-days{
                    top: -15px;
                    margin-left: 25px;
                    [dir=rtl] & {
                        margin-left: 0;
                        margin-right: 25px;
                    }

                    .el-input__inner{
                        height: 30px;
                    }
                }
            }

            .rule-put__input-days, .remind-day-input{
                &::after{
                    content: '\5929';
                    display: inline-block;
                    position: absolute;
                    right: 10px;
                    top: 0;
                    font-size: 12px;
                    color: $--color-text-primary;
                    [dir=rtl] & {
                        right: auto;
                        left: 10px;
                    }
                }
            }

            .el-radio{
                display: block;
                margin-bottom: 15px;
                margin-left: 0;
                line-height: 16px;
            }

            .el-form-item__label, .el-input__inner, .el-radio__label{
                font-size: 12px;
            }
            .el-form-item__label {
                line-height: 1;
            }
            .el-form-item__content{
                position: relative;

                .el-icon-ssq-guanbi1{
                    position: absolute;
                    right: -10px;
                    top: -10px;
                    color: $--color-text-regular;
                    font-size: 20px;
                    cursor: pointer;
                    [dir=rtl] & {
                        right: auto;
                        left: -10px;
                    }
                }
            }

            .rule-remind__operate_add{
                text-align: center;
                cursor: pointer;
                color: $--color-primary;

                .el-icon-ssq-jia{
                    margin-right: 3px;
                    font-size: 12px;
                    [dir=rtl] & {
                        margin-right: 0;
                        margin-left: 3px;
                    }
                }
            }
        }
    }
  .en-page  .set-label-rules__drawer{
        .rules-drawer__content{
            padding: 0 20px;
            .rule-put__input-days, .remind-day-input{
                &::after{
                    content: 'Days';
                    display: inline-block;
                    position: absolute;
                    right: 10px;
                    top: 0;
                    font-size: 12px;
                    color: $--color-text-primary;
                }
            }

        }
    }
</style>
