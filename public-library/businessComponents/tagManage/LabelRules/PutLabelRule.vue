<!-- 第一步：贴标签规则 -->
<template>
    <div>
        <div class="tip">{{ $t('TagManage.putLabelRules.tip') }}</div>
        <el-form>
            <el-radio-group v-model="putLabelTimeValue">
                <el-form-item v-for="(item, index) in putLabelTimes" :key="index">
                    <el-radio :label="item.type">
                        {{ item.text }}
                    </el-radio>
                    <el-input
                        v-if="index === inputVisibleIndex"
                        v-model.trim="item.value"
                        :placeholder="$t('TagManage.putLabelRules.inputDaysTip')"
                        class="rule-put__input-days"
                        @change="((val) => { handlePutInterval(index, val) })"
                        @blur="checkPutInterval"
                    ></el-input>
                </el-form-item>
            </el-radio-group>
        </el-form>
    </div>
</template>
<script>
// 手动触发的事件不展示自动触发天数的输入框
const FORBIDDENINPUTS = ['MANUAL'];
import cloneDeep from 'lodash/cloneDeep';
export default {
    props: {
        label: {
            type: Object,
            default: function() {
                return {};
            },
        },
        putType: {
            type: Array,
            default: () => {
                return [];
            },
        },
        putLabelTimeMap: {
            type: Array,
            default: () => {
                return [];
            },
        },
        setTimeMaxValue: {
            type: Number,
            default: 90,
        },
    },
    data() {
        return {
            defaultTimes: this.putLabelTimeMap,
            putLabelTimes: cloneDeep(this.putLabelTimeMap),
            putLabelTimeValue: this.label.stickRule.triggerEvent || 'MANUAL',
            putTypeMap: new Map(this.putType),
        };
    },
    computed: {
        // 当前可见的自动触发天数
        inputVisibleIndex() {
            if (FORBIDDENINPUTS.includes(this.putLabelTimeValue)) {
                return -1;
            }
            return this.putTypeMap.get(this.putLabelTimeValue);
        },
    },
    watch: {
        label: {
            handler(v) {
                // 赋值当前撕标签方式的自动触发天数
                this.putLabelTimes = cloneDeep(this.defaultTimes);
                this.putLabelTimes[this.putTypeMap.get(v.stickRule.triggerEvent)].value = v.stickRule.eventDelayDays;
                this.putLabelTimeValue = v.stickRule.triggerEvent;
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        handlePutInterval(index, val) {
            if (val < 0) {
                this.$nextTick(() => {
                    this.putLabelTimes[index].value = 0;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.minSet')}0${this.$t('TagManage.putLabelRules.day')}`);
            }
            if (val > this.setTimeMaxValue) {
                this.$nextTick(() => {
                    this.putLabelTimes[index].value = this.setTimeMaxValue;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.maxSet')}${this.setTimeMaxValue}${this.$t('TagManage.putLabelRules.day')}`);
            }
        },
        checkPutInterval() {
            if (this.putLabelTimes[this.inputVisibleIndex].value === '') {
                this.$nextTick(() => {
                    this.putLabelTimes[this.inputVisibleIndex].value = 0;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.minSet')}0${this.$t('TagManage.putLabelRules.day')}`);
            }
        },
        passStepData() {
            const obj = {
                triggerEvent: this.putLabelTimeValue,
                eventDelayDays: this.inputVisibleIndex === -1 ? 0 : this.putLabelTimes[this.inputVisibleIndex].value,
            };
            this.$emit('passData', 'stickRule', obj);
        },
    },
    deactivated() {
        this.passStepData();
    },
};
</script>
