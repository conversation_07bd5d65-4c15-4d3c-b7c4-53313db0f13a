<!-- 第二步：撕标签规则 -->
<template>
    <div>
        <div class="tip">{{ $t('TagManage.dropLabelRules.tip') }}</div>
        <el-form>
            <el-radio-group v-model="dropLabelTimeValue">
                <el-form-item v-for="(item, index) in dropLabelTimes" :key="index">
                    <el-radio :label="item.type">
                        {{ item.text }}
                    </el-radio>
                    <el-input
                        v-if="index === inputVisibleIndex"
                        v-model.trim="item.value"
                        :placeholder="$t('TagManage.putLabelRules.inputDaysTip')"
                        class="rule-put__input-days"
                        @change="((val) => { handlePutInterval(index, val) })"
                        @blur="checkPutInterval"
                    ></el-input>
                </el-form-item>
            </el-radio-group>
        </el-form>
    </div>
</template>
<script>
// 手动触发的事件不展示自动触发天数的输入框
const FORBIDDENINPUTS = ['MANUAL'];
import cloneDeep from 'lodash/cloneDeep';
export default {
    props: {
        label: {
            type: Object,
            default: function() {
                return {};
            },
        },
        dropType: {
            type: Array,
            default: () => {
                return [];
            },
        },
        dropLabelTimeMap: {
            type: Array,
            default: () => {
                return [];
            },
        },
        setTimeMaxValue: {
            type: Number,
            default: 90,
        },
    },
    data() {
        return {
            defaultTimes: this.dropLabelTimeMap,
            dropLabelTimes: cloneDeep(this.dropLabelTimeMap),
            dropLabelTimeValue: this.label.tearRule.triggerEvent || 'MANUAL',
            dropTypeMap: new Map(this.dropType),
        };
    },
    computed: {
        // 当前可见的自动触发天数
        inputVisibleIndex() {
            if (FORBIDDENINPUTS.includes(this.dropLabelTimeValue)) {
                return -1;
            }
            return this.dropTypeMap.get(this.dropLabelTimeValue);
        },
    },
    watch: {
        label: {
            handler(v) {
                // 赋值当前撕标签方式的自动触发天数
                this.dropLabelTimes = cloneDeep(this.defaultTimes);
                this.dropLabelTimes[this.dropTypeMap.get(v.tearRule.triggerEvent)].value = v.tearRule.eventDelayDays;
                this.dropLabelTimeValue = v.tearRule.triggerEvent;
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        handlePutInterval(index, val) {
            if (val < 0) {
                this.$nextTick(() => {
                    this.dropLabelTimes[index].value = 0;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.minSet')}0${this.$t('TagManage.putLabelRules.day')}`);
            }
            if (val > this.setTimeMaxValue) {
                this.$nextTick(() => {
                    this.dropLabelTimes[index].value = this.setTimeMaxValue;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.maxSet')}${this.setTimeMaxValue}${this.$t('TagManage.putLabelRules.day')}`);
            }
        },
        checkPutInterval() {
            if (this.dropLabelTimes[this.inputVisibleIndex].value === '') {
                this.$nextTick(() => {
                    this.dropLabelTimes[this.inputVisibleIndex].value = 0;
                });
                this.$MessageToast(`${this.$t('TagManage.putLabelRules.minSet')}0${this.$t('TagManage.putLabelRules.day')}`);
            }
        },
        passStepData() {
            const obj = {
                triggerEvent: this.dropLabelTimeValue,
                eventDelayDays: this.inputVisibleIndex === -1 ? 0 : this.dropLabelTimes[this.inputVisibleIndex].value,
            };
            this.$emit('passData', 'tearRule', obj);
        },
    },
    deactivated() {
        this.passStepData();
    },
};
</script>
