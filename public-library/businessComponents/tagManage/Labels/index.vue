<!-- 单个标签 -->
<template>
    <article class="label-manage-page__label">
        <div class="label-item__content" :style="{background: label.color}">
            <template v-if="!editing || forbidEditLabelName">
                <p class="label-item__name">
                    <template v-if="!isNeedDisableEdit && !forbidEditLabelName">
                        <el-tooltip class="item" effect="dark" :content="$t('TagManage.labelOperate.editLabel')" placement="top">
                            <span @click="triggerEdit">{{ cacheLabelName }}</span>
                        </el-tooltip>
                        <i class="label-item__edit-icon el-icon-ssq-bianjibia<PERSON>qian" @click="triggerEdit"></i>
                    </template>
                    <span class="el-tooltip" v-else>{{ cacheLabelName }}</span>
                </p>
            </template>
            <template v-else>
                <p class="label-item__name editing">
                    <el-input
                        class="label-item__name-edit"
                        v-model.trim="cacheLabelName"
                        type="text"
                        :autofocus="true"
                        :maxlength="15"
                        @blur="handleModifyLabelName"
                        ref="editingLabelName"
                    >
                    </el-input>
                </p>
            </template>
            <template v-if="!isNeedDisableEdit">
                <el-tooltip class="item" effect="dark" :content="$t('TagManage.labelOperate.setRule')" placement="top">
                    <p class="label-item__put-rule_set" @click="triggerOpenStep(1)">
                        <span>{{ putLabelTimes[label.stickRule.triggerEvent] }}</span>
                        <i class="el-icon-ssq-youjiantou1"></i>
                    </p>
                </el-tooltip>
                <i v-if="couldDel" @click="triggerDelete" class="label-item__del_icon el-icon-ssq-guanbi1"></i>
            </template>
            <p v-else class="label-item__put-rule_set">
                <span>{{ putLabelTimes[label.stickRule.triggerEvent] }}</span>
                <i class="el-icon-ssq-youjiantou1"></i>
            </p>
        </div>
        <footer class="label-item_footer">
            <template v-if="!isNeedDisableEdit">
                <el-tooltip class="item" effect="dark" :content="$t('TagManage.labelOperate.setPutRemind')" placement="bottom">
                    <p class="label-item__remind-rule_set" @click="triggerOpenStep(3)">
                        <span class="console-link">{{ label.notifyConfig.delayedDaysAfterSticking }}{{ $t('TagManage.labelOperate.afterDays') }}{{ $t('TagManage.labelOperate.remindPeople') }}</span>
                    </p>
                </el-tooltip>
                <el-tooltip class="item" effect="dark" :content="$t('TagManage.labelOperate.dropRule')" placement="bottom" v-if="!isEntDoc">
                    <p class="label-item__drop-rule_set">
                        <span>{{ dropLabelTimes[label.tearRule.triggerEvent] }}</span>
                        <span class="console-link" @click="triggerOpenStep(2)">{{ $t('TagManage.labelOperate.edit') }}</span>
                    </p>
                </el-tooltip>
            </template>
            <template v-else>
                <p class="label-item__remind-rule_set">
                    <span class="console-link">{{ label.notifyConfig.delayedDaysAfterSticking }}{{ $t('TagManage.labelOperate.afterDays') }}{{ $t('TagManage.labelOperate.remindPeople') }}</span>
                </p>
                <p class="label-item__drop-rule_set">
                    <span>{{ dropLabelTimes[label.tearRule.triggerEvent] }}</span>
                </p>
            </template>
            <p class="clear"></p>
        </footer>
    </article>
</template>
<script>

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['label', 'couldDel', 'index', 'putLabelTimeMap', 'dropLabelTimeMap', 'isNeedDisableEdit', 'forbidEditLabelName', 'isEntDoc'],
    data() {
        return {
            putLabelTimes: [],
            dropLabelTimes: this.dropLabelTimeMap,
            editing: false,
            cacheLabelName: '',
        };
    },
    computed: {
        labelNameChanged() {
            return this.cacheLabelName !== this.label.name;
        },
    },
    watch: {
        label: {
            handler(v) {
                (v.init === true) && (this.triggerEdit());
                this.cacheLabelName = v.name;
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        formatePutLabelTimeMap() {
            const newMap = {};
            this.putLabelTimeMap.map((item) => newMap[item.type] = item.text);
            return newMap;
        },
        setFocus() {
            const input = (this.$refs.editingLabelName).$el.getElementsByTagName('input')[0];
            input.focus();
        },
        triggerEdit() {
            this.editing = !this.editing;
            this.$nextTick(() => {
                (this.editing) && (this.setFocus());
            });
        },
        handleModifyLabelName() {
            // 这里触发一下 blur 告诉父组件当前操作的标签
            this.$emit('blur', this.label);

            if (this.cacheLabelName === '') {
                this.$MessageToast.info(this.$t('TagManage.labelOperate.labelNameRequire'));
                return false;
            }

            if (this.cacheLabelName.length > 15) {
                this.$MessageToast.info(this.$t('TagManage.labelOperate.labelNameLengthLimit'));
                return false;
            }

            this.triggerEdit();

            this.labelNameChanged && this.$emit('update', { ...this.label, ...{ name: this.cacheLabelName } });
        },
        triggerDelete() {
            this.$emit('delLabel', this.index, this.label);
        },
        triggerOpenStep(step) {
            this.$emit('openStep', step, this.label);
        },
    },
    mounted() {
        this.putLabelTimes = this.formatePutLabelTimeMap();
    },
};
</script>
<style lang="scss">
    .label-manage-page__label{
        float: left;
        width: 30%;
        min-width: 240px;
        margin-top: 20px;
        margin-right: 3%;
        [dir=rtl] & {
            float: right;
        }

        .label-item__content{
            position: relative;
            padding-left: 10px;
            padding-right: 20px;
            height: 40px;
            line-height: 40px;
            background: $--color-text-regular;
            color: $--color-white;
            border-radius: 2px;
            font-size: 14px;

            &::after{
                z-index: 1;
                position: absolute;
                right: -1px;
                top: 0;
                display: inline-block;
                width: 0;
                height: 0;
                border-left: 10px solid transparent;
                border-bottom: 20px solid transparent;
                border-right: 10px solid $--color-white;
                border-top: 20px solid transparent;
                content: '';
            }
            [dir=rtl] & {
                padding-left: 20px;
                padding-right: 10px;
                &::after{
                    right: auto;
                    left: -1px;
                    border-left-color: $--color-white;
                    border-right-color: transparent;
                }
            }
        }

        .label-item__name{
            font-size: 0;
            &:hover{
                .label-item__edit-icon{
                    display: inline-block;
                }
            }
            span.el-tooltip{
                font-size: 14px;
            }
        }

        .label-item__edit-icon{
            display: none;
            padding-left: 7px;
            cursor: text;
            font-size: 14px;
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 7px;
            }
        }

        .label-item__name-edit{
            width: 180px;
            .el-input__inner{
                padding-left: 0;
                background: none;
                color: $--color-white;
                border: none;

                &:hover, &:focus{
                    box-shadow: none;
                }
            }
        }

        .label-item__put-rule_set{
            position: absolute;
            right: 20px;
            top: 0;
            cursor: pointer;
            font-size: 12px;
            [dir=rtl] & {
                right: auto;
                left: 20px;
                i {
                    transform: rotate(180deg);
                }
            }
        }

        .label-item__del_icon{
            z-index: 2;
            position: absolute;
            right: -10px;
            top: -10px;
            font-size: 20px;
            color: $--color-text-regular;
            cursor: pointer;
            [dir=rtl] & {
                right: auto;
                left: -10px;
            }
        }

        .label-item_footer{
            margin-top: 5px;
            padding-left: 10px;
            padding-right: 20px;
            line-height: 16px;
            font-size: 12px;
            [dir=rtl] & {
                padding-left: 20px;
                padding-right: 10px;
            }
        }

        .label-item__remind-rule_set{
            float: left;
            [dir=rtl] & {
                float: right;
            }
            span {
                color: $--link-hover-color;
                cursor: pointer;
                &:hover{
                    color: $--color-primary-light-2;
                }
            }
        }

        .label-item__drop-rule_set{
            float: right;
            color: #9DA1A4;
            [dir=rtl] & {
                float: left;
            }

            .console-link{
                margin-left: 4px;
                color: $--link-hover-color;
                cursor: pointer;
                [dir=rtl] & {
                    margin-left: 0;
                    margin-right: 4px;
                }
                &:hover{
                    color: $--color-primary-light-2;
                }
            }
        }

        &:last-child{
            margin-right: 0;
            [dir=rtl] &{
                margin-left: 0;
                margin-right: 3%;
            }
        }
    }
</style>
