<!-- 业务组件：选择业务线弹窗 -->
<!-- 引用位置  登录注册模块-签署中转页面：/signing/transition，加入企业弹窗：dialogApplyJoinEnt-->
<template>
    <div class="box-sizing-dialog">
        <el-dialog
            :title="$t('common.selectBusLine')"
            :visible.sync="visible"
            :before-close="handleClose"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            size="tiny"
            class="select-biz-line-dialog"
        >
            <div class="select-biz-list">
                <el-form
                    :model="selectLine"
                >
                    <el-radio-group v-model="selectLine.value">
                        <el-form-item v-for="(line, index) in bizLineList" :key="index">
                            <el-radio :label="line.entId">{{ line.bizLineEntName }}</el-radio>
                        </el-form-item>
                    </el-radio-group>
                </el-form>
            </div>
            <span slot="footer" class="dialog-footer">
                <el-button @click="handleClose">{{ $t('commonHeader.cancel') }}</el-button>
                <el-button type="primary" @click="handleChoose">{{ $t('commonHeader.confirm') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>
<script>
export default {
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        bizLineList: {
            type: Array,
            default: () => [],
        },
        selectedLineEntId: {
            type: String,
            default: '',
        },
        callBack: {
            type: Function,
            default: () => () => {},
        },
    },
    data() {
        return {
            selectLine: {
                value: '',
            },
        };
    },
    watch: {
        bizLineList(val) {
            this.selectLine.value = val[0].entId || '';
        },
    },
    methods: {
        handleChoose() {
            this.$emit('update:selectedLineEntId', this.selectLine.value);
            this.callBack && this.callBack();
            this.handleClose();
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
};
</script>
<style lang="scss">
    .select-biz-line-dialog{
        .el-dialog{
            width: 370px;
            .el-dialog_header {
                line-height: 37px;
            }
        }
        @media (max-width: 768px) {
            .el-dialog{
                width: 90%;
            }
        }
        .select-biz-list .el-form-item{
            margin-bottom: 0;

            .el-radio__label{
                display: inline-block;
                line-height: 20px;
                word-break: break-all;
                white-space: normal;
                vertical-align: middle;
            }
        }
    }
</style>
