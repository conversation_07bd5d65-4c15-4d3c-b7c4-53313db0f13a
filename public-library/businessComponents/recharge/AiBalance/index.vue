<template>
    <div class="ai-balance-con">
        <div class="ai-title">
            <h3>{{ $t('recharge.aiRecharge') }}</h3>
        </div>
        <div class="ai-balance-content-main">
            <div class="ai-content-main__items">
                <div class="item-left">
                    <div class="recharg-page-icon__wrap"><i class="ai-page-icon el-icon-ssq-fengxianpanduan" /></div>
                    <div class="ai-subtitle member-balance">
                        <span>{{ $t('recharge.riskJudge') }}</span>
                    </div>
                </div>
                <div class="item-right">
                    <div class="right__part">
                        <p>{{ totalProductBalanceCount }}</p>
                        <p>{{ $t('recharge.useCopies') }}</p>
                    </div>
                </div>
            </div>
            <div class="ai-item-actions">
                <el-button class="btn-type-one" @click="goToRecharge"><!-- 充值 -->{{ $t('recharge.recharge') }}</el-button>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        isEnterprise: {
            type: Boolean,
            default: false,
        },
        pageFrom: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            totalProductBalanceCount: 0,
        };
    },
    methods: {
        goToRecharge() {
            this.$router.push(`recharge/selectpackage?packageType=ai&pageFrom=${this.pageFrom}`);
        },
    },
    created: function() {
        // 查询余额
        this.$http.get('/web/hubble/users/tool/overview-plan-detail?toolType=协议风险判断')
            .then(res => {
                const resData = res.data;
                if (resData && resData.length > 0) {
                    this.totalProductBalanceCount =  resData.map(person => person.productBalanceCount)
                        .reduce((acc, age) => acc + age, 0);
                }
            });
    },
};
</script>

<style lang="scss">
.ai-balance-con{
    .ai-title{
        height: 60px;
        h3 {
            width: 100%;
            height: 60px;
            line-height: 60px;
            font-size: 14px;
            color: $--color-text-primary;
            padding-left: 20px;
            font-weight: 500;
        }
    }
    .ai-balance-content-main{
        width: 100%;
        height: 82px;
        padding-left: 30px;
        border: 1px solid $--border-color-lighter;
        border-left: none;
        border-right: none;
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: flex-start;
        box-sizing: border-box;
        .recharg-page-icon__wrap {
            background-color: #f3c51e;
            width: 48px;
            height: 48px;
            border-radius:100%;
        }
        .ai-content-main__items{
            flex: 1;
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            justify-content: space-between;
            .item-left{
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: space-between;
                .ai-subtitle{
                    font-size: 14px;
                    color: $--color-text-primary;
                    letter-spacing: 0;
                    line-height: 20px;
                    font-weight: 400;
                    margin-left: 12px;
                    flex-shrink: 0;

                    &>p:nth-child(2) {
                        color: $--color-text-regular;
                        font-size: 12px;
                        margin-top: 5px;
                    }
                }
                .ai-page-icon{
                    font-size: 26px;
                    padding-top: 11px;
                    padding-left: 11px;
                    color: #fff;
                }
            }
            .item-right{
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: space-around;
                .right__part{
                    min-width: 100px;
                    max-width: 130px;
                    border-left: 1px solid #eee;
                    border-right: 1px solid #eee;
                    padding-top: 10px;
                    padding-bottom: 10px;
                    text-align: center;
                    display: flex;
                    flex-direction: column;
                    align-items: center;
                    justify-content: space-between;

                    &>p:nth-child(1) {
                        color: $--color-primary;
                        font-size: 16px;
                    }

                    &>p:nth-child(2) {
                        color: #999;
                        margin-top: 5px;
                        font-size: 12px;
                    }

                    &:nth-last-child(1) {
                        margin-right: 10px;
                    }

                }
            }
        }
        .ai-item-actions{
            width: 15%;
            text-align: left;

            .el-button {
                width: 100px;
            }
        }
    }
}

</style>
