<template>
    <div class="usage-record-container">
        <div class="usage-record">
            <el-table
                class="ssq-table"
                key="usage-record"
                :data="usageRecordData"
                highlight-current-row
                style="width: 100%"
                v-loading="loadingStatus"
                v-autoH:table="{
                    container: $el,
                    bottom: 29,
                }"
            >
                <el-table-column
                    class-name="usage-record-create-time"
                    prop="applicationId"
                    label="工单号"
                >
                </el-table-column>

                <el-table-column
                    prop="type"
                    label="出证项目"
                >
                    <template slot-scope="scope">
                        {{ scope.row.type === 1 ? '上上签电子签约证据报告' : '公证书' }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="applicantName"
                    label="申请人"
                >
                </el-table-column>
                <el-table-column
                    prop="applicantMobile"
                    label="联系方式"
                >
                </el-table-column>
                <el-table-column
                    prop="submitTime"
                    label="申请时间"
                >
                    <template slot-scope="scope">
                        {{ getTime(scope.row.submitTime) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="status"
                    label="状态"
                    class-name="status-coloum"
                >
                    <template slot-scope="scope">
                        {{ getStatusText(scope.row.status) }}
                    </template>
                </el-table-column>
                <el-table-column
                    prop="opetare"
                    label="操作"
                >
                    <template slot-scope="scope">
                        <a target="_blank" :href="`/converge/evidence/saas/detail/${scope.row.applicationId}`">查看</a>
                        <template v-if="scope.row.status === 3 && scope.row.type === 1">
                            <span class="sperate-line">|</span>
                            <a :href="`/octopus-api/notary/application/${scope.row.applicationId}/download?access_token=${accessToken}`" target="_self">下载</a>
                        </template>
                        <template v-else-if="scope.row.status === -1"> <span class="sperate-line">|</span>
                            <span class="pay-item" @click="handlePayClick(scope.row.orderId)">付款</span>
                        </template>

                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="contacts-pagination-absolute-bottom">
            <el-pagination
                @size-change="handleUsageRecordSizeChange"
                @current-change="handleUsageRecordCurrentChange"
                :current-page="usageRecordPageData.currentPage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="usageRecordPageData.currentPageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="usageRecordPageData.total"
                class="console-members-pagination"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { formatDateToString } from 'pub-utils/date.js';
import cookie from 'vue-cookie';

export default {
    props: {
        isEnterprise: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            usageRecordData: [],
            usageRecordPageData: { // 分页数据
                total: 0,
                currentPageSize: 10,
                currentPage: 1,
            },

            loadingStatus: false,
            rangeTime: [],
            exportLoading: false,
        };
    },
    computed: {
        ...mapGetters([
            'isNewGroupPath',
        ]),
        isZh() {
            return this.$i18n.locale === 'zh';
        },
        accessToken() {
            return cookie.get('access_token') === null ? '' : cookie.get('access_token');
        },
    },
    methods: {
        handlePayClick(id) {
            const path = `${this.isEnterprise ? '' : '/usercenter/recharge/'}pay?id=${id}`;
            this.$router.push(path);
        },
        getTime(time) {
            return  formatDateToString({
                date: time,
                format: 'YYYY-MM-DD hh:mm:ss',
            });
        },
        getStatusText(status) {
            const map = {
                '-1': '待付款',
                0: '审核中',
                1: '已受理',
                2: '拒绝受理',
                3: '已办结',
                4: '已评论',
            };
            return map[status];
        },
        getUsageRecordData() {
            this.loadingStatus = true;
            // 获取使用记录
            this.$http.get('/octopus-api/notary/application/paging', { params: {
                pageNum: this.usageRecordPageData.currentPage,
                pageSize: this.usageRecordPageData.currentPageSize,
            } }).then(res => {
                if (res.data && res.data.records) {
                    this.usageRecordData = res.data.records;
                    this.usageRecordPageData.total = parseInt(res.data.total);
                }
                this.loadingStatus = false;
            }).catch(() => {
                this.loadingStatus = false;
            });
        },
        handleUsageRecordSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.usageRecordPageData.currentPageSize = val;
            this.getUsageRecordData();
        },
        handleUsageRecordCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.usageRecordPageData.currentPage = val;
            this.getUsageRecordData();
        },
    },
    created() {
        this.getUsageRecordData();
    },
};
</script>

<style lang="scss">

.en-page .usage-record-container .usage-record .el-table .el-table__header-wrapper tr th .cell {
    word-break: break-word;
    white-space: normal;

}
.usage-record-container {
    .pay-item{
        cursor: pointer;
        color: $--color-primary;
    }
    .sperate-line{
        padding: 0 5px;
    }
    .usage-record .el-table {
        .cell {
            padding-left: 10px;
            padding-right: 0;
        }

        .usage-record-create-time .cell {
            padding-left: 20px;
        }

        .el-table__header-wrapper tr th{
            height: 43px;
            .cell{
                color: $--color-text-primary;
            }
        }
        .el-table__body-wrapper .el-table__row td{
            box-sizing: content-box;
            padding-top: 3px;
            padding-bottom: 3px;
        }
    }
    .recharge-con .enterprise-recharge-table-con{
        .el-table {
            border-top: none;
        }
    }

}
</style>
