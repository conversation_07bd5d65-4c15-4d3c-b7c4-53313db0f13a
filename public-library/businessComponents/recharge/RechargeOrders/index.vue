<!-- 充值订单、使用记录表格 -->
<template>
    <div class="recharge-table-con recharge-table-con-h">
        <ul class="nav-list clear">
            <li class="cursor-point fl" :class="{active : componentName === 'OrdersTable'}" @click="switchNav('OrdersTable')"><!-- 我的订单 -->{{ $t('recharge.myOrder') }}</li>
            <!-- <li class="cursor-point fl" :class="{active : componentName === 'ApplyEvidenceTable'}" @click="switchNav('ApplyEvidenceTable')">出证申请列表</li> -->
            <li class="cursor-point fl" :class="{active : componentName === 'RecordsTable'}" @click="switchNav('RecordsTable')"><!-- 使用记录 -->{{ $t('recharge.useRecord') }}</li>
        </ul>
        <components :is="componentName"></components>
    </div>
</template>
<script>
import OrdersTable from './OrdersTable';
import RecordsTable from './RecordsTable';
import ApplyEvidenceTable from './ApplyEvidenceTable';
export default {
    components: {
        OrdersTable,
        RecordsTable,
        ApplyEvidenceTable,
    },
    data() {
        return {
            componentName: 'OrdersTable',
        };
    },
    methods: {
        switchNav(name) {
            this.componentName = name;
        },
    },
};
</script>
<style lang="scss">
.recharge-con {
	.recharge-table-con {
		position: relative;
		width: 100%;
		margin-top: 20px;

		&-h {
			height: calc(100% - 324px);
		}

		tbody {
			color: $--color-text-regular;
			.payStatus {
				&.no {
					color: $--color-danger;
					cursor: pointer;
				}
			}
			.billStatus {
				&.no {
					cursor: pointer;
					color: $--color-primary-light-1;
				}

				.el-icon-ssq-warm-filling {
					margin-left: -14px;
					color: $--color-danger;
				}
			}
		}
		.nav-list {
			margin-left: 2px;
			li {
				width: 102px;
				height: 42px;
				line-height: 42px;
				text-align: center;
				&.active {
					color: $--color-primary-light-1;
					border-bottom: 2px solid $--color-primary-light-1;
				}
			}
		}
		.my-order,
		.usage-record {
			.el-table__body-wrapper {
				width: 100%;
			}

			&-container {
				height: 100%;
			}

			.el-table {
				min-height: 200px;
			}

			.el-table__body-wrapper {
				min-height: 159px;
				height: 159px;
			}
		}

		.el-table {
			.el-table__body-wrapper > table {
				width: 100%;
				overflow-y: auto;
				overflow-x: hidden;
			}
		}
		.el-table th,
		.el-table__footer-wrapper thead div,
		.el-table__header-wrapper thead div {
			background-color: $--background-color-regular;
		}
		.el-table--enable-row-hover .el-table__body tr:hover > td {
			background-color: $--background-color-secondary;
			background-clip: padding-box;
		}
	}

	.el-dialog__body {
		p {
			font-size: 14px;
		}
	}
	.contacts-pagination-absolute-bottom {
		background: $--color-white;
		.el-pagination {
			padding: 10px 20px 10px 0;
			border-top: 1px solid $--border-color-lighter;
			box-shadow: 1px 0px 0px 0px $--border-color-lighter;
			text-align: right;
		}
	}
}
</style>
