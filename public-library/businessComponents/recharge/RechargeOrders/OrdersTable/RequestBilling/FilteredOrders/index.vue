<!-- 可开票订单弹框内的表格 -->
<template>
    <el-table
        class="ssq-table request-billing-dialog__ordersTable"
        :data="ableList"
        highlight-current-row
    >
        <el-table-column
            prop="orderId"
            :label="$t('recharge.orderMap.num')"
            width="210"
            class-name="first-column"
        >
        </el-table-column>
        <el-table-column
            prop="packageName"
            :label="$t('recharge.orderMap.setMeal')"
        >
        </el-table-column>
        <el-table-column
            prop="numType"
            :label="$t('recharge.orderMap.amountAndType')"
        >
        </el-table-column>
        <el-table-column
            prop="rechargeMoney"
            :label="getIsUae ? $t('recharge.orderMap.amountYuanUae') : $t('recharge.orderMap.amountYuan')"
        >
        </el-table-column>
    </el-table>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['ableList'],
    data() {
        return {};
    },
    computed: {
        ...mapGetters(['getIsUae']),
    },
    methods: {},
};
</script>
<style lang="scss">
    .request-billing-dialog .request-billing-dialog__ordersTable.el-table{
        margin: -21px 0 105px -33px;
        width: calc(100% + 66px);
        max-width: none;

        .first-column .cell{
            padding-left: 33px;
        }
        .el-table__header-wrapper{
            border-top: 1px solid $--border-color-lighter;
            tr th {
                height: 43px;
                .cell{
                    color: $--color-text-primary;
                }
            }
        }
        .el-table__body-wrapper .el-table__row td{height: 47px;}
    }
</style>
