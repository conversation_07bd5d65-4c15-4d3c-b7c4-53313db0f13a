<!-- 个人开票表单 -->
<template>
    <el-form :model="formData" :rules="rules" ref="billingSubmitForm" label-width="80px" class="billing-submit-form personal-billing-submit">
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.name')" prop="userName">
            <el-input :disabled="true" type="text" v-model="formData.userName" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.amount')" prop="invoiceValue" class="amount-item">
            <span class="unit">¥</span>
            <el-input :disabled="true" type="text" v-model="formData.invoiceValue" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.inbox')" prop="customEmail">
            <el-input v-model="formData.customEmail"></el-input>
        </el-form-item>
    </el-form>
</template>
<script>
import i18n from 'src/lang';

export default {
    // eslint-disable-next-line vue/require-prop-types
    props: ['ordersInfo', 'defaultParams'],
    data() {
        return {
            formData: {
                userName: '',
                invoiceValue: '',
                customEmail: '',
                billingType: '1',
            },
            rules: {
                userName: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputName'), trigger: 'blur' }, // 请输入姓名
                ],
                customEmail: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputInbox'), trigger: 'blur' }, // 请输入收件箱
                    { type: 'email', message: i18n.t('recharge.entNomalInvoiceMap.plsInputEmail'), trigger: 'blur' }, // 请输入正确的邮箱地址
                ],
            },
        };
    },
    watch: {
        ordersInfo: {
            handler(val) {
                this.formData.userName = val.userName;
                this.formData.invoiceValue = val.invoiceValue;
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        submitBilling() {
            return new Promise((resolve) => {
                this.$refs['billingSubmitForm'].validate((valid) => {
                    if (valid) {
                        this.$http.post('/ents/charging/bill', {
                            ...this.defaultParams,
                            ...this.formData,
                        }).then(() => {
                            resolve(true);
                        }).catch(() => {
                            resolve(false);
                        });
                    } else {
                        resolve(false);
                    }
                });
            });
        },
    },
};
</script>
<style lang="scss">

</style>
