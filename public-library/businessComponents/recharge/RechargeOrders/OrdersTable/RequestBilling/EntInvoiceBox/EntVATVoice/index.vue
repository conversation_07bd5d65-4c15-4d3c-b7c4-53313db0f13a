<!-- 企业申请专票表单 -->
<template>
    <el-form :model="formData" :rules="rules" ref="billingSubmitForm" label-width="100px" class="billing-submit-form">
        <el-form-item label=" " class="form-tip-item">
            <!-- 增值税普通发票是以电子发票形式发送至邮箱 -->{{ $t('recharge.entSpecialInvoiceMap.mailTip') }}
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.entName')" prop="entName">
            <el-input :disabled="true" type="text" v-model="formData.entName" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.taxNum')" prop="taxCode">
            <el-input type="text" v-model="formData.taxCode" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.entAddress')" prop="customAddress">
            <el-input type="text" v-model="formData.customAddress"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.entTel')" prop="customMobile">
            <el-input type="text" v-model="formData.customMobile"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.accountBank')" prop="bankName">
            <el-input type="text" v-model="formData.bankName"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.bankAccount')" prop="bankAccount">
            <el-input type="text" v-model="formData.bankAccount"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.invoiceAmout')" prop="invoiceValue" class="amount-item">
            <span class="unit">¥</span>
            <el-input :disabled="true" type="text" v-model="formData.invoiceValue" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.tip2')" prop="fileId" class="upload-item">
            <el-upload
                :headers="uploadHeaders"
                accept=""
                action="/ents/charging/bill/pic"
                :file-list="fileList"
                :show-file-list="true"
                :on-preview="handlePreview"
                :before-upload="beforeUpload"
                :on-progress="uploading"
                :on-success="uploadSuccess"
                :on-error="uploadError"
                :on-remove="uploadRemove"
                ref="elUpload"
            >
                <div>
                    <el-button class="upload-btn" type="primary" plain :disabled="formData.fileId != null || uploadStatus > 0">+ <!-- 点击上传 -->{{ $t('recharge.entSpecialInvoiceMap.clickUpload') }}</el-button>
                </div>
            </el-upload>
        </el-form-item>
        <!-- <el-form-item label="" class="form-tip-item">
            填写收件信息，向您邮寄发票{{ $t('recharge.entSpecialInvoiceMap.tip3') }}
        </el-form-item> -->
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.inbox')" prop="customEmail">
            <el-input type="text" v-model="formData.customEmail"></el-input>
        </el-form-item>
        <!-- <el-form-item :label="$t('recharge.entSpecialInvoiceMap.recipientName')" prop="addresseeName">
            <el-input type="text" v-model="formData.addresseeName" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.recipientPhone')" prop="addresseeMobile">
            <el-input type="text" v-model="formData.addresseeMobile" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entSpecialInvoiceMap.recipientAddress')" prop="addresseeAddress">
            <el-input type="text" v-model="formData.addresseeAddress" auto-complete="off"></el-input>
        </el-form-item> -->
        <!-- <div class="billing-submit-form-tip">
            <el-checkbox v-model="isAgreeTip">
            </el-checkbox>
            {{ $t('recharge.entSpecialInvoiceMap.tip1') }}
        </div> -->
        <!-- 预览图片 -->
        <Preview
            v-model="dialogVisible"
            :title="$t('recharge.entSpecialInvoiceMap.certifyProvePic')"
        >
            <div slot="previewContent"><img class="preview-image" :src="dialogImageUrl" alt=""></div>
        </Preview>
    </el-form>
</template>
<script>
import i18n from 'src/lang';
import Preview from 'pub-components/preview';

export default {
    components: {
        Preview,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['ordersInfo', 'defaultParams', 'invoiceBillData'],
    data() {
        return {
            formData: {
                entName: '',
                customMobile: '',
                invoiceValue: '',
                billingType: '4',
                fileId: null,
                customEmail: '', // 收件箱
                taxCode: '',
                customAddress: '',
                bankName: '',
                bankAccount: '',
            },
            // isAgreeTip: false, // 是否同意 '温馨提示: 600元以下发票，以到付形式邮寄'
            rules: {
                entName: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputEntName'), trigger: 'blur' }, // 请输入公司名称
                ],
                taxCode: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputTaxNum'), trigger: 'blur' }, // 请输入税号
                ],
                customAddress: [
                    { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputEntAddress'), trigger: 'blur' }, // 请输入公司地址
                ],
                customMobile: [
                    { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputEntPhone'), trigger: 'blur' }, // 请输入公司电话
                ],
                bankName: [
                    { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputBankName'), trigger: 'blur' }, // 请输入开户行
                ],
                bankAccount: [
                    { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputBankAccount'), trigger: 'blur' }, // 请输入开户账号
                ],
                fileId: [
                    { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsUploadCertificate'), trigger: 'submit' }, // 请上传一般纳税人证明（截图或文件）
                ],
                // addresseeName: [
                //     { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputRecipientName'), trigger: 'blur' }, // 请输入收件人姓名
                // ],
                // addresseeMobile: [
                //     { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputRecipientPhone'), trigger: 'blur' }, // 请输入收件人电话
                // ],
                // addresseeAddress: [
                //     { required: true, message: i18n.t('recharge.entSpecialInvoiceMap.plsInputRecipientAddress'), trigger: 'blur' }, // 请输入收件人地址
                // ],
                customEmail: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputInbox'), trigger: 'blur' }, // 请输入收件箱
                    { type: 'email', message: i18n.t('recharge.entNomalInvoiceMap.plsInputEmail'), trigger: 'blur' }, // 请输入正确的邮箱地址
                ],
            },
            uploadHeaders: {
                Authorization: `bearer ${this.$cookie.get('access_token')}`,
            },
            uploadStatus: 0,
            fileList: [],
            dialogVisible: false,
            dialogImageUrl: '',
        };
    },
    computed: {
    },
    watch: {
        ordersInfo: {
            handler(val) {
                this.formData.entName = val.entName;
                this.formData.invoiceValue = val.invoiceValue;
            },
            deep: true,
            immediate: true,
        },
        invoiceBillData: {
            handler(val) {
                if (val) {
                    this.formData.customMobile = val.customMobile;
                    this.formData.customEmail = val.customEmail;
                    this.formData.taxCode = val.taxCode;
                    this.formData.customAddress = val.customAddress;
                    this.formData.bankName = val.bankName;
                    this.formData.bankAccount = val.bankAccount;
                    if (val.fileId) {
                        this.formData.fileId = val.fileId;
                        const hostName = `${window.location.protocol}//${window.location.host}`;
                        this.fileList = [{ name: val.fileName || 'taxpayer_certificate.jpg', url: `${hostName}/ents/charging/bill/file?billingRecordId=${val.billingRecordId}&access_token=${Vue.$cookie.get('access_token')}` }];
                    }
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        handlePreview(file) {
            if (file.url) {
                this.dialogImageUrl = file.url;
            } else {
                this.dialogImageUrl = URL.createObjectURL(file.raw);
            }
            this.dialogVisible = true;
        },
        // 检测图片格式
        checkImgFormat(file) {
            const checkSize = file.size / 1024 / 1024 < 5;
            const acceptType = ['image/png', 'image/jpeg', 'image/jpg'];
            const checkFormat = acceptType.some((type) => {
                return file.type === type;
            });

            // 判断图片大小
            if (!checkSize) {
                this.$MessageToast.error(this.$t('recharge.entSpecialInvoiceMap.picNotExceetTip')); // 图片大小不能超过5M
            }

            // 判断图片格式
            if (!checkFormat) {
                this.$MessageToast.error(this.$t('recharge.entSpecialInvoiceMap.picNotMeetRequire')); // 图片格式不符合要求
            }

            return checkSize && checkFormat;
        },
        // 上传前校验
        beforeUpload(file) {
            const checkFormat = this.checkImgFormat(file);
            return checkFormat;
        },
        uploading() {
            this.uploadStatus = 1;
        },
        uploadSuccess(res) {
            this.formData.fileId = res.value;
            this.$refs['billingSubmitForm'].validateField('fileId');
            this.uploadStatus = 2;
        },
        uploadError(err) {
            const errMes = (err.response && err.response.data.message) ? err.response.data.message : this.$t('recharge.errorTip'); // 出错啦
            this.uploadStatus = 0;
            this.$MessageToast.error(errMes);
        },
        uploadRemove() {
            this.formData.fileId = null;
            this.uploadStatus = 0;
        },
        submitBilling() {
            return new Promise((resolve) => {
                this.$refs['billingSubmitForm'].validate((valid) => {
                    // if (!this.isAgreeTip) {
                    //     this.$MessageToast.info(this.$t('recharge.entSpecialInvoiceMap.plsAgreeTip'));
                    //     return;
                    // }
                    if (valid) {
                        this.$http.post('/ents/charging/bill', {
                            ...this.defaultParams,
                            ...this.formData,
                        }).then(() => {
                            resolve(true);
                        }).catch(() => {
                            resolve(false);
                        });
                    } else {
                        resolve(false);
                    }
                });
            });
        },
    },
};
</script>
<style lang="scss">
.billing-submit-form-tip {
    margin-bottom: 20px;
    text-align: center;
    color: $--color-danger;
}
.preview-image {
    max-width: 100%;
    max-height: 100%;
}
.upload-btn{
    &:focus,&:hover,&:visited{
        color: $--color-primary !important;
    }
}
.upload-btn.is-disabled{
    &:focus,&:hover,&:visited{
        background-color: #F8F8F8 !important;
        color: #71b2e4 !important;
    }
}
</style>
