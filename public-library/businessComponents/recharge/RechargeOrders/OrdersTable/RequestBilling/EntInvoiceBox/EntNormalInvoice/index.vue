<!-- 企业申请普票表单 -->
<template>
    <el-form :model="formData" :rules="rules" ref="billingSubmitForm" label-width="90px" class="billing-submit-form">
        <el-form-item label=" " class="form-tip-item">
            <!-- 增值税普通发票是以电子发票形式发送至邮箱 -->{{ $t('recharge.entNomalInvoiceMap.tip1') }}
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.entName')" prop="entName">
            <el-input :disabled="true" type="text" v-model="formData.entName" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.taxNum')" prop="taxCode">
            <el-input type="text" v-model="formData.taxCode" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.amount')" prop="invoiceValue" class="amount-item">
            <span class="unit">¥</span>
            <el-input :disabled="true" type="text" v-model="formData.invoiceValue" auto-complete="off"></el-input>
        </el-form-item>
        <el-form-item :label="$t('recharge.entNomalInvoiceMap.inbox')" prop="customEmail">
            <el-input v-model="formData.customEmail"></el-input>
        </el-form-item>
    </el-form>
</template>
<script>
import i18n from 'src/lang';

export default {
    // eslint-disable-next-line
    props: ['ordersInfo', 'defaultParams', 'invoiceBillData'],
    data() {
        return {
            formData: {
                entName: '', // 公司名称
                invoiceValue: '', // 开票金额
                customEmail: '', // 收件箱
                billingType: '3', // 1个人电子，2个人邮寄，3企业电子，4企业邮寄
                taxCode: null, // 税号
            },
            rules: {
                entName: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputEntName'), trigger: 'blur' }, // 请输入公司名称
                ],
                customEmail: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputInbox'), trigger: 'blur' }, // 请输入收件箱
                    { type: 'email', message: i18n.t('recharge.entNomalInvoiceMap.plsInputEmail'), trigger: 'blur' }, // 请输入正确的邮箱地址
                ],
                taxCode: [
                    { required: true, message: i18n.t('recharge.entNomalInvoiceMap.plsInputTaxNum'), trigger: 'blur' }, // 请输入税号
                ],
            },
        };
    },
    watch: {
        ordersInfo: {
            handler(val) {
                this.formData.entName = val.entName;
                this.formData.invoiceValue = val.invoiceValue;
            },
            deep: true,
            immediate: true,
        },
        invoiceBillData: {
            handler(val) {
                if (val) {
                    this.formData.customEmail = val.customEmail;
                    this.formData.taxCode = val.taxCode;
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        submitBilling() {
            return new Promise((resolve) => {
                this.$refs['billingSubmitForm'].validate((valid) => {
                    if (valid) {
                        this.$http.post('/ents/charging/bill', {
                            ...this.defaultParams,
                            ...this.formData,
                        }).then(() => {
                            resolve(true);
                        }).catch(() => {
                            resolve(false);
                        });
                    } else {
                        resolve(false);
                    }
                });
            });
        },
    },
};
</script>
<style lang="scss">

</style>
