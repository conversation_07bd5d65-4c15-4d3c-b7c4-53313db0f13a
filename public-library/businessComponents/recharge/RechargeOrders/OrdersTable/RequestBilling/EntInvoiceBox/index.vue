<!-- 企业开票普票、专票切换 -->
<template>
    <div class="invoiceBox ent-invoiceBox">
        <div class="invoice-type-switch">
            <span :class="{active: currentType === 'normal'}" @click="switchType('normal')"><!-- 增值税普通发票 -->{{ $t('recharge.addNomalInvoice') }}</span>
            <span :class="{active: currentType === 'VAT'}" @click="switchType('VAT')"><!-- 增值税专用发票 -->{{ $t('recharge.addSpecialInvoice') }}</span>
        </div>
        <EntNormalInvoice v-if="currentType === 'normal'" :ordersInfo="ordersInfo" :invoiceBillData="invoiceBillData" :defaultParams="defaultParams" ref="EntNormalInvoice"></EntNormalInvoice>
        <EntVATVoice v-else :ordersInfo="ordersInfo" :invoiceBillData="invoiceBillData" :defaultParams="defaultParams" ref="EntVATVoice"></EntVATVoice>
    </div>
</template>
<script>
import EntNormalInvoice from './EntNormalInvoice';
import EntVATVoice from './EntVATVoice';
export default {
    components: {
        EntNormalInvoice,
        EntVATVoice,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['ordersInfo', 'defaultParams'],
    data() {
        return {
            currentType: 'normal',
            invoiceBillData: {},
        };
    },
    computed: {
        curComponent() {
            return this.currentType === 'normal' ? 'EntNormalInvoice' : 'EntVATVoice';
        },
    },
    methods: {
        switchType(type) {
            this.currentType = type;
        },
        submitBilling() {
            return new Promise((resolve) => {
                this.$refs[this.curComponent].submitBilling().then((res) => {
                    resolve(res);
                });
            });
        },
    },
    mounted() {
        this.$http.get(`/ents/charging/bill/history`)
            .then(res => {
                this.invoiceBillData = res.data;
            });
    },
};
</script>
<style lang="scss">
    .ent-invoiceBox{
        width: calc(100% + 66px);
        margin-left: -33px;
        margin-top: -33px;

        .invoice-type-switch{
            padding-left: 33px;
            background: $--color-white;
            border-bottom: 1px solid $--border-color-lighter;
            span{
                display: inline-block;
                margin-right: 15px;
                margin-bottom: -1px;
                padding-bottom: 7px;
                font-size: 12px;
                border-bottom: 1px solid $--border-color-lighter;
                cursor: pointer;

                &.active{
                    margin-bottom: -2px;
                    border-bottom: 2px solid $--color-primary;
                    color: $--color-primary;
                }
            }
        }
    }
</style>
