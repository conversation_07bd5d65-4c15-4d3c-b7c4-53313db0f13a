<!-- 申请开票弹窗 -->
<template>
    <el-dialog
        id="request-billing-dialog"
        class="request-billing-dialog"
        :visible.sync="billingDialogVisible"
        size="small"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <div slot="title">
            <template v-if="hasDisable && componentName === firstStep">
                <div class="slot-operate-title">
                    <!-- 以下订单支持批量申请开票 -->{{ $t('recharge.supportBatchInvoiceTip') }}
                    <el-button class="request-billing-dialog__title_btn" @click="componentName = secondStep"><!-- 批量申请 -->{{ $t('recharge.batchApply') }}</el-button>
                </div>
            </template>
            <template v-else><div class="slot-title"><!-- 申请开票 -->{{ $t('recharge.billStatusMap.status1') }}<span class="slot-title-tips"><!-- （上上签收到后会在7个工作日内处理完成） -->{{ $t('recharge.ssqDealTip') }}</span></div></template>
        </div>
        <component
            :is="componentName"
            :ableList="ableList"
            :ordersInfo="ordersInfo"
            :defaultParams="defaultParams"
            ref="billingComponent"
        >
        </component>
        <div slot="footer">
            <el-button v-if="isSubmitStep" type="primary" @click="handleSubmitBilling"><!-- 提交 -->{{ $t('recharge.submit') }}</el-button>
            <el-button v-if="isBillingDone" type="primary" @click="handleClose"><!-- 确定 -->{{ $t('recharge.confirm') }}</el-button>
        </div>
    </el-dialog>
</template>
<script>
import FilteredOrders from './FilteredOrders';
import PersonalInvoice from './PersonalInvoice';
import EntInvoice from './EntInvoiceBox';
import SuccessfulBilling from './SuccessfulBilling';
import { mapGetters } from 'vuex';
export default {
    components: {
        FilteredOrders,
        PersonalInvoice,
        EntInvoice,
        SuccessfulBilling,
    },
    // eslint-disable-next-line vue/require-prop-types
    props: ['hasDisable', 'ableList', 'billingDialogVisible'],
    data() {
        return {
            componentName: '',
            ordersInfo: {},
            defaultParams: {
                userName: null, // 个人姓名
                entName: null, // 企业名称
                invoiceValue: null, // 开票金额
                rechargeRecordIdList: null,
                // customMobile: '', // 电话
                customAddress: null, // 公司地址
                customEmail: null, // 收件箱
                billingType: null, // 1个人电子，2个人邮寄，3企业电子，4企业邮寄
                addresseeName: null, // 收件人姓名
                addresseeAddress: null, // 收件人地址
                addresseeMobile: null, // 收件人电话
                taxCode: null, // 税号
                bankName: null, // 开户行
                bankAccount: null, // 开户账号
            },
            firstStep: 'FilteredOrders',
            thirdStep: 'SuccessfulBilling',
        };
    },
    computed: {
        ...mapGetters(['getUserType']),
        isSubmitStep() {
            return !this.isBillingDone && this.componentName !== 'FilteredOrders';
        },
        isBillingDone() {
            return this.componentName === this.thirdStep;
        },
        rechargeRecordIds() {
            const resultArrary = this.ableList.map(item => {
                return item.rechargeRecordId;
            });
            return resultArrary.toString();
        },
        secondStep() {
            return this.getUserType === 'Enterprise' ? 'EntInvoice' : 'PersonalInvoice';
        },
    },
    watch: {
        billingDialogVisible: {
            handler(val) {
                if (val) {
                    this.getOrdersInfo();
                    this.componentName = this.hasDisable ? this.firstStep : this.secondStep;
                }
            },
            immediate: true,
        },
        rechargeRecordIds(val) {
            this.$set(this.defaultParams, 'rechargeRecordIdList', val);
        },
    },
    methods: {
        // 获取订单集合的金额等信息
        getOrdersInfo() {
            this.$http.post('/ents/charging/bill/sum', {
                rechargeRecordIdList: this.rechargeRecordIds,
            }).then(res => {
                this.ordersInfo = res.data;
            });
        },
        // 点击提交
        handleSubmitBilling() {
            this.$refs['billingComponent'].submitBilling().then(res => {
                res && (this.componentName = this.thirdStep);
            });
        },
        // 关闭申请开票弹窗
        handleClose() {
            this.$emit('update:billingDialogVisible', false);
            this.$emit('close', this.isBillingDone);
        },
    },
};
</script>
<style lang="scss">
    .box-sizing-dialog #request-billing-dialog .el-dialog__header{
        padding-top: 18px;
    }
    .request-billing-dialog{
        .el-dialog{
            width: 566px;
        }
        .el-dialog__header{
            font-size: 16px;
            color: $--color-text-primary;
            .slot-operate-title{
                margin-bottom: -25px;
            }
            .slot-title{
                margin-bottom: -15px;
                font-size: 15px;
                .slot-title-tips {
                    color: $--color-danger;
                }
            }
        }
        &__title_btn{
            margin-left: 10px;
            width: 115px;
            height: 30px;
            background: $--color-primary;
            border-color: $--color-primary;
            color: $--color-white;

            &:hover{
                background: $--color-primary-light-1;
                border-color: $--color-primary-light-1;
            }

            &:hover, &:focus, &:active{
                color: $--color-white;
            }
        }
        .el-dialog__footer{
            margin-top: -20px;
            margin-bottom: 10px;
            .el-button--primary{
                width: 84px;
                height: 30px;
                background: $--color-primary;;
                border-color: $--color-primary;
                color: $--color-white;

                &:hover, &:focus, &:active{
                    color: $--color-white;
                }
            }
        }

        .billing-submit-form{
            padding: 25px 105px 0;

            .el-form-item{
                margin-bottom: 20px;

                &.form-tip-item{
                    margin-bottom: 0px;
                    padding-bottom: 10px;

                    .el-form-item__content{
                        line-height: 16px;
                        font-size: 12px;
                        color: $--color-text-secondary;
                    }
                }

                &.upload-item{
                    .el-form-item__label {
                        line-height: 16px;
                    }
                    .uploadBtn{
                        border: 1px solid $--color-primary;
                        width: 125px;
                        height: 30px;
                        vertical-align: top;
                        color: $--color-primary;

                        &:active, &:focus{
                            border-color: $--color-primary;
                            color: $--color-primary;
                        }
                    }
                    .el-upload-list__item-name{
                        cursor: default;
                    }

                    .el-form-item__error {
                        top: calc(100% - 5px);
                    }
                }
                &.amount-item{
                    position: relative;
                    .unit{
                        position: absolute;
                        top: 0;
                        left: 10px;
                        color: $--color-text-secondary;
                        z-index: 1;
                    }
                    .el-input__inner{
                        padding-left: 20px;
                    }
                }

                .el-input.is-disabled .el-input__inner{
                    color: $--color-text-secondary;
                }
            }

            &.personal-billing-submit{
                margin-top: -15px;
            }
        }
    }
</style>
