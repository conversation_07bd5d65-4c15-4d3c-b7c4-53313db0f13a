<!-- 充值订单表格 -->
<template>
    <div class="my-order-container">
        <!-- 批量操作按钮，绝对定位到表格上方 -->
        <div
            v-show="selectedLines.length"
            class="recharge-order-table-opeartionLine"
            :style="{
                height: operateHeight,
                lineHeight: operateHeight
            }"
        >
            <span><!-- 已选中{{ selectedLines.length }}个订单 -->{{ $t('recharge.selectOrderTip',{length:selectedLines.length }) }}</span>
            <el-button
                v-if="!isEnterprise"
                class="dozen-operate-btn"
                size="small"
                @click="handleMultBilling"
            ><!-- 批量开票 -->{{ $t('recharge.batchInvoicing') }}</el-button>
        </div>
        <div class="my-order">
            <el-table
                class="ssq-table"
                key="my-order"
                :data="myOrderData"
                highlight-current-row
                style="width: 100%"
                @selection-change="handleSelectionChange"
                ref="myOrderTable"
                v-loading="loadingStatus"
                v-autoH:table="{
                    container: $el,
                    bottom: 49,
                }"
            >
                <el-table-column
                    class-name="my-order-selection"
                    type="selection"
                    width="40"
                >
                </el-table-column>
                <el-table-column
                    class-name="my-order-id"
                    prop="orderId"
                    :label="$t('recharge.orderMap.num')"
                    width="180"
                >
                </el-table-column>
                <el-table-column
                    width="150"
                    prop="packageName"
                    :label="$t('recharge.orderMap.setMeal')"
                >
                </el-table-column>
                <el-table-column
                    prop="numType"
                    width="150"
                    :label="$t('recharge.orderMap.amountAndType')"
                >
                </el-table-column>
                <el-table-column
                    prop="rechargeMoney"
                    width="120"
                    :label="getIsUae ? $t('recharge.orderMap.amountYuanUae') : $t('recharge.orderMap.amountYuan')"
                >
                </el-table-column>
                <el-table-column
                    prop="endTime"
                    :label="$t('recharge.orderMap.expireDate')"
                    width="150"
                >
                </el-table-column>
                <el-table-column
                    prop="loseEfficacyNum"
                    :label="$t('recharge.orderMap.expireNum')"
                    width="150"
                >
                </el-table-column>
                <el-table-column
                    :label="$t('recharge.orderMap.payStatus')"
                    width="120"
                >
                    <template slot-scope="scope">
                        <span
                            v-if="scope.row.status === $t('recharge.orderMap.noPay')"
                            class="payStatus no"
                            @click="goOnPay(scope.row.orderId)"
                        >
                            {{ scope.row.status }}
                        </span>
                        <span v-else class="payStatus">{{ scope.row.status }}</span>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="payTime"
                    :label="$t('recharge.orderMap.payDate')"
                    width="144"
                >
                </el-table-column>
                <el-table-column
                    :label="$t('recharge.orderMap.invoiceStatus')"
                    width="120"
                >
                    <template slot-scope="scope">
                        <template v-if="!scope.row.hideBillStatus">
                            <span
                                v-if="scope.row.billStatus === $t('recharge.orderMap.applyInvoice') || scope.row.billStatus === $t('recharge.orderMap.reApply')"
                                class="billStatus no"
                                @click="handleMultBilling([scope.row])"
                            >
                                <el-tooltip
                                    v-if="scope.row.billStatus === $t('recharge.orderMap.reApply')"
                                    effect="dark"
                                    :content="$t('recharge.orderMap.infoError')"
                                    placement="top"
                                >
                                    <i class="el-icon-ssq-warm-filling"></i>
                                </el-tooltip>

                                {{ scope.row.billStatus }}
                            </span>
                            <span v-else>
                                {{ scope.row.billStatus }}
                            </span>
                        </template>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="createTime"
                    :label="$t('recharge.orderMap.createDate')"
                    width="166"
                >
                </el-table-column>
            </el-table>
        </div>
        <div class="contacts-pagination-absolute-bottom">
            <el-pagination
                @size-change="handleSizeChange"
                @current-change="handleCurrentChange"
                :current-page="myOrderPageData.currentPage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="myOrderPageData.currentPageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="myOrderPageData.total"
                class="console-members-pagination"
            >
            </el-pagination>
        </div>

        <!-- 弹窗 -->
        <div class="box-sizing-dialog">
            <RequestBilling :hasDisable="!couldAllBilling" :ableList="ableList" :billingDialogVisible.sync="billingDialogVisible" @close="handleBillingClose"></RequestBilling>
        </div>
    </div>
</template>
<script>
import RequestBilling from './RequestBilling';
import { mapGetters } from 'vuex';
import i18n from 'src/lang';

export default {
    components: { RequestBilling },
    props: {
        isEnterprise: {
            type: Boolean,
            default: false,
        },
        project: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            payStatusMap: new Map([[1, i18n.t('recharge.payStatusMap.status1')], [2, i18n.t('recharge.payStatusMap.status2')], [3, i18n.t('recharge.payStatusMap.status3')], [4, i18n.t('recharge.payStatusMap.status4')], [5, i18n.t('recharge.payStatusMap.status5')]]), // [[1, '已支付'], [2, '失败'], [3, '注销'], [4, '错误'], [5, '未支付']]
            billStatusMap: new Map([[1, i18n.t('recharge.billStatusMap.status1')], [2, i18n.t('recharge.billStatusMap.status2')], [3, i18n.t('recharge.billStatusMap.status3')], [4, i18n.t('recharge.billStatusMap.status4')]]), // [[1, '申请开票'], [2, '已申请'], [3, '已开票'], [4, '重新申请']]
            myOrderData: [],
            myOrderPageData: { // 分页数据
                total: 0,
                currentPageSize: 10,
                currentPage: 1,
            },
            selectedLines: [],
            ableList: [],
            billingDialogVisible: false,
            loadingStatus: false,
            operateHeight: '40px',
        };
    },
    computed: {
        ...mapGetters(['getAuthStatus', 'getIsUae']),
        couldAllBilling() {
            return this.selectedLines.length === this.ableList.length;
        },
        isEn() {
            return i18n.locale === 'en';
        },
    },
    watch: {
        selectedLines: {
            handler(v) {
                if (v.length) {
                    const tableWrapper = document.querySelector('.el-table__header-wrapper');
                    if (tableWrapper) {
                        this.operateHeight = tableWrapper.offsetHeight - 1 + 'px';
                    }
                }
            },
        },
    },

    methods: {
        getMyOrderData() {
            this.loadingStatus = true;
            const tsAt20190919 = new Date('2019-09-19').getTime();
            // 获取我的订单
            this.$http.get('/ents/charging/rechargeRecords', { params: {
                pageNumber: this.myOrderPageData.currentPage,
                pageSize: this.myOrderPageData.currentPageSize,
            } }).then(res => {
                if (res.data && res.data.list) {
                    this.myOrderData = res.data.list;
                    this.myOrderPageData.total = res.data.count;

                    this.myOrderData.forEach(val => {
                        // operaterType 1 客服代充 2 订单充值 3 平台赠送 4 反向打款
                        // 判断 平台赠送、反向打款、未实名用户、没有金额的订单不允许开票
                        val.hideBillStatus = val.operaterType === 3 || val.operaterType === 4 || this.getAuthStatus !== 2 || ~~val.rechargeMoney <= 0;
                        if (val.status) {
                            val.status = this.payStatusMap.get(val.status);
                        }
                        if (val.products[0]) {
                            const temO = { endTime: '', payTime: '', numType: '', loseEfficacyNum: '' };
                            if (val.products[0].endWorkTime) {
                                temO.endTime = val.products[0].endWorkTime;
                            }
                            if (val.products[0].beginWorkTime) {
                                temO.payTime = val.products[0].beginWorkTime;
                            }
                            temO.loseEfficacyNum = val.products[0].loseEfficacyNum;
                            if (this.isEn) {
                                temO.numType = `${val.products[0].productNum} ${this.$t('mainChart.fen')} ${val.products[0].productTypeName}`;
                            } else {
                                temO.numType = `${val.products[0].productNum}${this.$t('mainChart.fen')}${val.products[0].productTypeName}`;
                            }
                            if (val.products[0].productType === 14) {
                                // 高级功能购买
                                let showProductName = '';
                                val.products.forEach((element, index) => {
                                    showProductName += `${index > 0 ? '、' : ''}${element.productName}`;
                                });
                                temO.numType = showProductName;
                            } else if (val.products[0].productType === 22) { // ai套餐
                                temO.numType = val.packageName === '智签无忧套餐A' ? '1次风险判断+1份对私合同' : '1次风险判断+1份对公合同';
                            }
                            if ([15].includes(val.products[0].productType)) {
                                // 合同比对
                                if (this.isEn) {
                                    temO.numType = `${val.products[0].productNum} ${this.$t('mainChart.ci')}`;
                                } else {
                                    temO.numType = `${val.products[0].productNum}${this.$t('mainChart.ci')}`;
                                }
                            }
                            val = Object.assign(val, temO);
                        }
                        if (val.billStatus) {
                            // safari浏览器不兼容 yyyy-mm-dd hh:mm:ss 格式日期转换
                            const payTimeStamp = new Date((val.payTime).replace(/-/g, '/')).getTime();
                            val.billStatus = payTimeStamp > tsAt20190919 ? this.billStatusMap.get(val.billStatus) : ''; // 只有支付时间晚于20190919的订单才能通过此方式开票
                        }
                    });
                }
                this.loadingStatus = false;
            }).catch(() => {
                this.loadingStatus = false;
            });
        },
        // 批量开票
        handleMultBilling(row) {
            if (this.project === 'console') {
                this.$sensors && this.$sensors.track({
                    eventName: 'Ent_Console_BtnClick',
                    eventProperty: {
                        page_name: window.location.href.indexOf('group') !== -1 ? '集团控制台' : '企业控制台',
                        first_category: '我的订单',
                        icon_name: '批量开票',
                    },
                });
            }
            // 单个开票
            if (row instanceof Array) {
                this.ableList = row;
                this.$refs.myOrderTable.clearSelection();
                this.$refs.myOrderTable.toggleRowSelection(row[0], true);
            } else if (this.selectedLines.length === 0) { // 企业点击批量开票时 存在未选择订单的情况
                return this.$MessageToast.info(this.$t('recharge.orderMap.selectOneOrder')); // 请选择一个以上订单
            } else {
                // 处理 ableList
                this.ableList = this.selectedLines.filter(item => {
                    return item.status === this.$t('recharge.payStatusMap.status1') && (item.billStatus === this.$t('recharge.billStatusMap.status1') || item.billStatus === this.$t('recharge.billStatusMap.status4')) && !item.hideBillStatus;
                });
            }

            if (this.ableList.length === 0) {
                this.$MessageToast(this.$t('recharge.orderMap.noInvoiceOrder')); // 没有可开票的订单
                return false;
            }

            this.billingDialogVisible = true;
        },
        // 开票弹窗关闭
        handleBillingClose(done) {
            // 刷新表格
            if (done) {
                this.getMyOrderData();
            }
        },
        // 去充值
        goOnPay(id) {
            const path = `${this.isEnterprise ? '' : '/usercenter/recharge/'}pay?id=${id}`;
            this.$router.push(path);
        },
        // 分页
        handleSizeChange(val) {
            this.myOrderPageData.currentPageSize = val;
            this.getMyOrderData();
        },
        handleCurrentChange(val) {
            this.myOrderPageData.currentPage = val;
            this.getMyOrderData();
        },
        // 表格选择
        handleSelectionChange(list) {
            this.selectedLines = list;
        },
    },
    created() {
        this.getMyOrderData();
    },
};
</script>

<style lang="scss">
.my-order .el-table {
    .cell {
        padding-left: 10px;
        padding-right: 0;
        word-break: break-word;
        white-space: normal;
    }

    .my-order-selection .cell {
        padding-left: 20px;
        padding-right: 0;
        [dir=rtl] & {
            padding-left: 0;
            padding-right: 20px;
        }
    }

    .my-order-id .cell {
        padding-left: 6px;
        [dir=rtl] & {
            padding-left: 0;
            padding-right: 6px;
        }
    }
    .el-table__header-wrapper tr th{
        height: 43px;
        .cell{
            color: $--color-text-primary;
        }
    }
    .el-table__body-wrapper .el-table__row td{
        box-sizing: content-box;
        padding-top: 3px;
        padding-bottom: 3px;
    }
}
.recharge-order-table-opeartionLine{
    position: absolute;
    left: 46px;
    top: 43px;
    width: calc(100% - 46px);
    height: 40px;
    line-height: 40px;
    z-index: 1;
    background: $--background-color-base;
    [dir=rtl] & {
        left: auto;
        right: 46px;
    }

    .dozen-operate-btn{
        margin-left: 5px;
        padding: 0 20px;
        height: 28px;
        border-color: $--color-primary-light-3;
        border-radius: 1px;
        background: $--color-primary-light-8;
        color: $--color-text-primary;

        &:hover{
                color: $--color-primary-light-1;
        }
    }
}
.recharge-con .enterprise-recharge-table-con {
    position: relative;

    .el-table {
        border-top: none;

        tbody {
            color: $--color-text-regular;
            .payStatus {
                &.no {
                    color: $--color-danger;
                    cursor: pointer;
                }
            }
            .billStatus {
                &.no {
                    cursor: pointer;
                    color: $--color-primary-light-1;
                }

                .el-icon-ssq-warm-filling{
                    margin-left: -14px;
                    color: $--color-danger;
                }
            }
        }
    }
    .recharge-order-table-opeartionLine {
        position: absolute;
        top: 0;
        font-size: 12px;
    }
}
</style>
