<template>
    <div class="usage-record-container">
        <div class="usage-record-searchbox">
            <div class="usage-record-searchbox-item">
                <label>{{ $t('rechargeOrder.searchType') }}</label>
                <el-select v-model="timeRangeType">
                    <el-option :label="$t('rechargeOrder.sendTime')" :value="1"></el-option>
                    <el-option :label="$t('rechargeOrder.deductTime')" :value="2"></el-option>
                </el-select>
            </div>
            <div class="usage-record-searchbox-item">
                <label><!-- 选择时间范围： -->{{ $t('rechargeOrder.selectDateRange') }}</label>
                <el-date-picker
                    v-model="rangeTime"
                    type="daterange"
                    :range-separator="$t('rechargeOrder.till')"
                    :start-placeholder="$t('rechargeOrder.startDate')"
                    :end-placeholder="$t('rechargeOrder.endDate')"
                    :clearable="false"
                >
                </el-date-picker>
            </div>
            <el-button type="primary" @click="handleSearch"><!-- 搜索 -->{{ $t('rechargeOrder.search') }}</el-button>
            <div class="usage-record-export">
                <el-button @click="handleGoBatchOperateCenter">{{ $t('rechargeOrder.batchCenter') }}</el-button>
                <el-button
                    @click="handleExport"
                    v-loading.fullscreen.lock="exportLoading"
                >
                    <i class="el-icon-ssq-daochu1"></i><!-- 导出 -->{{ $t('rechargeOrder.export') }}
                </el-button>
            </div>
        </div>
        <div class="usage-record">
            <el-table
                class="ssq-table"
                key="usage-record"
                :data="usageRecordData"
                highlight-current-row
                style="width: 100%"
                v-loading="loadingStatus"
                v-autoH:table="{
                    container: $el,
                    bottom: 49,
                }"
            >
                <el-table-column
                    class-name="usage-record-create-time"
                    prop="createTime"
                    :label="$t('rechargeOrder.sendTime')"
                    width="180px"
                >
                </el-table-column>
                <el-table-column
                    v-if="isEnt && !checkFeat.deductMethod"
                    class-name="usage-record-create-time"
                    prop="deductTime"
                    :label="$t('rechargeOrder.deductTime')"
                    width="180px"
                >
                </el-table-column>
                <el-table-column
                    prop="docName"
                    :label="$t('rechargeOrder.contractTitle')"
                    min-width="176px"
                >
                </el-table-column>
                <el-table-column
                    prop="contractId"
                    :label="$t('rechargeOrder.contractId')"
                    min-width="120px"
                >
                </el-table-column>
                <el-table-column
                    v-if="$store.state.commonHeaderInfo.userType == 'Enterprise'"
                    min-width="300px"
                    :label="$t('rechargeOrder.user')"
                >
                    <template slot-scope="scope">
                        {{ scope.row.operatorEntName }} {{ scope.row.operatorAccount }}
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="isEnt"
                    prop="operatorDeptName"
                    :label="$t('rechargeOrder.belongDepartment')"
                    width="120px"
                >
                </el-table-column>
                <el-table-column
                    width="120"
                    prop="productTypeName"
                    :label="$t('rechargeOrder.contractType')"
                >
                    <template slot="header">
                        {{ $t('rechargeOrder.contractType') }}
                        <span v-if="!isEnt"></span>
                        <CommonTip v-else>
                            <template slot="content">
                                <span>{{ $t('rechargeOrder.personConsumedCopiesTips1') }}</span>
                            </template>
                        </CommonTip>
                    </template>
                </el-table-column>
                <el-table-column
                    prop="sendNum"
                    :width="isZh ? '' : '140'"
                >
                    <template slot="header">
                        {{ $t('rechargeOrder.consumedCopies') }}
                        <span v-if="!getIsForeignVersion && isEnt && checkFeat.deductMethod"></span>
                        <CommonTip v-else>
                            <template slot="content">
                                <span v-if="!getIsForeignVersion && isEnt">{{ $t('rechargeOrder.consumedCopiesTips') }}</span>
                                <div v-else>
                                    <div>{{ $t('rechargeOrder.personConsumedCopiesTips1') }}</div>
                                    <div>{{ $t('rechargeOrder.personConsumedCopiesTips2') }}</div>
                                </div>
                            </template>
                        </CommonTip>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="!getIsForeignVersion && isEnt && !checkFeat.deductMethod"
                    prop="freezedNum"
                    :width="isZh ? '' : '140'"
                >
                    <template slot="header">
                        {{ $t('recharge.withHoldCopies') }}
                        <CommonTip :content="$t('recharge.withHoldCopiesTips')"></CommonTip>
                    </template>
                </el-table-column>
                <el-table-column
                    v-if="!getIsForeignVersion && isEnt && !checkFeat.deductMethod"
                    prop="completeNum"
                    :width="isZh ? '' : '140'"
                >
                    <template slot="header">
                        {{ $t('recharge.actuallDeductedCopies') }}
                        <CommonTip :content="$t('recharge.actuallDeductedCopiesTips')"></CommonTip>
                    </template>
                </el-table-column>
            </el-table>
        </div>
        <div class="contacts-pagination-absolute-bottom">
            <el-pagination
                @size-change="handleUsageRecordSizeChange"
                @current-change="handleUsageRecordCurrentChange"
                :current-page="usageRecordPageData.currentPage"
                :page-sizes="[10, 20, 30, 40]"
                :page-size="usageRecordPageData.currentPageSize"
                layout="total, sizes, prev, pager, next, jumper"
                :total="usageRecordPageData.total"
                class="console-members-pagination"
            >
            </el-pagination>
        </div>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import { formatDateToString } from 'pub-utils/date.js';
export default {
    data() {
        return {
            usageRecordData: [],
            usageRecordPageData: { // 分页数据
                total: 0,
                currentPageSize: 10,
                currentPage: 1,
            },
            searchDataTime: {
                startTime: null,
                endTime: null,
            }, // 起始时间和结束时间
            loadingStatus: false,
            rangeTime: [],
            timeRangeType: 1,
            exportLoading: false,
        };
    },
    computed: {
        ...mapGetters([
            'isNewGroupPath',
            'getIsForeignVersion',
            'checkFeat',
            'getUserType',
        ]),
        isZh() {
            return this.$i18n.locale === 'zh';
        },
        isEnt() {
            return this.getUserType === 'Enterprise';
        },
    },
    methods: {
        getUsageRecordData() {
            this.loadingStatus = true;
            // 获取使用记录
            this.$http.get('/ents/charging/expenseGroup', { params: {
                page: this.usageRecordPageData.currentPage,
                size: this.usageRecordPageData.currentPageSize,
                timeRangeType: this.timeRangeType,
                ...this.searchDataTime,
            } }).then(res => {
                if (res.data && res.data.content) {
                    this.usageRecordData = res.data.content;
                    this.usageRecordPageData.total = parseInt(res.data.totalElements);
                }
                this.loadingStatus = false;
            }).catch(() => {
                this.loadingStatus = false;
            });
        },
        handleUsageRecordSizeChange(val) {
            // console.log(`每页 ${val} 条`);
            this.usageRecordPageData.currentPageSize = val;
            this.getUsageRecordData();
        },
        handleUsageRecordCurrentChange(val) {
            // console.log(`当前页: ${val}`);
            this.usageRecordPageData.currentPage = val;
            this.getUsageRecordData();
        },
        // 触发搜索
        handleSearch() {
            const result = (this.rangeTime || []).map(time => formatDateToString({
                date: time,
                format: 'YYYY-MM-DD',
            }));
            this.searchDataTime = {
                startTime: result.length > 1 ?  result[0] : null,
                endTime: result.length > 1 ?  result[1] : null,
            };
            this.usageRecordPageData.currentPage = 1;
            this.getUsageRecordData();
        },
        handleGoBatchOperateCenter() {
            window.open('/sign-flow/doc-manage/batch-log?taskType=rechargeUseRecord');
        },
        handleExport() {
            // if (this.usageRecordPageData.total > 10000) {
            //     return this.$MessageToast.error(this.$t('rechargeOrder.exportMaxTip')); // 导出数据不能大于10000条
            // }
            const { startTime, endTime } = this.searchDataTime;
            const getTaskIdUrl = `/ents/charging/expenseGroup/export/confirm?startTime=${encodeURIComponent(startTime || '')}&endTime=${encodeURIComponent(endTime || '')}&isNewGroup=${this.isNewGroupPath}&timeRangeType=${this.timeRangeType}`;
            this.exportLoading = true;
            this.$http.get(getTaskIdUrl).then(() => {
                this.$confirm(this.$t('rechargeOrder.batchExportTip'), this.$t('commonHeader.tip')) // 确认关闭？
                    .then(() => {
                        this.handleGoBatchOperateCenter();
                    });
            }).finally(() => {
                this.exportLoading = false;
            });
        },
        invokeDownloadHelper(url) {
            const $el = document.querySelector('#J_download-link') || document.createElement('a');
            $el.setAttribute('id', 'J_download-link');
            $el.setAttribute('style', 'visibility:hidden;height:0;width:0;');
            $el.setAttribute('target', '_self');
            $el.setAttribute('href', url);
            document.body.appendChild($el);
            $el.click();
        },
    },
    created() {
        const now = new Date();
        this.rangeTime = [now.setTime(now.getTime() - 3600 * 1000 * 24 * 30), new Date()];
        this.handleSearch();
    },
};
</script>

<style lang="scss">

.en-page .usage-record-container .usage-record .el-table .el-table__header-wrapper tr th .cell {
    word-break: break-word;
    white-space: normal;

}
.usage-record-container {
    .usage-record-searchbox {
        padding: 15px 20px;
        line-height: 30px;
        box-sizing: content-box;
        label {
            font-size: 14px;
            color: $--color-text-primary;
        }
        &-item{
            float: left;
            margin-right: 10px;
            [dir=rtl] & {
                margin-right: 0;
                margin-left: 10px;
                float: right;
            }
        }
        .el-date-editor--daterange.el-input__inner {
            width: 300px;
        }
        .el-date-editor {
            .el-range__icon {
                line-height: 24px;
            }
            .el-range-separator {
                width: 30px;
                line-height: 24px;
            }
            .el-range__close-icon {
                font-size: 16px;
                line-height: 24px;
                height: 24px;
            }
        }
        .usage-record-export {
            float: right;
            border-color: $--color-primary;
            color: $--color-primary;
            background-color: $--color-white;
            [dir=rtl] & {
                float: left;
            }
        }
    }

    .usage-record .el-table {
        .cell {
            padding-left: 10px;
            padding-right: 0;
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 10px;
                text-align: right;
            }
        }

        .usage-record-create-time .cell {
            padding-left: 20px;
            [dir=rtl] & {
                padding-right: 20px;
            }
        }

        .el-table__header-wrapper tr th{
            height: 43px;
            .cell{
                color: $--color-text-primary;
            }
        }
        .el-table__body-wrapper .el-table__row td{
            box-sizing: content-box;
            padding-top: 3px;
            padding-bottom: 3px;
        }
    }
    .recharge-con .enterprise-recharge-table-con{
        .el-table {
            border-top: none;
        }
    }

    .el-tooltip__content {
       white-space: pre-line; /* 允许换行 */
    }
}
</style>
