<template>
    <!-- 购买高级功能 -->
    <div v-if="!getIsForeignVersion && listData && listData.length > 0" class="advanced-features-container">
        <h3>
            {{ $t('rechargePackage.advancedFeature') }}
            <el-button v-if="expirationList.length" @click="handleShowExpirationDialog" type="primary">查看功能有效期</el-button>
        </h3>
        <div class="features-box">
            <div
                class="features-item"
                v-for="(item, index) in listData"
                :key="item.authinfoType"
                :class="`${(item.canPurchase) && 'active'} ${!item.canPurchase ? 'is-using' : ''} ${checkItemIsSelected(item) ? 'is-selected' : ''}`"
                @click="handleChooseItem(item, index)"
            >
                <div class="features-item__activity-icon" v-if="showActivityIcon(item)">
                    <div>送</div><span>15份合同</span>
                </div>
                <div class="feature-content">
                    <div class="select-icon">
                        <i v-if="item.canPurchase" :class=" `${ checkItemIsSelected(item) ? 'el-icon-ssq-btn_seal_selected_qiantaidaishouh': 'el-icon-ssq-btn_seal_select_qiantaidaishouh'}`"></i>
                    </div>
                    <div class="icon-box" :class="item.isPurchased ? 'is-using' : ''">
                        <i class="el-icon-ssq-gongneng icon-feature"></i>
                    </div>
                    <div class="detail">{{ item.name }}</div>
                    <div class="detail">¥{{ item.isNumChangeable ? item.price : item.originPrice }}/{{ $t('rechargePackage.year') }}</div>
                    <div class="learn-detail" @click.stop="handleLearnMoreClick(item)">{{ $t('rechargePackage.learnDetail') }}</div>
                </div>
                <div
                    class="feature-status"
                    :class="`${item.isPurchased ? 'is-using' : ''} ${checkItemIsSelected(item) ? 'is-selected' : ''}`"
                >
                    {{ item.isPurchased ? $t('rechargePackage.isUsing') : $t('rechargePackage.unUsing') }}
                </div>
            </div>
        </div>
        <div class="place-order-box">
            <div class="left"></div>
            <div class="container">
                <el-checkbox v-model="checkAll" @change="handleCheckAllChange">{{ $t('companyFull.selectAll') }}</el-checkbox>
                <div class="pay-amount">{{ $t('rechargePackage.payAmount') }}：<span>{{ totalAmout }}</span></div>
                <el-button :disabled="!selectedList.length > 0" type="primary" @click="confirmRenewal">{{ $t('rechargePackage.placeOrder') }}</el-button>
            </div>
        </div>
        <el-dialog
            class="expirationDialog"
            :title="isRenewal ? '购买提醒' : '功能有效期'"
            :visible.sync="showExpirationDialog"
        >
            <div>{{ isRenewal ? '您将购买的功能详情如下，请确认是否要继续购买？' : '生效中的功能详情如下' }}</div><br>
            <el-table
                :data="isRenewal ? selectedList : expirationList"
                border
            >
                <el-table-column align="center" prop="name" label="功能名称"></el-table-column>
                <el-table-column align="center" prop="status" label="当前状态"></el-table-column>
                <el-table-column align="center" prop="showExpirationTime" label="当前有效期至"></el-table-column>
                <el-table-column align="center" prop="expirationTimeAfterRenewal" label="购买后有效期至"></el-table-column>
            </el-table>
            <span slot="footer" class="dialog-footer">
                <el-button type="primary" @click="handleClose">{{ isRenewal ? '确定' : '知道了' }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapGetters } from 'vuex';
export default Vue.extend({
    data() {
        return {
            listData: [],
            selectedList: [], // 已选择的要支付的高级功能list
            checkAll: false, // 是否全选
            totalAmout: 0, // 总的要支付的金额
            featureIdList: this.$route.query.featureIdData ? JSON.parse(this.$route.query.featureIdData) : [], // 功能介绍跳转过来的featureId数组
            showExpirationDialog: false,
            isRenewal: false,
        };
    },
    computed: {
        ...mapGetters([
            'isNewGroupPath',
            'getIsForeignVersion',
        ]),
        // 总的未开通的功能总金额
        totoalUnusingAmount() {
            let amout = 0;
            this.canPurchaseList.forEach(e => {
                amout += (e.isNumChangeable ? e.price : e.originPrice);
            });
            return amout;
        },
        // 能开通/续费功能的list
        canPurchaseList() {
            return this.listData.filter(e => e.canPurchase);
        },
        // 正在使用的功能list
        expirationList() {
            return this.listData.filter(e => !!e.expirationTime);
        },
    },
    methods: {
        showActivityIcon(item) {
            const expired = +new Date() > +new Date('2025-01-20T18:00:00'); // 过期时间
            return !expired && (item.name === '骑缝章+水印' || item.name === '模板授权');
        },
        // 点击某一功能
        handleChooseItem(item) {
            if (item.canPurchase) {
                const isSelect = this.selectedList.some(e => e.name === item.name);
                if (!isSelect) {
                    this.selectedList = [...this.selectedList, item];
                    this.totalAmout += (item.isNumChangeable ? item.price : item.originPrice);
                } else {
                    this.selectedList = this.selectedList.filter(e => e.name !== item.name);
                    this.totalAmout -= (item.isNumChangeable ? item.price : item.originPrice);
                }
                this.checkAll = this.selectedList.length  === this.canPurchaseList.length;
            }
        },
        // 判断功能是否被选择
        checkItemIsSelected(item) {
            return this.selectedList.some(e => e.name === item.name);
        },
        // 全选
        handleCheckAllChange() {
            this.selectedList = this.checkAll ? this.canPurchaseList : [];
            this.totalAmout = this.checkAll ? this.totoalUnusingAmount : 0;
        },
        confirmRenewal() {
            const idList = this.selectedList.map(e => e.productPackageId);
            const nameList = this.selectedList.map(e => e.name);
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Console_BtnClick',
                eventProperty: {
                    page_name: window.location.href.indexOf('group/') !== -1 ? '集团控制台' : '企业控制台',
                    first_category: '账户余额',
                    icon_name: '下单购买',
                    commodity_id: idList.join(','),
                    commodity_name: nameList.join(','),
                    commodity_num: idList.length,
                },
            });
            this.isRenewal = true;
            this.showExpirationDialog = true;
        },
        placeOrder() {
            const idList = this.selectedList.map(e => e.productPackageId);
            this.$http.post(`ents/charging/advancedFeatures/ordering`, {
                ids: idList,
                isNewGroup: this.isNewGroupPath,
            }).then(res => {
                const { orderId } = res.data;
                const path = `recharge/pay?id=${orderId}&pageFrom=console`;
                this.$router.push(path);
            });
        },
        handleClose() {
            if (this.isRenewal) {
                return this.placeOrder();
            }
            this.showExpirationDialog = false;
        },
        handleShowExpirationDialog() {
            this.isRenewal = false;
            this.showExpirationDialog = true;
        },
        // 了解详情
        handleLearnMoreClick(item) {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Console_BtnClick',
                eventProperty: {
                    page_name: window.location.href.indexOf('group/') !== -1 ? '集团控制台' : '企业控制台',
                    first_category: '账户余额',
                    icon_name: '高级功能查看有效期',
                    commodity_id: item.productPackageId,
                    commodity_name: item.name,
                },
            });
            this.$featureSupport({ featureId: item.featureId, type: 'learnDetail' });
        },
        init() {
            if (this.featureIdList) {
                this.selectedList = [];
                this.totalAmout  = 0;
                // 自动选中跳转过来的功能
                this.listData.forEach(item => {
                    if (this.featureIdList.includes(item.featureId)) {
                        this.handleChooseItem(item);
                    }
                });
            }
        },
    },
    mounted() {
        this.$http.get('/ents/charging/advancedFeatures').then(res => {
            this.listData = res.data.filter(el => !el.name.toLowerCase().includes('hubble')).map(el => {
                return {
                    ...el,
                    status: !el.expirationTime ? '未开通' : (el.isOpen ? '生效中' : '未生效'),
                    showExpirationTime: el.expirationTime || '-',
                    expirationTimeAfterRenewal: el.expirationTimeAfterRenewal || dayjs().add(1, 'year').format('YYYY-MM-DD'),
                };
            });
            this.init();
        });
    },
});
</script>

<style lang="scss">
	.advanced-features-container{
        border-bottom: 1px solid $--border-color-lighter;
		h3{
            height: 60px;
			line-height: 60px;
			font-size: 14px;
            padding-left: 20px;
            font-weight: 500;
            color: $--color-text-primary;
            border-bottom: 1px solid $--border-color-lighter;
            [dir=rtl] & {
                padding-right: 20px;
                padding-left: 0;
            }
		}
		.features-box{
            display: flex;
            flex-wrap: wrap;
			margin-top: 20px;
            margin-right: 30px;
            [dir=rtl] & {
                margin-right: 0;
                margin-left: 30px;
            }
			.features-item{
				display: inline-block;
				margin-left: 30px;
				margin-bottom: 15px;
				text-align: center;
                position: relative;
                [dir=rtl] & {
                    margin-left: 0;
                    margin-right: 30px;
                }
                &.is-using {
                    .feature-content {
                        border: 1px solid $--color-success ;
                    }
                }
                &.is-selected{
                    .feature-content {
                        border: 1px solid $--color-primary ;
                    }
                }
				span,i{
					vertical-align: middle;
				}

				.feature-content{
					width: 160px;
					height: 210px;
					border: 1px solid $--border-color-light;
					font-size: 14px;
					color: $--color-text-primary;
					transition: border 0.2s;
                    .select-icon{
                        font-size: 15px;
                        height: 20px;
                        margin: 10px 12px 0 0;
                        text-align: end;
                        [dir=rtl] & {
                            margin: 10px 0 0 12px;
                        }
                        .el-icon-ssq-btn_seal_selected_qiantaidaishouh{
                            color: $--color-primary;
                        }

                    }
                    .icon-box{
                        width: 50px;
                        height: 50px;
                        margin: 0 0 10px 55px;
                        background:  #CCCCCC;
                        border-radius: 25px;
                        text-align: center;
                        [dir=rtl] & {
                            margin: 0 55px 10px 0;
                        }
                         &.is-using {
                            background: $--color-success;
                        }
                        .icon-feature{
                            font-size: 28px;
                            color: white;
                            margin-top: 10px;
                        }
                    }

                    .detail{
                        height: 30px;
                        line-height: 30px;
                    }
                    .learn-detail{
                        font-size: 12px;
                        color: $--color-primary;
                        cursor: pointer;
                    }
					span{
						font-size: 14px;
					}
				}

				.feature-status{
                    color: white;
					height: 34px;
					line-height: 34px;
					font-size: 14px;
					background: $--border-color-light;
					transition: background 0.2s;

					i{
						font-size: 18px;
					}

					&.waiting{
						span,i{
							color: $--color-text-regular;
						}
					}
                    &.is-using {
                        span {
                            color: $--color-white
                        }
                        background: $--color-success;
                    }
                    &.is-selected{
                        background: $--color-primary;
                    }
				}

				&.active:not(.is-using):hover{
					cursor: pointer;

					.feature-content{
						border-color: $--color-primary;
					}

					.feature-status{
						background: $--color-primary;

						span,i{
							color: $--color-white;
						}
					}
				}
                &__activity-icon{
                    position: absolute;
                    background: #f1478e;
                    color: #fff;
                    font-size: 12px;
                    border-radius: 0 0 5px 0;
                    display: flex;
                    line-height: 14px;
                    padding: 3px 8px;
                    div{
                        font-size: 10px;
                        width: 14px;
                        height: 14px;
                        text-align: center;
                        line-height: 10px;
                        padding: 2px;
                        background: #fff;
                        color: #f1478e;
                        border-radius: 50%;
                        margin-right: 3px;
                        [dir=rtl] & {
                            margin-right: 0;
                            margin-left: 3px;
                        }
                    }
                }
			}
		}
        .place-order-box{
            margin:  0 30px 20px 0;
            display: flex;
            [dir=rtl] & {
                margin:  0 0 20px 30px;
            }
            .left{
                flex: 1;
            }
            .container{
                display: flex;
                justify-content: center;
                align-items: center;
                .el-checkbox{
                    padding-top: 5px;
                }
                .pay-amount{
                    font-size: 14px;
                    margin: 0 20px;
                    color: $--color-text-secondary;
                    [dir=rtl] & {
                        display: flex;
                    }
                    span{
                        font-size: 18px;
                        color: $--color-primary;
                    }
                }
            }
        }
	}

	// .en-page .advanced-features-container{
    //     word-break: break-word;
    //     white-space: normal;
	// 	.features-box{
	// 		.features-item{
	// 			.ent-authWay-content{
	// 				width: 347px;
	// 			}
	// 		}
	// 	}
	// }

</style>
