<template>
    <div class="balance-con">
        <div v-if="isEnterprise" class="console-block-title">{{ $t('recharge.balance') }}</div>
        <div v-else class="balance-con-title"><!-- 账户余额 -->{{ $t('recharge.balance') }}</div>
        <ul class="balance-detail">
            <li v-if="onlyUnlimited" class="per-balance-detail public-balance-detail">
                <p class="inline-block">
                    <span class="inline-block" style="background-color: #FF9500;">
                        <i class="el-icon-ssq-wuxianliang" style="font-size: 16px; position: relative; top: -1px;"></i>
                    </span>
                </p>
                <p class="inline-block">
                    <span><!-- 不限量合同 -->{{ $t('recharge.noLimitContract') }}</span><br>
                    <span><!-- 有效期内不限量使用 -->{{ $t('recharge.unLimitUseTip') }}</span>
                </p>
                <p class="inline-block"></p>
                <p class="inline-block"></p>
                <p class="inline-block"></p>
                <p class="inline-block"></p>
                <p class="inline-block"></p>
                <p class="inline-block">
                    <span></span><span>{{ endTime }} <br> <!-- 到期时间 -->{{ $t('recharge.expireDate') }}</span>
                </p>
                <p class="inline-block line"></p>
                <el-button
                    class="recharge-btn btn-type-one"
                    @click="goToRecharge('all')"
                >
                    <!-- 充值 -->{{ $t('recharge.recharge') }}
                </el-button>
            </li>
            <template v-else>
                <li class="per-balance-detail public-balance-detail">
                    <p class="inline-block">
                        <span class="inline-block">
                            <i class="el-icon-ssq-duigonghetong"></i>
                        </span>
                    </p>
                    <p class="inline-block">
                        <span><!-- 对公合同 -->{{ $t('recharge.publicContract') }}</span><br>
                        <span><!-- 签署人（不包含发件人）中有企业账户的文档 -->{{ $t('recharge.tip1') }}</span>
                    </p>
                    <template v-if="HOST_ENV == 'official'">
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block">
                            <span>{{ accountBalanceData.useable2bSum }}</span><br>
                            <span><!-- 可用份数 -->{{ $t('recharge.useCopies') }}</span>
                        </p>
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block" v-if="!getIsForeignVersion && isEnt">
                            <span>{{ accountBalanceData.withHoldingConsume2bNum }}</span><br>
                            <span><!-- 预扣份数 -->{{ $t('recharge.withHoldCopies') }}</span>
                        </p>
                        <p class="inline-block line" v-if="!getIsForeignVersion && isEnt">

                        </p>
                        <p class="inline-block">
                            <span>{{ accountBalanceData.actualConsume2bNum }}</span><br>
                            <span><!-- 实扣份数 -->{{ isEnt ? $t('recharge.actuallDeductedCopies') : $t('recharge.alreadyDeductedCopies') }}</span>
                        </p>
                    </template>
                    <p class="inline-block line"></p>
                    <el-button
                        class="recharge-btn btn-type-one"
                        @click="goToRecharge('public')"
                    >
                        <!-- 充值 -->{{ $t('recharge.recharge') }}
                    </el-button>
                </li>
                <li class="per-balance-detail personal-balance-detail">
                    <p class="inline-block">
                        <span class="inline-block">
                            <i class="el-icon-ssq-duisihetong"></i>
                        </span>
                    </p>
                    <p class="inline-block">
                        <span><!-- 对私合同 -->{{ $t('recharge.privateContract') }}</span><br>

                        <span><!-- 签署人（不包含发件人）中没有企业账户的文档 -->{{ $t('recharge.tip2') }}</span>
                    </p>
                    <template v-if="HOST_ENV == 'official'">
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block">
                            <span> {{ accountBalanceData.useable2cSum }} </span><br>
                            <span><!-- 可用份数 -->{{ $t('recharge.useCopies') }}</span>
                        </p>
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block" v-if="!getIsForeignVersion && isEnt">
                            <span>{{ accountBalanceData.withHoldingConsume2cNum }}</span><br>
                            <span><!-- 预扣份数 -->{{ $t('recharge.withHoldCopies') }}</span>
                        </p>
                        <p class="inline-block line" v-if="!getIsForeignVersion && isEnt">

                        </p>
                        <p class="inline-block">
                            <span>{{ accountBalanceData.actualConsume2cNum }}</span><br>
                            <span><!-- 实扣份数 -->{{ isEnt ? $t('recharge.actuallDeductedCopies') : $t('recharge.alreadyDeductedCopies') }}</span>
                        </p>
                    </template>

                    <p class="inline-block line">

                    </p>
                    <el-button
                        class="recharge-btn btn-type-one"
                        @click="goToRecharge('private')"
                    >
                        <!-- 充值 -->{{ $t('recharge.recharge') }}
                    </el-button>
                </li>
            </template>
        </ul>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import { formatDateToString } from 'pub-utils/date.js';
export default {
    props: {
        isEnterprise: {
            type: Boolean,
            default: false,
        },
        pageFrom: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            HOST_ENV: this.GLOBAL.HOST_ENV,
            rechargeVisible: false,
            accountBalanceData: {},
            onlyUnlimited: false,
            endTime: '',
        };
    },
    computed: {
        ...mapGetters(['getUserType', 'getIsForeignVersion']),
        isEnt() {
            return this.getUserType === 'Enterprise';
        },
    },
    methods: {
        goToRecharge(packageType) {
            this.$router.push(`recharge/selectpackage?packageType=${packageType}&pageFrom=${this.pageFrom}`);
        },
    },
    created: function() {
        // 查询余额
        this.$http.get('/ents/charging/wallet')
            .then(res => {
                const resData = res.data;
                this.accountBalanceData = res.data;
                this.onlyUnlimited = resData.unlimited;

                this.onlyUnlimited && (this.endTime = formatDateToString({
                    date: resData.unlimitedEndWorkTime || '',
                    format: 'YYYY-MM-DD',
                }));
            });
    },
};
</script>

<style lang="scss">

    .balance-con {
        width: 100%;
        .balance-con-title {
            width: 100%;
            height: 40px;
            padding-left: $main-padding-left;
            line-height: 40px;
            background-color: $--background-color-regular;
            color: $--color-text-primary;
            font-weight: bold;
        }
        .per-balance-detail {
            display: flex;
            width: 100%;
            padding-top: 6px;
            padding-bottom: 6px;
            border-bottom: 1px solid $--border-color-lighter;
            p {
                height: 60px;
                margin-top: 10px;
                vertical-align: middle;
                color: $--color-text-primary;
                & > span:nth-of-type(1) {
                    margin-bottom: 10px;
                    line-height: 28px;
                }
            }
            p:nth-child(1) {
                padding-left: $main-padding-left;
                & > span {
                    width: 48px;
                    height: 48px;
                    border-radius: 100%;
                    line-height: 58px;
                    text-align: center;
                }
                i {
                    font-size: 27px;
                    color: $--color-white;
                }
            }
            p.line {
                margin-top: 0;
                border-right: 1px solid $--border-color-lighter;
            }
            p:nth-child(2) {
                // width: 47%;
                flex: 1;
                padding-left: 13px;
                & > span:nth-of-type(1) {
                    font-size: 14px;
                }
                & > span:nth-of-type(2) {
                    color: $--color-text-regular;
                    font-size: 12px;
                }
            }
            p:nth-child(4), p:nth-child(6), p:nth-child(8) {
                width: 10%;
                text-align: center;
                & > span {
                    &:nth-of-type(1) {
                        color: $--color-primary;
                        font-size: 16px;
                    }

                    &:nth-of-type(2) {
                        color: $--color-text-secondary;
                        font-size: 12px;
                    }
                }
            }
            p:nth-child(10) {
                width: 10%;
                padding-top: 8px;
                text-align: center;
            }
            .recharge-btn {
                width: 100px;
                height: 30px;
                margin-top: 15px;
                line-height: 30px;
                padding: 0;
                margin-right: 66px;
                margin-left: 10px;
            }
        }
        .public-balance-detail {
            p:nth-child(1) {
                & > span {
                    background-color: $--color-primary;
                }
            }
        }
        .personal-balance-detail {
            p:nth-child(1) {
                & > span {
                    background-color: $--color-success;
                }
            }

        }
    }

</style>
