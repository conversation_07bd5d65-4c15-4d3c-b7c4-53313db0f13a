<template>
    <div>
        <div class="order-pay-con">
            <div class="title">
                <i class="el-icon-ssq-qiandai"></i>
                <h1><!-- 在线支付 -->{{ $t('recharge.onlinePay') }}<span class="order-pay-tips"><!-- （支付后不能退款） -->{{ $t('recharge.noRefundAfterPay') }}</span></h1>
            </div>
            <div class="order-con">
                <div class="order-con-title">
                    <span><!-- 订单号： -->{{ $t('recharge.orderNum') }}{{ myOrderData.orderId }}</span>
                </div>
                <ul class="order-detail">
                    <li class="per-order-detail">
                        <p class="inline-block">
                            {{ myOrderData.packageName }}
                        </p>
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block">
                            {{ !isAiPay ? calProductName : myOrderData.packageName === '智签无忧套餐A' ? '1次风险判断+1份对私合同' : '1次风险判断+1份对公合同' }}
                        </p>
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block" v-if="isRechargeMember"><!-- 成员数量： -->{{ $t('recharge.memberMumer') }}{{ curProduct.productNum }}</p>
                        <p class="inline-block" v-if="isAdvancedFeature">{{ $t('recharge.number') }}{{ myOrderData.products.length }}</p>
                        <p class="inline-block" v-else-if="!isRechargeMember">
                            <!-- 文档数量：{{ curProduct.productNum }}份 -->
                            {{ curProduct.productNum }}
                            <span class="danger" v-if="isSupportDiscountCode && discountCount">+ {{ discountCount }} {{ $t('RechargeAccount.orders.numberUnit') }}</span>
                            <span v-else>{{ $t('RechargeAccount.orders.numberUnit') }}</span>
                            <!-- {{ $t('recharge.documentAmout',{num:`${curProduct.productNum}`}) }} -->
                        </p>
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block">
                            <!-- 金额：￥{{ myOrderData.rechargeMoney }}元 -->
                            {{ $t('recharge.amout1') }}<span> {{ getAmountUint(myOrderData.rechargeMoney) }} </span>
                            <CommonTip v-if="isNotarizationPay" content="公证书基础收费 1500元，每多一份合同 PDF公证额外付费 1000 元。"></CommonTip>
                        </p>
                        <p class="inline-block line">

                        </p>
                        <p class="inline-block danger">
                            <!-- 有效期至 --><i class="el-icon-warning"></i>{{ $t('recharge.validUntil') }} {{ curProduct.endWorkTime.split(' ')[0].replace(/-/g, '/') }}
                        </p>
                    </li>
                </ul>
            </div>
            <div v-if="isSupportDiscountCode" class="discount-code-container">
                <div class="top-tip">
                    <i class="el-icon-ssq-liwu"></i>
                    {{ $t('recharge.discountTip') }}
                </div>
                <div class="discount-code-wrapper">
                    <span class="left-title">{{ $t('recharge.discountCode') }}:</span>
                    <el-input
                        class="right-input"
                        :placeholder="$t('recharge.plsInputDiscountCode')"
                        clearable
                        size="small"
                        :disabled="discountDisabled"
                        v-model="discountCode"
                    >
                    </el-input>
                    <el-button :disabled="discountDisabled" class="btn-type-one" @click="handleVerifyDiscountCode"> {{ $t('companyFull.confirm') }} </el-button>
                </div>
            </div>
            <!-- 普通版支付 -->
            <div v-if="!getIsForeignVersion" class="pay-con clear">
                <div class="top-con">
                    <ul class="nav-list fl">
                        <template v-if="isIcbcDeveloper">
                            <li class="cursor-point fl" :class="{active : currentNavIndex === '1'}" data-index="1" @click="switchNav">工商银行</li>
                        </template>
                        <template v-else>
                            <li class="cursor-point fl" :class="{active : currentNavIndex === '1'}" data-index="1" @click="switchNav"><!-- 支付宝 -->{{ $t('recharge.aliPay') }}</li>
                            <li class="cursor-point fl" :class="{active : currentNavIndex === '2'}" data-index="2" @click="switchNav"><!-- 微信支付 -->{{ $t('recharge.wechatPay') }}</li>
                            <li class="cursor-point fl" :class="{active : currentNavIndex === '3'}" data-index="3" @click="switchNav"><!-- 银行转账 -->{{ $t('recharge.bankTransfer') }}</li>
                        </template>
                    </ul>
                    <div class="fr">
                        <span><!-- 热线 -->{{ $t('recharge.hotline') }}：400-993-6665</span>
                        <span>|</span>
                        <span><!-- 客服 -->{{ $t('recharge.customerService') }}：</span>
                        <a class="cursor-point el-icon-ssq-kefu" target="_blank" href="http://bestsign.udesk.cn/im_client/?web_plugin_id=23490"></a>
                    </div>
                </div>
                <div v-if="isIcbcDeveloper" class="bottom-con">
                    <div v-if="currentNavIndex === '1'" class="weixinpay-con">
                        <p class="recharge-account">{{ isEnterprise ? $t('recharge.rechareSubject') : $t('recharge.rechareAccout') }}<span class="invoice-tip">{{ $t('recharge.invoiceTip') }}</span>：<span>{{ rechargeAccount }}</span></p>
                        <p><!-- 金额： -->{{ $t('recharge.amout1') }}<span> {{ getAmountUint(myOrderData.rechargeMoney) }} </span></p>
                        <p>{{ $t('recharge.payValidTime') }}<span>{{ $t('recharge.within24h') }}</span></p>
                        <el-button @click="toIcbc">去工行支付</el-button>
                    </div>
                </div>
                <div v-else class="bottom-con">
                    <div v-if="currentNavIndex === '1'" class="weixinpay-con">
                        <p class="recharge-account">{{ isEnterprise ? $t('recharge.rechareSubject') : $t('recharge.rechareAccout') }}<span class="invoice-tip">{{ $t('recharge.invoiceTip') }}</span>：<span>{{ rechargeAccount }}</span></p>
                        <p><!-- 金额： -->{{ $t('recharge.amout1') }}<span>{{ getAmountUint(myOrderData.rechargeMoney) }}</span></p>
                        <p>{{ $t('recharge.payValidTime') }}<span>{{ $t('recharge.within24h') }}</span></p>
                        <div class="wechat-pay-qrcode">
                            <img class="qrcode-border" src="~pub-images/alipay-pay.png" alt="ali pay border">
                            <!-- 支付宝二维码是iframe -->
                            <div class="qrcode-img-con-ali" v-loading="aliImgSrcLoading">
                                <iframe class="qrcode-img-ali"
                                    :src="aliPayImg"
                                    ref="aliIframe"
                                    frameborder="0"
                                ></iframe>
                            </div>

                        </div>

                    </div>
                    <div v-if="currentNavIndex === '2'" class="weixinpay-con">
                        <p class="recharge-account">{{ isEnterprise ? $t('recharge.rechareSubject') : $t('recharge.rechareAccout') }}<span class="invoice-tip">{{ $t('recharge.invoiceTip') }}</span>：<span>{{ rechargeAccount }}</span></p>
                        <p><!-- 金额： -->{{ $t('recharge.amout1') }}<span>{{ getAmountUint(myOrderData.rechargeMoney) }}</span></p>
                        <p class="valid-time">{{ $t('recharge.payValidTime') }}<span>{{ $t('recharge.within24h') }}</span></p>
                        <div class="wechat-pay-qrcode">
                            <img class="qrcode-border" src="~pub-images/<EMAIL>" alt="wechat pay border">
                            <div class="qrcode-img-con" v-loading="weixinImgSrcLoading">
                                <img class="qrcode-img" :src="weixinImgSrc" v-if="!weixinImgSrcLoading" alt="wechat pay qrcode">
                            </div>
                        </div>
                    </div>
                    <div v-if="currentNavIndex === '3'">
                        <p class="recharge-account">{{ isEnterprise ? $t('recharge.rechareSubject') : $t('recharge.rechareAccout') }}<span class="invoice-tip">{{ $t('recharge.invoiceTip') }}</span>：<span>{{ rechargeAccount }}</span></p>
                        <p class="card-valid-time">{{ $t('recharge.payValidTime') }}
                            <span>{{ $t('recharge.within24h') }}</span>
                        </p>
                        <div class="self-help-con">
                            <p class="self-help-title"><!-- 您也可以自助转账到以下银行账号 -->{{ $t('recharge.selfTransferBankAccout') }}</p>
                            <div class="per-section inline-block section-one">
                                <p></p>
                            </div>
                            <div class="per-section inline-block section-two">
                                <p><!-- 户名 -->{{ $t('recharge.accountName') }}：杭州尚尚签网络科技有限公司上海分公司</p>
                                <p><!-- 账号 -->{{ $t('recharge.account') }}：***************</p>
                                <p><!-- 开户行 -->{{ $t('recharge.accountBank') }}：招商银行股份有限公司上海外滩支行</p>
                                <p><!-- 行号 -->{{ $t('recharge.lineNumber') }}：************</p>
                            </div>
                            <p class="per-section inline-block line"></p>
                            <div class="per-section inline-block section-three">
                                <p><!-- 温馨提示： -->{{ $t('recharge.rechareTip1.title') }}</p>
                                <p>① <!-- 请将订单号备注在付款信息里 -->{{ $t('recharge.rechareTip1.tip1') }}</p>
                                <p>② <!-- 转账成功后 -->{{ $t('recharge.rechareTip1.tip2') }}</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <!-- 日文版支付 -->
            <div class="pay-con-paypal clear" v-else>
                <div class="bottom-con--account">
                    <p class="recharge-account">
                        <label class="recharge-account__label">{{ $t('RechargeAccount.orders.main') }}:</label><span>{{ rechargeAccount }}</span>
                    </p>
                    <p class="recharge-account">
                        <label class="recharge-account__label">{{ $t('RechargeAccount.orders.money') }}:</label><span> {{ getAmountUint(myOrderData.rechargeMoney) }}</span>
                    </p>
                    <p class="recharge-account">
                        <label class="recharge-account__label">{{ $t('recharge.payValidTime') }}</label><span>{{ $t('recharge.within24h') }}</span>
                    </p>
                    <div class="recharge-paypal-wrap">
                        <div class="recharge-paypal">
                            <svg
                                t="*************"
                                class="icon"
                                viewBox="0 0 1024 1024"
                                version="1.1"
                                xmlns="http://www.w3.org/2000/svg"
                                p-id="13106"
                                width="500"
                                height="200"
                                transform="scale(1.3)"
                            >
                                <path
                                    d="M692.84 330.67H112.1a142.3 142.3 0 0 0-0.94 16.28v330.1c0 78 63.46 141.47 141.47 141.47h518.74c3.09 0 6.16-0.11 9.21-0.31V417.73c0-48.98-38.39-87.06-87.74-87.06z"
                                    fill="#BBF2FF"
                                    p-id="13107"
                                ></path>
                                <path
                                    d="M771.37 205.47c78 0 141.47 63.46 141.47 141.47v330.11c0 78-63.46 141.47-141.47 141.47H252.63c-78 0-141.47-63.46-141.47-141.47v-330.1c0-78 63.46-141.47 141.47-141.47h518.74m0-47.16H252.63C148.45 158.32 64 242.77 64 346.95v330.1c0 104.18 84.45 188.63 188.63 188.63h518.74c104.18 0 188.63-84.45 188.63-188.63v-330.1c0-104.18-84.45-188.63-188.63-188.63z"
                                    fill="#2087E7"
                                    p-id="13108"
                                ></path>
                                <path
                                    d="M926.7 405.88H99.05c-16.45 0-27.42 10.88-27.42 27.21s11 27.21 27.42 27.21H926.7c16.45 0 27.42-10.89 27.42-27.21s-11-27.21-27.42-27.21zM267.86 601.8H213c-16.45 0-27.42 10.88-27.42 27.2s11 27.21 27.42 27.21h54.84c16.46 0 27.42-10.89 27.42-27.21s-11-27.2-27.42-27.2zM535.78 601.8H371.26c-16.45 0-27.42 10.88-27.42 27.21s11 27.21 27.42 27.21h164.52c16.45 0 27.42-10.89 27.42-27.21s-11-27.21-27.42-27.21z"
                                    fill="#2087E7"
                                    p-id="13109"
                                ></path></svg><span class="pay-approach">{{ $t('RechargeAccount.orders.payApproachType') }}</span>
                            <p class="clip-path-tick"><i class="iconfont el-icon-ssq-qianyuewancheng" /></p>
                        </div>
                        <p class="pwd-by">
                            <span>{{ $t('RechargeAccount.orders.poweredBy') }}</span><img :src="paypalLogo" class="paypal-logo" />
                        </p>
                    </div>
                </div>
                <div class="bottom-con">
                    <div class="alipay-con">
                        <el-button class="btn-type-one" @click="toPay">
                            {{ $t('RechargeAccount.orders.chargeNow') }}
                        </el-button>
                    </div>
                </div>
            </div>
        </div>
        <div slot="pop-content">

        </div>
        <el-dialog
            :visible.sync="showSuccessDialog"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            class="pay-success-dialog"
            title="支付成功"
        >
            <div class="pay-success-dialog-con">
                <img :src="appletImg" alt="">
                <span>扫码添加微信小程序，下次购买更方便</span>
            </div>
            <div slot="footer">
                <el-button v-if="$route.query.pageFrom === 'send'" type="primary" @click="goBackToSend">继续发送合同
                </el-button>
                <el-button v-else type="primary" @click="goOrder">查看订单</el-button>
            </div>
        </el-dialog>
        <div class="ai-pay-success-modal" v-if="showAiSuccessDialog">
            <div class="ai-pay-success-body">
                <div class="content">
                    <img src="~pub-images/ai-pay-success.png" alt="支付成功">
                    <div class="join">
                        <div class="success">充值成功</div>
                        <div>您的账户已经成功充值<span class="blue">1份</span></div>
                        <div><span class="blue">{{ aiShowSecond }}秒</span>后跳转<span class="blue">风险判断</span>页面</div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</template>

<script>
import { goReturnUrl } from 'pub-utils/business/returnUrl.js';
import { getCurrentUser } from 'pub-utils/business/getCurrentUser.js';
import { mapState, mapGetters } from 'vuex';
export default {
    components: {

    },
    props: {
        isEnterprise: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            interval: 0,
            id: '',
            aliActionPath: '',
            weixinImgSrc: '',
            weixinImgSrcLoading: false,
            aliPayImg: '', // 支付宝支付img
            aliImgSrcLoading: false, // 支付宝支付二维码loading
            currentNavIndex: '1',
            myOrderData: {
                'rechargeRecordId': '',
                'rechargeMoney': '',
                'createTime': '',
                'status': '',
                'operaterType': '',
                'operaterTypeName': '',
                'orderId': '',
                'payTime': '',
                'billStatus': '',
                'payer': '',
                'packageName': '',
                'validityMonth': '',
                'products': [{
                    'productNum': '',
                    'productTypeName': '',
                    'endWorkTime': ' ',
                }],
            },
            paypalLogo: require('../img/paypal-logo.png'),
            discountCode: '', // 折扣码
            discountCount: '', // 送的合同份数
            discountDisabled: false, // 折扣码框不能输入
            pageFromMap: {
                'console': '控制台',
                'sign': '签署流程',
                'send': '发件流程',
                'userCenter': '个人中心',
            },
            enterPageDate: '', // 进页面时间
            showSuccessDialog: false,
            showAiSuccessDialog: false,
            aiShowSecond: 3,
            appletImg: require(`pub-images/sign-applet-${process.env.NODE_ENV === 'production' ? 'product' : 'test'}.png`),
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
        ...mapGetters(['getAuthStatus', 'getIsJa', 'getIsForeignVersion', 'getIsUae']),
        rechargeAccount() {
            return getCurrentUser(this.$store.state.commonHeaderInfo, 0);
        },
        userName() {
            return this.commonHeaderInfo.platformUser.fullName;
        },
        isNotAuth() {
            return this.getAuthStatus !== 2;
        },
        curProduct() {
            return this.myOrderData.products && this.myOrderData.products[0];
        },
        // 是否是充值 成员个数
        isRechargeMember() {
            return this.curProduct && Number(this.myOrderData.products[0].productType) === 13;
        },
        // 是否是充值 高级功能
        isAdvancedFeature() {
            return this.curProduct && Number(this.myOrderData.products[0].productType) === 14;
        },
        lang() {
            return this.$i18n.locale;
        },
        calProductName() { // 显示的产品名称
            if (this.isAdvancedFeature) {
                let showProductName = '';
                this.myOrderData.products.forEach((element, index) => {
                    showProductName += `${index > 0 ? '、' : ''}${element.productName}`;
                });
                return showProductName;
            } else if (this.isOnlineCertify) {
                return this.curProduct.productTypeName;
            }

            return `${this.curProduct.productTypeName} ${this.isRechargeMember ?   '' : this.$t('recharge.document')}`;
        },
        // 是否是充值合同
        isSupportDiscountCode() {
            const code = this.$route.query.isSupportDiscountCode;
            const rechargeScene = this.$route.query.rechargeScene; // 官网跳过来，买折扣套餐
            return (code && code === '1') || (rechargeScene && rechargeScene === 'isDiscountCode');
        },
        isOnlineCertify() {
            return this.$route.query.onlineCertify === 'true';
        },
        isAiPay() {
            return this.$route.query.packageType === 'ai';
        },
        isIcbcDeveloper() {
            return this.commonHeaderInfo.isICBCDeveloper;
        },
        isNotarizationPay() { // 公证书支付
            return this.curProduct && Number(this.myOrderData.products[0].productType) === 20;
        },
    },
    methods: {
        getAmountUint(money) {
            if (this.getIsJa) {
                return `￥${money} ${this.$t('RechargeAccount.orders.jaMonyUnit')}`;
            } else if (this.getIsUae) {
                return `$${money} USD`;
            }
            return `￥${money} ${this.$t('RechargeAccount.orders.monyUnit')}`;
        },
        toIcbc() {
            window.open('https://corporbank.icbc.com.cn/icbc/corporbank/epassclogon.jsp?ItemNo=E9638');
        },
        async goToAlipay() {
            const t = this.isEnterprise ? this.$t('recharge.ent') : this.$t('recharge.person');
            const  accountTip = `${this.rechargeAccount}（${this.isNotAuth ? this.$t('recharge.notAuth') : t}）`;
            const moreTip = this.isNotAuth ? this.$t('recharge.curRechareDialog.notAuthTip') : this.$t('recharge.curRechareDialog.tip');
            await this.$alert(`<p class="recharge-account">
                            ${this.isEnterprise ? this.$t('recharge.curRechareDialog.subject') : this.$t('recharge.rechareAccout')}：<span>${accountTip}</span>
                        </p>
                        <p>
                            ${this.$t('recharge.curRechareDialog.amount')}：<span>￥${this.myOrderData.rechargeMoney}${this.$t('recharge.yuan')}</span>
                        </p>
                        <p class="red">${moreTip}</p>
            `,
                              this.$t('recharge.tip'), {
                                  dangerouslyUseHTMLString: true,
                                  customClass: 'recharge-account-dialog',
                              });
            window.open(this.aliActionPath);
        },
        // 验证折扣码
        handleVerifyDiscountCode() {
            if (!this.discountCode) {
                this.discountCount = '';
                return;
            }
            if (this.discountCode.match(/\D/)) {
                this.$MessageToast.error(this.$t('recharge.discountCodeErrorTip'));
                return;
            }
            const packageId = this.$route.query.packageId;
            this.$http.post('ents/charging/save-discount-order', {
                id: this.discountCode,
                packageId: packageId,
                productId: this.curProduct.productId,
            }).then((res) => {
                if (res.data) {
                    this.discountCount = res.data.value;
                    this.discountDisabled = true;
                }
            }).catch(() => {
                this.discountCount = '';
            });
        },
        switchNav(event) {
            this.weixinImgSrcLoading = true;
            const dom = event.target;
            const iconMap = {
                '1': '支付宝',
                '2': '微信支付',
                '3': '银行转账',
            };
            this.currentNavIndex = dom.getAttribute('data-index');
            this.handleSensorsTrack('btnClick', {
                icon_name: iconMap[this.currentNavIndex],
            });
            if (this.currentNavIndex === '2') {
                this.$http.post(`/ents/wechat/request/${this.myOrderData.orderId}`)
                    .then(res => {
                        if (res.data) {
                            this.weixinImgSrc = `data:image/png;base64,${res.data}`;
                            this.weixinImgSrcLoading = false;
                        }
                    });
            } else if (this.currentNavIndex === '1') {
                this.aliPayImg  = '';
                this.getAlipayQrCode();
            }
            this.checkPayResult();
        },
        handleSensorsTrack(type, params = {}) {
            const eventName = 'Ent_CommodityDetail_PageView';
            const eventMap = {
                btnClick: 'Ent_CommodityDetail_BtnClick', // 云平台_商品详情页_按钮点击
                pageView: 'Ent_CommodityDetail_PageView', // 云平台_商品详情页_页面浏览
                urlResult: 'Ent_OrderSubmit_Result', // 云平台_订单支付_结果
                pageLeave: 'Ent_CommodityDetail_PageLeave', // 云平台_商品详情页_页面离开
            };
            const { originPrice, pageFrom } = this.$route.query;
            const { productId, productName, productNum } = this.curProduct;
            const eventParams = {
                previous_page_name: this.pageFromMap[pageFrom],
                commodity_id: productId, // 商品id
                commodity_name: productName, // 商品名称
                commodity_price: originPrice, // 商品原价格
                purchase_num: productNum, // 采购数量
                purchase__price: this.myOrderData.rechargeMoney, // 采购价格（支付的金额）
            };
            this.$sensors && this.$sensors.track({
                eventName: eventMap[type] || eventName,
                eventProperty: {
                    ...eventParams,
                    ...params,
                },
            });
        },
        // 获取支付宝支付img
        getAlipayQrCode() {
            if (this.isIcbcDeveloper) {
                return this.$http.get(`/ents/icbc/request/${this.myOrderData.orderId}`).then(() => {

                });
            }
            this.aliImgSrcLoading = true;
            this.$http.get(`/ents/alipay/request/${this.myOrderData.orderId}`).then(res => {
                if (res.data) {
                    this.aliPayImg = res.data;
                    this.iframeLoad();
                }
            });
        },
        // 支付宝二维码loading监听
        iframeLoad() {
            this.aliImgSrcLoading = true;
            const iframe = this.$refs.aliIframe;
            // 兼容处理
            if (iframe.attachEvent) {
                // IE
                iframe.attachEvent('onload', () => {
                    this.aliImgSrcLoading = false;
                });
            } else {
                // 非IE
                iframe.onload = () => {
                    this.aliImgSrcLoading = false;
                };
            }
        },
        // 日文版支付
        toPay() {
            // window.open(`${window.location.origin}/ents/ignore/paypal/request?orderId=${this.id}`);
            window.open(`${window.location.origin}/ents/paypal/request/${this.id}`);
        },
        handleAiPaySuccess(){
            this.showAiSuccessDialog = true;
            let times = 3;
            let interval = setInterval(() => {
                if (times > 0) {
                    times--;
                    this.aiShowSecond = times;
                    console.log(times);
                } else {
                    clearInterval(interval); // 清除定时器，停止计时
                    this.showAiSuccessDialog = false;
                    this.$router.push('/hubble-apply/risk-judgement');
                }
            }, 1000);
        },
        checkPayResult() {
            this.interval && clearInterval(this.interval);
            let times = 180;
            this.interval = setInterval(() => {
                times--;
                const url = `/ents/charging/rechargeRecord/order/${this.id}`;
                this.$http.get(url)
                    .then(res => {
                        if (res.data.status === 1) {
                            clearInterval(this.interval);
                            this.$MessageToast({
                                message: this.$t('recharge.paySuccess'), // 支付成功
                                type: 'success',
                            });
                            const paMap = {
                                '1': '支付宝',
                                '2': '微信支付',
                                '3': '银行转账',
                            };
                            const isUseDiscount = this.discountCount.length > 0;
                            const { originPrice } = this.$route.query;
                            const disCountAmount = originPrice ? (parseFloat(originPrice) - parseFloat(this.myOrderData.rechargeMoney)) : 0;
                            this.handleSensorsTrack('urlResult', {
                                order_id: this.id,
                                pay_type: paMap[this.currentNavIndex],
                                is_use_discount: isUseDiscount, // 是否使用优惠券
                                discount_name: isUseDiscount ? '折扣码' : '', // 优惠券名称
                                discount_amount: disCountAmount.toFixed(Number.isInteger(disCountAmount) ? 0 : 2), // 优惠金额
                                is_success: true,
                                request_url: url,
                            });
                            const rechargeReturnUrl = this.$cookie.get('rechargeReturnUrl');
                            if (rechargeReturnUrl) {
                                goReturnUrl(rechargeReturnUrl);
                            } else if (this.isOnlineCertify) {
                                this.$router.push('/usercenter/apply/evidence');
                            } else if (this.isAiPay) {
                                this.handleAiPaySuccess();
                            } else {
                                this.showSuccessDialog = true;
                            }
                        } else if (times <= 0) {
                            clearInterval(this.interval);
                        }
                    });
            }, 2000);
        },
        goOrder() {
            if (this.isEnterprise) {
                this.$router.push('orders');
            } else {
                this.$router.push('/usercenter/recharge');
            }
        },
        goBackToSend() {
            const pageUrl = sessionStorage.getItem('chargeFromSendLink');
            location.href = pageUrl || '/sign-flow/send/prepare';
        }
    },
    beforeMount: function() {
        this.id = this.$route.query.id;
        if (this.id && this.id !== '') {
            this.$http.get(`/ents/charging/rechargeRecord/order/${this.id}`).then(async  res => {
                if (res.data) {
                    this.myOrderData = res.data;
                    if (this.myOrderData.remark) {
                        this.discountDisabled = true;
                    }
                    !this.getIsForeignVersion && this.getAlipayQrCode();
                    this.handleSensorsTrack('pageView', {});
                    // this.aliActionPath = `/ents/alipay/request/${this.myOrderData.orderId}?access_token=${this.$cookie.get('access_token')}`;
                    this.checkPayResult();
                }
            });
        }
    },
    mounted() {
        // SAAS-39258: 充值页面做单独跳转界面用，进来后修改为全屏
        if (this.$route.query?.noValidPermission === 'true') {
            const parentEl = document.querySelector('.console-container');
            if (parentEl && !parentEl.className.includes('content-full-screen')) {
                parentEl.classList.add('content-full-screen');
            }
        }
    },
    created() {
        this.enterPageDate = Date.parse(new Date());
    },
    beforeDestroy() {
        if (this.$route.query?.noValidPermission === 'true') {
            const parentEl = document.querySelector('.console-container');
            if (parentEl) {
                parentEl.classList.remove('content-full-screen');
            }
        }
    },
    destroyed() {
        this.handleSensorsTrack('pageLeave', {
            $event_duration: (Date.parse(new Date()) - this.enterPageDate) / 1000,
        });
        window.clearInterval(this.interval);
    },
};
</script>

<style lang="scss">
.console-container.content-full-screen {
    .console-slider {
        width: 0;
    }
    .console-content {
        margin-left: 0 !important;
        [dir=rtl] & {
            margin-right: 0 !important;
        }

    }
}

.order-pay-con {
    .title {
        height: 55px;
        line-height: 55px;
        padding-left: 30px;
        border-bottom: 1px solid $--border-color-light;
        font-size: 16px;
        [dir=rtl] & {
            padding-right: 30px;
            padding-left: 0;
        }
        i {
            display: inline-block;
            vertical-align: middle;
            color: $--color-text-regular;
            font-size: 20px;
        }
        h1 {
            display: inline-block;
            padding-left: 5px;
            color: $--color-black;
            font-size: 16px;
            [dir=rtl] & {
                padding-right: 5px;
                padding-left: 0;
            }
        }
    }
    .order-con, .pay-con {
        width: 95%;
        margin-left: $main-padding-left;
        border: 1px solid $--border-color-lighter;
        [dir=rtl] & {
            margin-right: $main-padding-left;
            margin-left: 0;
        }
    }
    .order-pay-tips {
        color: $--color-text-primary;
        font-size:12px;
    }
    .order-con {
        margin-top: 25px;
        .order-con-title {
            height: 40px;
            line-height: 40px;
            padding-left: 20px;
            border-bottom: 1px solid $--border-color-lighter;
            background-color: $--background-color-regular;
            font-size: 12px;
            [dir=rtl] & {
                padding-right: 20px;
                padding-left: 0;
            }
        }

        .per-order-detail {
            min-height: 43px;
            max-height: 60px;
            font-size: 12px;
            p {
                margin-top: 10px;
                vertical-align: middle;
                text-align: center;
            }
            .line {
                height: 30px;
                border-right: 1px solid $--border-color-lighter;
                [dir=rtl] & {
                    border-right: none;
                    border-left: 1px solid $--border-color-lighter;
                }
            }
            p:nth-child(1) {
                width: 15%;
            }
            p:nth-child(3) {
                width: 15%;
            }
            p:nth-child(5) {
                width: 15%;
            }
            p:nth-child(7) {
                width: 15%;
            }
            p:nth-child(9) {
                width: 35%;
            }
            .danger {
                color: $--color-danger;
            }
        }
    }
    .pay-con {
        margin-top: 24px;

        .cursor-point{
            cursor: pointer;
        }

        .top-con {
            height: 38px;
            line-height: 38px;
            background-color: $--background-color-regular;
            border-bottom: 1px solid $--border-color-lighter;
            font-size: 14px;
            color: $--color-text-regular;
            .fr {
                span:nth-child(2) {
                    display: inline-block;
                    margin-left: 15px;
                    margin-right: 15px;
                }
                a {
                    margin-right: 15px;
                    font-size: 21px;
                    color: $--color-text-secondary;
                    [dir=rtl] & {
                        margin-left: 15px;
                        margin-right: 0;
                    }
                    &:hover {
                        color: $--color-primary-light-1;
                    }
                }
            }
            [dir=rtl] & {
                .fl {
                    float: right;
                }
                .fr {
                    float: left;
                }
            }
        }
        .nav-list {
            width: 450px;
            li {
                width: 150px;
                border-left: 1px solid $--border-color-lighter;
                border-right: 1px solid $--border-color-lighter;
                text-align: center;
                &.active {
                    border-left: none;
                    border-right: none;
                    border-top: 2px solid $--color-primary-light-1;
                    background-color: $--color-white;
                    color: $--color-primary-light-1;
                }
            }
        }
        .bottom-con {
            padding-left: 26px;
            padding-bottom: 26px;
            [dir=rtl] & {
                padding-right: 26px;
                padding-left: 0;
            }
            .recharge-account{
                margin-top: 30px;
                font-size: 14px;
                color: $--color-text-regular;
                .invoice-tip{
                   font-size: 12px;
                color: $--color-text-regular;
                    font-weight: normal;
                }
                span {
                    font-size: 14px;
                    color: $--color-danger;
                    font-weight: bold;
                }
                &-red {
                    color: $--color-danger;
                }
            }
            .icbc-pay-tip{
                margin-top: 20px;
                color: $--color-danger !important;
            }
            .alipay-con, .weixinpay-con{
                p{
                    font-size: 14px;
                    color: $--color-text-regular;
                    span {
                        font-size: 14px;
                        color: $--color-danger;
                        font-weight: bold;
                    }
                }
            }
            .alipay-con {
                .alipay-icon {
                    margin-top: 30px;
                    width: 174px;
                    height: 48px;
                    background-image: url(../img/alipay.png);
                    background-size: 100%;
                    background-repeat: no-repeat;
                }
                .el-button {
                    width: 175px;
                    height: 40px;
                    margin-top: 26px;
                    span {
                        font-size: 18px;
                        color: $--color-white;
                    }
                }
            }
            .weixinpay-con {
                p:nth-child(4) {
                    width: 180px;
                    height: 180px;
                }
                .wechat-pay-qrcode{
                    position: relative;
                    margin-top: 20px;

                    .qrcode-border{
                        height: 200px;
                    }
                    .qrcode-img-con{
                        position: absolute;
                        width: 130px;
                        height: 130px;
                        left: 12px;
                        top: 10px;
                        [dir=rtl] & {
                            left: auto;
                            right: 12px;
                        }
                    }
                    .qrcode-img-con-ali{
                        position: absolute;
                        width: 130px;
                        height: 130px;
                        left: 12px;
                        top: 10px;
                        background-color: white;
                        [dir=rtl] & {
                            left: auto;
                            right: 12px;
                        }
                    }
                    .qrcode-img-ali{
                        width: 130px;
                        height: 130px;
                        padding-top: 14px;
                        padding-left: 14px;
                        [dir=rtl] & {
                            padding-left: 14px;
                            padding-right: 14px;
                        }
                    }

                    .qrcode-img{
                        width: 130px;
                    }
                }
            }
            .card-valid-time {
                color: $--color-text-regular;
                font-size: 14px;
                span {
                    color: $--color-danger;
                    font-weight: bold;
                }
            }
        }
    }
    .pay-con-paypal {
        width: 95%;
        margin-left: $main-padding-left;
        border: 1px solid #eee;
        margin-top: 24px;
        [dir=rtl] & {
            margin-left: 0;
            margin-right: $main-padding-left;
        }
        .bottom-con--account {
            display: flex;
            flex-direction: column;
            align-items: flex-start;
            justify-content: flex-start;
            .recharge-account {
                margin-top: 5px;
                font-size: 14px;
                color: #666;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-start;
                label {
                    margin-right: 20px;
                    [dir=rtl] & {
                        margin-left: 20px;
                        margin-right: 0;
                    }
                }
                span {
                    font-size: 14px;
                    color: #FF5500;
                    font-weight: bold;
                }
            }
        }
        .recharge-paypal-wrap{
            .recharge-paypal {
                box-sizing: content-box;
                padding: 10px 17px;
                position: relative;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-start;
                border: 1px solid #127FD2;
                margin-top: 20px;
                margin-bottom: 5px;
                .pay-approach {
                    font-size: 14px;
                    line-height: 14px;
                    color: #333333;
                    font-weight: 600;
                    margin-left: 13px;
                    [dir=rtl] & {
                        margin-left: 0;
                        margin-right: 13px;
                    }
                }
                .clip-path-tick{
                    position: absolute;
                    width: 16px;
                    height: 16px;
                    bottom: 0;
                    right: 0;
                    clip-path: polygon(100% 0, 100% 100%, 0 100%);
                    background-color: #127FD2;
                    text-align: center;

                    .iconfont.el-icon-ssq-qianyuewancheng {
                        color: #fff;
                        font-size: 12px;
                        margin-left: 2px;
                        transform: scale(0.8);
                    }
                }
            }
            .pwd-by {
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-start;
                &>span{
                    font-size: 12px;
                    color: #666666;
                    font-weight: 400;
                }
                &>img.paypal-logo{
                    height: 10px;
                    width: auto;
                    display: block;
                    margin-left: 10px;
                }
            }
        }

        .bottom-con .el-button.btn-type-one {
            width: 174px;
            height: 42px;
            margin-top: 26px;
            box-sizing: border-box;

            span {
                font-size: 18px;
                color: #fff;
            }
        }

    }
    .pay-con-paypal.clear {
        border: 0;
    }
    .self-help-con {
        color: $--color-text-primary;
        .self-help-title {
            margin-top: 30px;
            margin-bottom: 33px;
            font-size: 14px;
            color: $--color-text-primary;
            i {
                color: $--color-primary-light-2;
            }
        }
        .per-section {
            vertical-align: middle;
        }
        .section-one {
            width: 171px;
            height: 48px;
            margin-right: 45px;
            background-image: url(../img/bank.png);
            background-size: 100%;
            background-repeat: no-repeat;
            [dir=rtl] & {
                margin-right: 0;
                margin-left: 45px;
            }
        }
        .section-two {
            font-size: 12px;
            p {
                margin-bottom: 6px;
                &:last-child {
                    margin-bottom: 0;
                }
            }
        }
        .section-three {
            font-size: 12px;
            p {
                margin-top: 6px;
            }
            p:nth-child(1) {
                font-size: 12px;
                margin-top: 0;
            }
        }
        .line {
            height: 80px;
            margin-left: 30px;
            margin-right: 40px;
            border-right: 1px solid $--border-color-lighter;
            [dir=rtl] & {
                margin-left: 40px;
                margin-right: 30px;
            }
        }
    }
}
.recharge-account-dialog {
    .red {
        color: red;
        margin-top: 10px;
        line-height: 1.4;
    }
}
.discount-code-container{
    margin: 30px 30px;
    .top-tip{
        font-size: 12px;
        padding: 5px 5px;
        border-radius: 5px;
        margin-bottom: 10px;
        background-color: rgba(255,53,71,0.07);
        color:rgb(255,53,71);
    }
    .discount-code-wrapper{
        display: flex;
        align-items: center;
        .left-title{
            font-size: 14px;
            padding-right: 5px;
            color:  $--color-text-primary;
            min-width: 50px;
            [dir=rtl] & {
                padding-right: 0;
                padding-left: 5px;
            }
        }
        .right-input{
            width: 300px;
            margin-right: 20px;
            [dir=rtl] & {
                margin-right: 0;
                margin-left: 20px;
            }
        }
        .el-input__inner{
            height: 30px;
        }
    }
}
.pay-success-dialog{
    .el-dialog{
        width: 515px;
    }
    .el-dialog__header{
        height: auto !important;
    }
    &-con{
        display: flex;
        align-items: center;

        img{
            width: 160px;
            height: 160px;
            margin-right: 20px;
            [dir=rtl] & {
                margin-right: 0;
                margin-left: 20px;
            }
        }
        span{
            margin-right: 20px;
            display: inline-block;
            line-height: 24px;
            font-size: 16px;
            [dir=rtl] & {
                margin-right: 0;
                margin-left: 20px;
            }
        }
    }

    .el-dialog__footer{
        text-align: center;
    }
}
   .ai-pay-success-modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.6);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 9999;
    // .close-icon {
    //     width: 36px;
    //     height: 36px;
    //     border-radius: 18px;
    //     cursor: pointer;
    //     overflow: hidden;
    //     img {
    //         width: 100%;
    //         height: 100%;
    //     }
    // }
    .ai-pay-success-body {
        display: flex;
        align-items: center;
        flex-direction: column;
        .content {
            display: flex;
            justify-content: center;
            align-items: center;
            width: 400px;
            border-radius: 32px;
            margin-bottom: 35px;
            background-size: cover;
            position: relative;
            img{
                width: 100%;
            }
            .join{
                position: absolute;
                // display: flex;
                text-align: center;
                top: 180px;
                font-size: 16px;
                font-weight: 400;
                color: $--color-text-regular;
                div{
                    margin: 10px 0;
                }
                .success{
                    font-size: 24px;
                    font-weight: 500;
                    color: $--color-text-primary;
                    margin-bottom: 25px;
                }
                .blue{
                    color: $--color-primary;
                }
            }
        }

    }
}
</style>
