<template>
    <div v-loading.fullscreen.lock="loading">
        <div class="recharge-con">
            <div class="title">
                <i class="el-icon-ssq-qiandai"></i>
                <h1><!-- 充值管理 -->{{ $t('rechargePackage.manage') }}</h1>
            </div>
            <div class="packages-con">
                <div class="packages-con-title">
                    <div class="con">
                        <!-- 选择套餐 -->{{ $t('rechargePackage.selectPackage') }}
                        <div class="recharge-service-info fr" v-if="!getIsUae">
                            <p><!-- 1000份以上请咨询客服： -->{{ $t('rechargePackage.tip1') }} 400-993-6665</p>
                            <span class="line">&nbsp;&nbsp;|&nbsp;&nbsp;</span>
                            <p>
                                <span><!-- 在线客服： -->{{ $t('rechargePackage.onlineService') }}</span>
                                <a class="cursor-point el-icon-ssq-kefu" target="_blank" href="http://bestsign.udesk.cn/im_client/?web_plugin_id=23490"></a>
                            </p>
                        </div>
                    </div>
                </div>
                <div class="per-type-package company-package clear" v-if="currentPackageType !== 'private'">
                    <div class="per-type-package-title fl" v-show="companyPackageList.length">
                        <i class="el-icon-ssq-duigonghetong"></i>
                        <p>{{ publicTitle }}</p>
                    </div>
                    <div class="package-list">
                        <Item
                            v-for="perPackage in companyPackageList"
                            :packageType="currentPackageType"
                            :perPackage="perPackage"
                            :key="perPackage.productPackageId"
                            :data-id="perPackage.productPackageId"
                            @pay="(productPackageId, isNumChangeable,num,finalOriginPrice) => gotoPay(productPackageId, isNumChangeable,num,finalOriginPrice, 'public')"
                        ></Item>
                    </div>
                </div>
                <div class="per-type-package personal-package clear" v-if="!['public','ai'].includes(currentPackageType)">
                    <div class="per-type-package-title fl" v-show="personalPackageList.length">
                        <i class="el-icon-ssq-duisihetong"></i>
                        <p>{{ HOST_ENV == 'CCB' ? $t('rechargePackage.ccbPrivateContract') : $t('rechargePackage.privateContract') }}</p>
                    </div>
                    <div class="package-list">
                        <Item
                            v-for="perPackage in personalPackageList"
                            :perPackage="perPackage"
                            :key="perPackage.productPackageId"
                            :data-id="perPackage.productPackageId"
                            @pay="(productPackageId, isNumChangeable,num,finalOriginPrice) => gotoPay(productPackageId, isNumChangeable,num,finalOriginPrice, 'private')"
                        ></Item>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script>
import Item from './Item.vue';
import { mapGetters } from 'vuex';
export default {
    components: {
        Item,
    },
    data() {
        return {
            HOST_ENV: this.GLOBAL.HOST_ENV,
            companyPackageList: [],
            personalPackageList: [],
            currentPackageType: this.$route.query.packageType || '',
            loading: false,
            pageFrom: this.$route.query.pageFrom || '',
            contractId: this.$route.query.contractId || '',
        };
    },
    computed: {
        ...mapGetters(['getUserType', 'getIsUae']),
        isEnt() {
            return this.getUserType === 'Enterprise';
        },
        publicTitle() {
            if (this.HOST_ENV === 'CCB') {
                return this.$t('rechargePackage.ccbPublicContract');
            }
            if (this.currentPackageType === 'ai') {
                return this.$t('recharge.ai');
            }
            return this.$t('rechargePackage.publicContract');
        },
    },
    methods: {
        checkEntChannel() {
            return this.$http('/ents/channel/check-ent-channel');
        },
        gotoPay: function(id, chooseNum, chooseContractNum, finalOriginPrice, packageType) {
            let  perPackage = {};
            // ai套餐购买
            if (this.currentPackageType === 'ai') {
                perPackage = this.companyPackageList.find(p => p.productPackageId === id);
                this.$http.post(`/ents/charging/aiCombinationPackage/${id}/ordering`, {}).then((res) => {
                    if (res.data && res.data.rechargeRecordId) {
                        /**
                         * @desc  以下字段只为埋点功能专用
                         * pageFrom 支付（前向页面名称）
                         * originPrice：原价
                         */
                        this.$router.push(`pay?id=${res.data.orderId}&packageId=${id}&isSupportDiscountCode=${this.isEnt ? perPackage.isDiscountCode : '0'}&pageFrom=${this.pageFrom}&originPrice=${finalOriginPrice}&packageType=${this.currentPackageType}`);
                    }
                });
                return;
            }

            // 常规合同套餐购买
            if (packageType === 'public') {
                perPackage = this.companyPackageList.find(p => p.productPackageId === id);
            } else {
                perPackage = this.personalPackageList.find(p => p.productPackageId === id);
            }
            this.loading = true;
            const paramsMap = { contractId: this.contractId };
            this.$http
                .post(`/ents/charging/package/${id}/ordering`, null,
                      {
                          params: chooseNum ? { ...paramsMap, amount: chooseContractNum } : paramsMap,
                      },
                )
                .then((res) => {
                    if (res.data && res.data.rechargeRecordId) {
                        /**
                         * @desc  以下字段只为埋点功能专用
                         * pageFrom 支付（前向页面名称）
                         * originPrice：原价
                         */
                        this.$router.push(`pay?id=${res.data.orderId}&packageId=${id}&isSupportDiscountCode=${this.isEnt ? perPackage.isDiscountCode : '0'}&pageFrom=${this.pageFrom}&originPrice=${finalOriginPrice}&packageType=${this.currentPackageType}`);
                    }
                }).catch(() => {
                    this.loading = false;
                });
        },
    },
    created: async function() {
        if (this.currentPackageType === 'ai') {
            const { data } = await this.$http.get('/ents/ignore/charging/ai-combination-package');
            this.companyPackageList = data;
            return;
        }
        const { data: { entChannel,  registerChannel } } = await this.checkEntChannel();
        // 两个字段都为false就调用ignore/charging/package接口，其他情况调用/ents/charging/package/channel接口
        let packageUrl = (!entChannel && !registerChannel)
            ? '/ents/ignore/charging/package'
            : '/ents/charging/package/channel';
        if (this.HOST_ENV === 'CCB') {
            packageUrl = '/ents/charging/isv/package';
        }
        const { data: { hasPurchaseFirstPackageToC,  hasPurchaseFirstPackageToB } } = await this.$http('/ents/charging/check-first-purchase-package');
        // 查询套餐详情
        this.$http.get(`${packageUrl}`, { params: {
            entChannel: entChannel,
            registerChannel: registerChannel,
            clientId: this.$store.state.commonHeaderInfo?.extendFields?.clientId ?? '',
            contractId: this.contractId,
        } }).then((res) => {
            const resData = res.data;
            if (resData && resData.length > 0) {
                resData.forEach((val) => {
                    if (this.HOST_ENV === 'official') {
                        // 官网
                        if (val.productType === 2) {
                            // 对私
                            if (val.isFirstPurchase && (hasPurchaseFirstPackageToC || !this.isEnt)) {
                                return;
                            }
                            this.personalPackageList.push(val);
                        } else {
                            if (val.isFirstPurchase && (hasPurchaseFirstPackageToB || !this.isEnt)) {
                                return;
                            }
                            this.companyPackageList.push(val);
                        }
                    } else if (this.HOST_ENV === 'CCB') {
                        // 建行
                        if (val.productType === 8) {
                            // 8  对私 9 对公
                            this.personalPackageList.push(val);
                        } else {
                            this.companyPackageList.push(val);
                        }
                    }
                });
            }
        });
    },
    destroyed() {
        this.loading = false;
    },
};
</script>

<style lang="scss">
.recharge-con {
	.title {
		height: 55px;
		line-height: 55px;
		padding-left: 30px;
		border-bottom: 1px solid $--border-color-light;
		font-size: 16px;
        [dir=rtl] & {
			padding-right: 30px;
			padding-left: 0;
		}
		h1 {
			display: inline-block;
			padding-left: 5px;
			color: $--color-black;
			font-size: 16px;
            [dir=rtl] & {
                padding-left: 0;
                padding-right: 5px;
            }
		}
		i {
			display: inline-block;
			vertical-align: middle;
			color: $--color-text-regular;
			font-size: 20px;
		}
	}
	.packages-con {
		width: 100%;
		.packages-con-title {
			width: 100%;
			height: 40px;
			padding-left: $main-padding-left;
			line-height: 40px;
			background-color: $--background-color-regular;
			color: $--color-text-primary;
			font-weight: bold;
            [dir=rtl] & {
                padding-right: $main-padding-left;
                padding-left: 0;
            }
			.con {
				width: 100%;
				font-size: 14px;
			}
		}
		.per-type-package {
			display: flex;
			width: 98%;
			// height: 157px;
			// padding-left: $main-padding-left;
			.per-type-package-title{
				display: flex;
				border: 1px solid #DDDDDD ;
				width:20%;
				align-content: center;
				justify-content: center;
				align-items: center;
				font-size: 18px;
			}
			.package-list {
				display: flex;
				width:80%;
				flex-wrap: wrap;
				div:nth-child(n+5){
					border-top:0px;
				}
				.per-package {
				padding: 20px;
				position: relative;
				border-top: 1px solid $--border-color-light;
				border-right: 1px solid $--border-color-light;
				border-bottom: 1px solid $--border-color-light;
                [dir=rtl] & {
                    border-right: 0px;
                    border-left: 1px solid $--border-color-light;
                }
				p:nth-child(2) {
					height: 45px;
					line-height: 45px;
					font-size: 16px;
			}
				p:nth-child(3) {
					display: none;
					width: 70%;
					margin-left: 15%;
					border-top: 1px solid $--border-color-light;
			}
				.per-package__price {
					margin-top: 5px;
					margin-bottom: 12px;
					span {
						font-size: 24px;
						color: $--color-danger;
			}
					del {
						color: $--color-text-secondary;
						font-size: 12px;
			}
				}
				p:nth-child(5),
				p:nth-child(6) {
					color: $--color-text-regular;
					font-size: 12px;
					margin-bottom: 5px;
				}
				p:nth-child(5) {
					margin-bottom: 5px;
					font-size: 12px;
				}
				}
			}
			.per-package {
				position: relative;
				.first-buy{
					position: absolute;
					left: 0px;
					top: 0px;
					padding: 2px 6px;
					border-radius: 0px 0px 10px 0px;
					color: #fff;
					font-size: 12px;
					background: linear-gradient(58.49deg, #ff2e4a 34.16%, #ffae00 133.44%);
                    [dir=rtl] & {
                        right: 0px;
                        left: auto;
                    }
				}
				.activity{
					background: linear-gradient(to right, #ff0083, #fd0e56) !important;
				}
				.step-package{
					position: absolute;
					left: 0px;
					top: 0px;
					padding: 2px 6px;
					border-radius: 0px 0px 10px 0px;
					color: #fff;
					font-size: 12px;
					background: linear-gradient(58.49deg, #ff2e4a 34.16%, #ffae00 133.44%);
                    [dir=rtl] & {
                        right: 0px;
                        left: auto;
                    }
				}
				width: 25%;
				position: relative;
				border-top: 1px solid $--border-color-light;
				border-right: 1px solid $--border-color-light;
				border-bottom: 1px solid $--border-color-light;
                [dir=rtl] & {
                    border-left: 1px solid $--border-color-light;
                    border-right: none;
                }
				.hover-border {
					display: none;
					position: absolute;
					top: 0;
					left: 0;
					width: 100%;
					height: 100%;
					border: 1px solid $--color-primary-light-1;
				}
				&:hover .hover-border {
					display: block;
				}
				p:nth-child(2) {
					height: 45px;
					line-height: 45px;
					font-size: 18px;
				}
				p:nth-child(3) {
					width: 70%;
					margin-left: 15%;
					border-top: 1px solid $--border-color-light;
                    [dir=rtl] & {
                        margin-left: 0;
                        margin-right: 15%;
                    }
				}
				p:nth-child(4) {
					margin-top: 15px;
					margin-bottom: 12px;
					span {
						font-size: 24px;
						color: $--color-danger;
					}
					del {
						color: $--color-text-secondary;
						font-size: 12px;
					}
				}
				p:nth-child(5),
				p:nth-child(6), .min-grey {
					color: $--color-text-regular;
					font-size: 12px;
				}
				p:nth-child(5) {
					margin-bottom: 5px;
					font-size: 12px;
					.per-package-give {
						margin: 0;
						color:  $--color-danger;
						background: transparent;
						padding: 0;
					}
				}
				.per-package-give {
					font-size: 12px;
					// color:  $--color-danger;
					// background: $--color-danger-lighter;
					cursor: default;
					border: none;
					margin: 4px 0;
					background: #feebe6;
					border-radius: 20px;
					padding: 4px 16px;
					color: #ff6432;
					font-weight: 600;
				}
			}
		}
		.per-type-package-title {
			position: relative;
			&:before {
				content: "";
				position: absolute;
				top: -2px;
				left: -2px;
				width: 48px;
				height: 48px;
				background-size: 100%;
				background-repeat: no-repeat;
                //[dir=rtl] & {
                //    right: -2px;
                //    left: auto;
                //}
			}
		}
		.company-package {
			margin: 10px 1%;

			.per-type-package-title::before {
				background-image: url(~img/public-limited-time.png);
			}
		}
		.personal-package {
			margin: 20px 1%;

			.per-type-package-title::before {
				background-image: url(~img/private-limited-time.png);
			}
		}
	}
	.recharge-endtime-info {
		width: 895px;
		padding-left: $main-padding-left;
		text-align: left;
		font-size: 14px;
		color: $--color-text-primary;
        [dir=rtl] & {
            padding-left: 0;
            padding-right: $main-padding-left;
            text-align: right;
        }
	}
	.recharge-endtime-info {
		margin-top: 20px;
	}
	.recharge-service-info {
		display: inline-block;
		margin-right: 18px;
        [dir=rtl] & {
            margin-right: 0;
            margin-left: 18px;
            float: left;
        }
		.line {
			color: $text-color-light;
		}
		p {
			display: inline-block;
		}
		p:nth-of-type(1) {
			color: $--color-text-primary;
		}
		p:nth-of-type(2) {
			font-weight: normal;
			a {
				font-size: 18px;
				color: $--color-text-secondary;
				&:hover {
					color: $--color-primary-light-1;
				}
			}
		}
	}
}
</style>
