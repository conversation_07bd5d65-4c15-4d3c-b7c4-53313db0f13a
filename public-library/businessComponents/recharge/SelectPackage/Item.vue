<template>
    <div class="per-package">
        <div v-if="isTieredPackage" class="step-package">{{ tieredTip }}</div>
        <div v-else-if="perPackage.isFirstPurchase" v-show="perPackage.isFirstPurchase" class="first-buy">{{ $t('rechargePackage.limitBugTip') }}</div>
        <div v-else v-show="perPackage.activity" class="first-buy activity">{{ perPackage.activity }}</div>
        <p class="per-package_name">{{ perPackage.name }}</p>
        <p class="line"></p>
        <div class="ai-des" v-if="packageType === 'ai'">套餐包含：{{ perPackage.name === '智签无忧套餐A' ? '1次风险判断+1份对私合同' : '1次风险判断+1份对公合同' }}</div>
        <p class="per-package__price">
            <span>{{ amountUint }}{{ price }}</span>
            <del v-if="!isMJ && packageType !== 'ai'">{{ amountUint }}{{ finalOriginPrice }}</del>
            <img class="per-package__price-icon" v-if="showPriceIcon" src="~pub-images/activityHuiIcon.png" alt="">
        </p>
        <p v-if="!isNumChangeable">
            {{ $t('rechargePackage.copiesTip',{num:perPackage.contractNum}) }}
            <el-button v-if="isMJ && packageType !== 'ai'" size="small" class="per-package-give">+{{ perPackage.presentNum }}</el-button>
        </p>
        <p v-if="isNumChangeable">
            {{ $t('rechargePackage.copiesTip1') }}
            <el-input-number v-model="num" size="mini" :step="tolerance" :min="minNum"></el-input-number>
        </p>
        <el-button v-if="isNumChangeable && isMJ" size="small" class="per-package-give">+{{ perPackage.presentNum }}</el-button>
        <p class="min-grey"><!-- 有效期：{{ perPackage.validityMonth }}个月 -->{{ $t('rechargePackage.vailidityPeriodTip',{month:perPackage.validityMonth }) }}</p>
        <p>
            <el-button type="text" style="color:#127FD2" @click="$emit('pay', perPackage.productPackageId, isNumChangeable,num,finalOriginPrice)"><!-- 立即购买 -->{{ $t('rechargePackage.buyNow') }}</el-button>
        </p>
        <span v-if="isEnt && perPackage.isDiscountCode" class="more-contract-tip" @click="$emit('pay', perPackage.productPackageId, isNumChangeable,num,finalOriginPrice)">{{ $t('recharge.getMoreContract') }}</span>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
export default {
    props: {
        perPackage: {
            default: () => {},
            type: Object,
        },
        packageType: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            num: 0, // 灵活套餐的购买份数
            minNum: 0, // 灵活套餐的最小购买份数
            tolerance: 1, // 灵活套餐的增减幅度
        };
    },
    computed: {
        ...mapGetters(['getUserType', 'getIsUae', 'getIsJa']),
        showPriceIcon() {
            const expired = +new Date() > +new Date('2025-01-20T18:00:00'); // 过期时间
            return !expired && ['10981', '10985', '10002', '10008'].includes(this.perPackage.productPackageId);
        },
        // marketType: 1：满赠，才需要显示赠送的份数，如下字段 presentNum
        isMJ() {
            return this.perPackage.marketType === 1;
        },
        // 是否灵活套餐
        isNumChangeable() {
            return this.perPackage.isNumChangeable;
        },
        price() {
            const { isNumChangeable, price, presentNum } = this.perPackage;
            // 灵活套餐价格
            if (isNumChangeable) {
                let resultPrice = price;
                if (this.isTieredPackage) { // 阶梯套餐
                    const { tieredRules } = this.perPackage;
                    let resultPackage = tieredRules.find(e => this.num >= e.minNum && this.num <= e.maxNum);
                    if (!resultPackage) {
                        resultPackage = tieredRules[tieredRules.length - 1];
                    }
                    resultPrice = resultPackage.price;
                }
                const val = (this.num + (this.isMJ ?  presentNum : 0)) * resultPrice;
                return val.toFixed(Number.isInteger(val) ? 0 : 2);
            }
            return price;
        },
        // 原价
        originPrice() {
            const { isNumChangeable, originPrice, presentNum } = this.perPackage;
            if (isNumChangeable) {
                const val = (this.num + (this.isMJ ?  presentNum : 0)) * originPrice;
                return val.toFixed(Number.isInteger(val) ? 0 : 2);
            }
            return '';
        },
        finalOriginPrice() {
            return !this.isNumChangeable ? this.perPackage.originPrice : this.originPrice;
        },
        isEnt() {
            return this.getUserType === 'Enterprise';
        },
        // 是否是阶梯套餐
        isTieredPackage() {
            const { tieredRules } = this.perPackage;
            return (tieredRules || []).length > 0;
        },
        // 阶梯套餐tip提示
        tieredTip() {
            const { tieredRules } = this.perPackage;
            if (!tieredRules || tieredRules.length <= 0) {
                return '';
            }
            let stepPackage = tieredRules[0];
            if (tieredRules && tieredRules.length >= 2) {
                stepPackage = tieredRules[tieredRules.length - 1];
            }
            const { minNum, price } = stepPackage;
            return `${minNum}份以上，¥${price}/份`;
        },
        amountUint() {
            if (this.getIsUae) {
                return `$`;
            }
            return `￥`;
        },
    },
    watch: {
        perPackage: {
            handler(val) {
                if (val) {
                    const { isNumChangeable, contractNum, tolerance } = val;
                    if (isNumChangeable && !this.num) {
                        this.minNum = contractNum;
                        this.num = contractNum;
                        this.tolerance = tolerance || 1;
                    }
                }
            },
            deep: true,
            immediate: true,
        },
    },
};
</script>

<style lang="scss" scoped>
    .more-contract-tip{
        font-size: 12px;
        background-color: $--color-danger;
        color:white;
        padding: 2px 3px;
        border-radius: 3px;
        cursor: pointer;
    }
    .per-package__price{
        display: flex;
        align-items: baseline;
        &-icon{
            width: 16px;
            margin-left: 5px;
            position: relative;
            top: 2px;
        }
    }
    .ai-des{
        font-size: 13px;
        color: $--color-text-secondary;
    }

</style>
