<template>
    <el-dialog
        custom-class="checkout-preview"
        :modal="false"
        :visible="visible"
        v-if="visible"
        @close="close"
        :append-to-body="true"
        :lock-scroll="false"
        :title="curName || ''"
    >
        <el-carousel height="430px"
            :autoplay="false"
            indicator-position="none"
            arrow="never"
            ref="carousel"
            :initial-index="current"
        >
            <el-carousel-item v-for="item in list" :key="item.index">
                <div class="preview-img__content">
                    <img :style="previewImgStyle"
                        class="preview-img__detail"
                        :src="handleImgUrl(item)"
                        @mousedown="onMousedown($event, item)"
                        ondragstart="return false;"
                        alt=""
                    >
                </div>
            </el-carousel-item>
        </el-carousel>
        <div class="preview-img__arrow" v-if="list.length > 1">
            <div class="preview-img__arrow--left" @click="change('prev')"><i class="el-icon-arrow-left"></i></div>
            <div class="preview-img__arrow--right" @click="change('next')"><i class="el-icon-arrow-right"></i></div>
        </div>
        <div class="preview-img_operations">
            <div @click="toRotate(-90)" class="preview-rotate_left"><i class="el-icon-ssq-xiangzuoxuanzhuan"></i></div>
            <div class="preview-scale">
                <i @click="toScale('small')" class="fl">-</i>
                <span>{{ parseInt(100*scale) }}%</span>
                <i @click="toScale('big')" class="fr">+</i>
                <i class="clear"></i>
            </div>
            <div @click="toRotate(90)" class="preview-rotate_right"><i class="el-icon-ssq-xiangyouxuanzhuan"></i></div>
            <div class="preview-rotate_left full-screen" @click="showFullScreenPreview">
                <i class="el-icon-ssq-quanping"></i>
            </div>
        </div>
        <el-dialog
            class="checkout-preview__fullscreen"
            :append-to-body="true"
            :visible.sync="fullScreenPreviewVisible"
            :title="curName || ''"
        >
            <img
                :src="currentImg"
                alt="title"
            >
        </el-dialog>
    </el-dialog>
</template>

<script>
let isDown = false;
let x = 0;
let y = 0;
let l = 0;
let t = 0;
export default {
    name: 'Preview',
    props: {
        list: {
            type: Array,
            default: () => {
                return [];
            },
        },
        current: {
            type: Number,
            default: 0,
        },
        visible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            scale: 1,
            access_token: this.$cookie.get('access_token'),
            requestTime: '',
            imgRotateDeg: 0,
            curName: '',
            fullScreenPreviewVisible: false,
            currentImg: '',
        };
    },
    computed: {
        previewImgStyle() {
            return {
                'transform': `rotate(${this.imgRotateDeg}deg)`,
                '-webkit-transform': `rotate(${this.imgRotateDeg}deg)`,
                '-o-transform': `rotate(${this.imgRotateDeg}deg)`,
                '-ms-transform': `rotate(${this.imgRotateDeg}deg)`,
                '-moz-transform': `rotate(${this.imgRotateDeg}deg)`,
            };
        },
    },
    watch: {
        visible(val) {
            if (val) {
                this.curName = this.list[this.current].name;
                this.imgRotateDeg = 0;
                this.$nextTick(() => {
                    this.$refs.carousel.setActiveItem(this.current);

                    let $doms = document.querySelectorAll('.preview-img__detail');
                    $doms = [].slice.call($doms); // 兼容IE
                    setTimeout(() => {
                        $doms.forEach(element => {
                            const img = new Image();
                            img.src = element.src;
                            img.onload = () => { // 图片加载后定位
                                this.markImageSize(element);
                                element.style.width = element.width + 'px';
                                element.style.height = element.height + 'px';
                                element.style.left = '';
                                element.style.top = (300 - element.height) / 2 + 'px';
                            };
                        });
                    }, 1000);
                });
            }
        },
    },
    methods: {
        handleImgUrl(item) {
            return typeof item === 'string' ? `${item}&access_token=${this.access_token}&t=${this.requestTime}` : item.imgUrl;
        },
        change(type) {
            if (type === 'prev') {
                this.$refs.carousel.prev();
            } else {
                this.$refs.carousel.next();
            }
            this.initImage();
        },
        close() {
            this.scale = 1;
            this.$emit('close');
        },
        markImageSize(element) { // 记录图片原始大小
            element.setAttribute('data-attrw', element.width);
            element.setAttribute('data-attrh', element.height);
        },
        initImage() {
            const item = this.list[this.$refs.carousel.activeIndex];
            this.scale = 1;
            this.curName = item.name;
        },
        showFullScreenPreview() {
            const item = this.list[this.$refs.carousel.activeIndex];
            this.currentImg = this.handleImgUrl(item);
            this.fullScreenPreviewVisible = true;
        },
        toScale(type) {
            const percent = type === 'big' ? 1.25 : 0.8;
            this.scale *= percent;
            const $dom = document.querySelectorAll('.preview-img__detail')[this.$refs.carousel.activeIndex];
            const nowW = $dom.getAttribute('data-attrw') * this.scale; // 缩放后的宽度
            const nowH = $dom.getAttribute('data-attrh') * this.scale;
            let posW = -(nowW - parseInt($dom.style.width.replace('px'))) / 2; // 调整宽度需要跳转左边距离
            let posH = -(nowH - parseInt($dom.style.height.replace('px'))) / 2;
            if ($dom.style.left) {
                posW = parseInt($dom.style.left.replace('px')) + posW;
                posH = parseInt($dom.style.top.replace('px')) + posH;
            } else {
                posH = (470 - $dom.getAttribute('data-attrh')) / 2 + posH;
                posW = 100 + posW;
            }
            $dom.style.top = `${posH}px`;
            $dom.style.left = `${posW}px`;
            $dom.style.width = `${nowW}px`;
            $dom.style.height = `${nowH}px`;
        },
        // 图片旋转
        toRotate(deg) {
            this.$nextTick(() => {
                this.imgRotateDeg += deg;
            });
        },
        onMousedown(e) {
            document.addEventListener('mousemove', this.onDragMove);
            document.addEventListener('mouseup', this.onDragEnd);
            // 获取x坐标和y坐标
            x = e.clientX;
            y = e.clientY;

            // 获取左部和顶部的偏移量
            l = e.target.offsetLeft;
            t = e.target.offsetTop;
            // 开关打开
            isDown = true;
            // 设置样式
            e.target.style.cursor = 'move';
        },
        onDragEnd(e) {
            // 开关关闭
            isDown = false;
            if (e.target.localName !== 'img') {
                return;
            }
            e.target.style.cursor = 'default';
            document.removeEventListener('mousemove', this.onDragMove);
            document.removeEventListener('mouseup', this.onDragEnd);
        },
        onDragMove(e) {
            if (isDown === false || e.target.localName !== 'img') {
                return;
            }
            // 获取x和y
            const nx = e.clientX;
            const ny = e.clientY;
            // 计算移动后的左偏移量和顶部的偏移量
            const nl = nx - (x - l);
            const nt = ny - (y - t);
            e.target.style.left = nl + 'px';
            e.target.style.top = nt + 'px';
        },
    },
    created() {
        this.requestTime = +new Date();
    },
};
</script>

<style lang="scss">
    .el-dialog.checkout-preview{
        width: 660px;
        border-radius: 6px;
        position: fixed;
        bottom: 0;
        right: 0;
        margin: 0;
        .el-dialog__header{
            padding: 18px 30px;
            line-height: 30px;
            height: 30px;
            box-sizing: content-box;
            .el-icon-close{
                line-height: 32px;
            }
        }
        .el-dialog__body{
            padding: 10px;
            .el-carousel{
                width:640px;
                .el-carousel__item{
                    background: $--color-white;
                }
                .preview-img__content{
                    height:100%;
                    position: relative;
                    overflow: hidden;
                    background: $--color-white;
                    img{
                        width:100%;
                        position: relative;
                        transition: all 0.2s;
                        &.init{
                            margin: 0 auto;
                            transform: translateY(-50%);
                            top: 50%;
                        }
                    }
                    .preview-img__content-warpper{
                        position: absolute;
                        pointer-events: none;
                    }
                }
            }
            .preview-img__arrow--left,.preview-img__arrow--right{
                display: block!important;
                font-size: 36px;
                width: 43px;
                height: 43px;
                background: #fff;
                opacity: 0.4;
                color: $--color-black;
                position: absolute;
                top:50%;
                cursor: pointer;
                z-index: 9;
            }
            .preview-img__arrow--left{
                left: 40px;
            }
            .preview-img__arrow--right{
                right: 40px;
            }
            .preview-img_operations{
                position: absolute;
                top: 18px;
                right: 75px;
                text-align: center;
            }
            .preview-rotate_left, .preview-rotate_right, .preview-scale{
                display: inline-block;
                width: 32px;
                border: 1px solid $theme-color;
                border-radius: 3px;
                text-align: center;
                height: 32px;
                line-height: 32px;
                vertical-align: middle;
                color: $theme-color;
            }
            .preview-rotate_left, .preview-rotate_right{
                cursor: pointer;
            }
            .full-screen{
                margin-left: 10px;
            }
            .preview-scale{
                margin: 0 10px;
                width: 160px;
                font-size: 16px;
                overflow: hidden;
                clear: both;
                i{
                    display: inline-block;
                    width: 40px;
                    text-align: center;
                    cursor: pointer;
                }
            }
        }
    }
    .checkout-preview__fullscreen .el-dialog{
        margin: 0 !important;
        width: 100%;
        height: 100%;
        .el-dialog__body{
            overflow: auto;
            position: absolute;
            width: 100%;
            top: 50px;
            bottom: 0;
            text-align: center;
            img{
                max-width: 100%;
            }
        }
    }
    .el-dialog .el-dialog__title{
        max-width: 43% !important;
        height: 25px;
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: keep-all;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
    }
</style>
