.business-chart-large {
    width: 100%;
    height: 306px;
    padding-top: 21px;
    box-sizing: border-box;
}

.symbol-color-block {
    width: 10px;
    height: 10px;
    display: block;
    margin-right: 10px;
    [dir=rtl] & {
        margin-left: 10px;
        margin-right: 0;
    }
}

.complete-chart-head-symbols {
    display: flex;
    align-items: flex;
    justify-content: flex-start;
}

.symbol-color-block.green {
    background-color: $--color-success;
}

.symbol-color-block.blue {
    background-color: $--color-primary;
}

.symbol-label {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-flow: row nowrap;
}

.complete-chart-head {
    width: 100%;
    display: flex;
    flex-flow: row nowrap;
    align-items: center;
    justify-content: space-between;
    box-sizing: border-box;
    padding-left: 30px;
    padding-right: 30px;
}

.send-amount {
    margin-right: 85px;
    [dir=rtl] & {
        margin-right: 0;
        margin-left: 85px;
    }
}

.symbol-text {
    font-size: 12px;
    color: $--color-text-primary;
    text-align: left;
    [dir=rtl] & {
        text-align: right;
    }
}

.period-options {
    display: flex;
    align-items: center;
    justify-content: center;
    flex-flow: row nowrap;
    cursor: pointer;
}

.period-option {
    height: 28px;
    padding: 0 12px;
    line-height: 28px;
    font-size: 12px;
    color: $--color-text-primary;
    border: 1px solid $--border-color-base;
    display: block;
    text-align: center;
}

.period-option:nth-child(1) {
    border-radius: 3px 0 0 3px;
    border-right: none;
    [dir=rtl] & {
        border-radius: 0 3px 3px 0;
        border-right: 1px solid;
    }
}

.period-option:nth-child(2) {
    border-left: 0px;
    border-radius: 0 3px 3px 0;
    [dir=rtl] & {
        border-radius: 3px 0 0 3px;
        border-left: 1px solid $--border-color-base;
        border-right: 0;
    }
}

.period-option.disabled {
    color: $--color-text-secondary;
}

.period-option.selected {
    border: 1px solid $--color-primary;
    color: $--color-primary;
}

.complete-chart-body {
    width: 100%;
    height: 178px;
    margin-top: 48px;
}
