<!--
签发量月度报表
首页、统计报表页面都有用到
-->
<template>
    <div id="business-chart-large" class="business-chart-large">
        <div class="complete-chart-head">
            <div class="complete-chart-head-symbols">
                <div class="symbol-label send-amount">
                    <span class="symbol-color-block green" :style="{'background-color': sendLineColor}"></span>
                    <span class="symbol-text">{{ sendTitle }}</span>
                </div>
                <div class="symbol-label complte-amount">
                    <span class="symbol-color-block blue" :style="{'background-color': signLineColor}"></span>
                    <span class="symbol-text">{{ signTitle }}</span>
                </div>
            </div>
            <div class="period-options">
                <span class="period-option" :class="selectedPeriodOption === 'day' ? 'selected': ''" data-dom-id="viewOverviewByDay" data-dom-type="action" @click="onDayMonthSwitch('day')"><!-- 日 -->{{ $t('common.day') }}</span>
                <span class="period-option" :class="{selected :selectedPeriodOption === 'month' , disabled: isMonthDisabled }" data-dom-id="viewOverviewByMonth" data-dom-type="action" @click="onDayMonthSwitch('month')"><!-- 月 -->{{ $t('common.month') }}</span>
            </div>
        </div>
        <div class="complete-chart-body" id="completed-chart-block" ref="completed-chart-block"></div>
    </div>
</template>
<script>
import dayjs from 'dayjs';
import duration from 'dayjs/plugin/duration';
dayjs.extend(duration);
import echarts from 'echarts/lib/echarts';
import 'echarts/lib/chart/line';
import 'echarts/lib/component/tooltip';
import i18n from 'src/lang';

export default {
    name: 'BusinessChartLarge',
    props: {
        sendTitle: {
            type: String,
            default: i18n.t('mainChart.sendVolume'), // 发送量
        },
        signTitle: {
            type: String,
            default: i18n.t('mainChart.signFinishAmout'), // 签署完成量
        },
        sendLineColor: {
            type: String,
            default: '#08B9A5',
        },
        sendLineOffsetColor: {
            type: Array,
            default: () => ['#b9e7d1', 'rgba(185,231,209,0)'],
        },
        sendSymbol: {
            type: String,
            default: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAaJJREFUOBG1VT1PAkEQfbPGQgtjZSIdmhgNLbFRG38Bmkhlh/IPMJaUKv8ApbOCROlNbLQyZ+lHo3RnYmWMiYUJ68zeHbfHIRyKr7nb2Tfv9uPNHKEXnOI4WshBt3MAZZmS8mkuoB2QaiKNJrLVr+506g6gvrvBsQonzsfmIgF64mEJ+eNzOxwK6rJCwz0A2iWbMPhdVbCV2geV28Id6yRkFg6HF5NsvYL7j0k0bi9k5K3QbFOfSSAAEWEnvYZCehWZKe8I795d1FrXOGldQWsdUP0nbcr2CXIBz/rBPrPZiWmcLhewPrPYleQNL18fsX1Tw8vnmzXPZzpHS8rcpnUBsrJ+YqIgHxKOcEPwJbIzlGeNMCzb/GllIcsTFW4EbDPl+6wTlzNLijiXsizYMa3RCS4giWgPbkoERwoR5HIKIdZIih5clwW5Ni2Iz5IiztWOMoVuKYhpxWeDIBzhRsBNQ5muAVPoZk4qQEzbTzQwdrRaWIM70D+UXrDmevHod81BBLjj5Kt75i3QMy1IJoaG3778PLsYvdDIGqy9sj/8Ar4Bj+6ozmWXJ38AAAAASUVORK5CYII=',
        },
        signLineColor: {
            type: String,
            default: '#619CFF',
        },
        signOffsetLineColor: {
            type: Array,
            default: () => ['#b1ccff', 'rgba(176,203,255,0)'],
        },
        signSymbol: {
            type: String,
            default: 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAABQAAAAUCAYAAACNiR0NAAAAAXNSR0IArs4c6QAAAY9JREFUOBGtVT1LBDEQfQlYClociI3FWmgnuLV/wY9e7LS0OhG1sNDG+wXaib2uf8F6BTst3MJGhCsULAXjvHUnl13PvT3uBkKSmTdvJ5c3OYM+tnzuJp67WIORAcTGYZYwZ/AqUwqHZL6F5H7HfNEfmgk3XE+duHUHdGQZVWOVfSbJ7Y8jcxP6rW6OnbPTp+5MyK7FN4iMaRGxzGGu8vgKGfh2aGtgmNkadN4PzR5zcsLimKzMm5HI1hKwKWOh9et+6gJXD8ClDCflhSbwDR7fFBfwKEF/zJlJ4GIVWJkLU3rruxdg+xZ4++z5ZJXJRS3a/DYDMlZWR0YKfogYYgOLyGULaXg/j/lfZR4kC2KILZnIjLcTh07+Zk2tDza2Klol0QvQfd1cxZLL66cucZiYLdrJ51AaTa2KJRcrTEMC6qyp9cGmlo0eElC01NkgI4bYkgmX5ashzkwD7ACKto5UhV3pFgo7yaU51tbTysb1OHjZ7B5gn6+GfqDpzBzmKr7cjeId9YH9Q8gvjfIX8AMjiJ6swYM37QAAAABJRU5ErkJggg==',
        },
        symbolSize: {
            type: Number,
            default: 14,
        },
        xAxisPointer: {
            type: Object || null,
            default: () => null,
        },
        seriesNameArr: {
            type: Array,
            default: () => [i18n.t('mainChart.contractSendAmout'), i18n.t('mainChart.contractSignAmout')], // 合同发送量 合同签署量
        },
        // 发送量
        sendData: {
            type: Array,
            default: () => [],
        },
        // 签完的量
        completedData: {
            type: Array,
            default: () => [],
        },
        dateRange: {
            type: Object,
            default: () => {},
        },
        axisFormat: {
            type: String,
            default: '-',
        },
        yAxisSplitLineColor: {
            type: String,
            default: '#D8D8D8',
        },
    },
    data() {
        return {
            selectedPeriodOption: '', // day日 month月,
            completeChart: null,
            chartData: {
                send: [],
                completed: [],
            },
            isMonthDisabled: false,
            pageX: 0,
        };
    },
    computed: {
        format() {
            const temp = this.selectedPeriodOption === 'day' ? 'YYYY-MM-DD' : 'YYYY-MM';
            return temp.replace(/-/g, this.axisFormat);
        },
    },
    watch: {
        dateRange: function _onDayRangeChange() {
            const { startDate, endDate } = this.dateRange;
            this.selectedPeriodOption = 'day';
            const formatedStarted = dayjs(startDate).format('YYYY-MM');
            const formatedEnd = dayjs(endDate).format('YYYY-MM');
            if (formatedEnd !== formatedStarted) {
                this.isMonthDisabled = false;
            } else {
                this.isMonthDisabled = true;
            }
        },
    },
    methods: {
        calcuateSimpleUnitAndAmount(amount, baseUnit) {
            if (amount === 0) {
                return [0, baseUnit];
            }
            if (Number(amount) >= 100000000) {
                return [(amount / 100000000).toFixed(1), `${this.$t('mainChart.billion')}${baseUnit}`];
            }
            if (Number(amount) >= 10000) {
                return [(amount / 10000).toFixed(1), `${this.$t('mainChart.wan')}${baseUnit}`];
            }
            return [amount, baseUnit];
        },
        updateChartOption() {
            const config = this.generateChartOption();
            this.completeChart.setOption(config);
        },
        // 准备chart的数据
        generateDataForChart() {
            const { format } = this;
            let { startDate, endDate } = this.dateRange;
            const cdata = {
                send: [],
                completed: [],
            };
            const dateKeys = [];
            endDate = dayjs(endDate).format('YYYY-MM-DD');
            startDate = dayjs(startDate).format('YYYY-MM-DD');
            const dateDuration =
                dayjs.duration(dayjs(endDate) - dayjs(startDate)).asDays() + 1;
            for (let i = 0; i < dateDuration; i++) {
                const dailySend = this.sendData[i] || 0;
                const dailyComplted = this.completedData[i] || 0;
                const dailyDate = dayjs(startDate)
                    .add(i, 'day')
                    .format(format);
                if (dateKeys.indexOf(dailyDate) === -1) {
                    dateKeys.push(dailyDate);
                }
                cdata.send.push([dailyDate, dailySend]);
                cdata.completed.push([dailyDate, dailyComplted]);
            }
            if (this.selectedPeriodOption === 'month') {
                const sendObj = {};
                const compltedObj = {};
                const monthlySend = [];
                const monthlyCompleted = [];
                cdata.send.forEach(_item => {
                    if (sendObj[_item[0]]) {
                        sendObj[_item[0]] += _item[1];
                    } else {
                        sendObj[_item[0]] = _item[1];
                    }
                });
                cdata.completed.forEach(_item => {
                    if (compltedObj[_item[0]]) {
                        compltedObj[_item[0]] += _item[1];
                    } else {
                        compltedObj[_item[0]] = _item[1];
                    }
                });
                for (let i = 0; i < dateKeys.length; i++) {
                    monthlySend.push([dateKeys[i], sendObj[dateKeys[i]]]);
                    monthlyCompleted.push([dateKeys[i], compltedObj[dateKeys[i]]]);
                }
                return {
                    send: monthlySend,
                    completed: monthlyCompleted,
                };
            }
            return cdata;
        },
        onDayMonthSwitch(dateType) {
            if (dateType === 'month' && this.isMonthDisabled) {
                return;
            }
            this.selectedPeriodOption = dateType;
            this.$nextTick(function _updateChart() {
                this.chartData = this.generateDataForChart();
                this.updateChartOption();
            });
        },
        generateChartOption() {
            const _self = this;
            const { format } = this;
            const tooltip = {
                show: true,
                trigger: 'axis',
                borderWidth: 0,
                padding: 0,
                backgroundColor: '$--color-white',
                formatter: (params) => {
                    const [{
                               seriesName: name1,
                               axisValue,
                               value: [, value1],
                           },
                           { seriesName: name2 = '', value: [, value2] = ['', ''] } = {},
                    ] = params;
                    const valueOneDisplay = _self.calcuateSimpleUnitAndAmount(
                        value1,
                        this.$t('mainChart.fen'), // 份
                    );
                    const valueTwoDisplay = _self.calcuateSimpleUnitAndAmount(
                        value2,
                        this.$t('mainChart.fen'), // 份
                    );
                    // 怎么判断tip是在moveover的左边还是右边
                    let e = document.querySelectorAll('.complete-chart-tooltip')[0];
                    let triggerIsInRight = false;
                    if (e && e.parentElement) {
                        e = e.parentElement;
                        // toolTip的三角形是否放在右边
                        triggerIsInRight = this.pageX - e.parentElement.getBoundingClientRect().x > e.style.left.replace('px', '');
                    }

                    return `<div class="complete-chart-tooltip ${triggerIsInRight ? 'complete-chart-tooltip_right' : 'complete-chart-tooltip_left'}" style="width: 169px; height: 104px; border-radius: 6px; border: none; background-color: rgba(51,51,51, 0.85); color: #FFF; text-align: center; box-sizing: border-box">
                                                                                                                      <p style="font-size: 14px; padding-top: 10px">${dayjs(
                                                                                                                        axisValue,
                                                                                                                      ).format(
                                                                                                                          _self.format,
                    // _self.selectedPeriodOption ===
                    // 'day'
                    //   ? 'YYYY-MM-DD'
                    //   : 'YYYY-MM',
                    )}</p>
                                                                                                                      <hr style="width: 141px; border-color: #4F5D87; border-top: none; border-left: none; margin: 5.5px 14px 4.5px">
                                                                                                                      <p style="font-size: 12px">${name1}：<span style="font-size: 16px; font-weight: 600">${
                        valueOneDisplay[0]
                    }</span>${valueOneDisplay[1]}</p>
                                                                                                                      ${
                        params.length >
                        1
                            ? `<p style="font-size: 12px">${name2}：<span style="font-size: 16px; font-weight: 600">${
                                valueTwoDisplay[0]
                            }</span>${
                                valueTwoDisplay[1]
                            }</p>`
                            : ''
                    }
                                                                                                                      </div>`;
                },
            };
            const grid = {
                left: '8%',
                top: '8%',
                right: '8%',
                bottom: '0%',
                containLabel: true,
            };
            const xAxis = {
                type: 'category',
                show: true,
                scale: true,
                min: 'dataMin',
                max: 'dataMax',
                alignWithLabel: true,
                showMinLabel: true,
                showMaxLabel: true,
                boundaryGap: false,
                axisLabel: {
                    show: true,
                    margin: 1,
                    color: '$--color-text-secondary',
                    align: 'center',
                    padding: [5, 5, 0, 5],
                    formatter: function(value) {
                        if (format === 'YYYY-MM-DD') {
                            const valueTime = dayjs(value);
                            return valueTime.format(format);
                        }
                        return value.replace(/\//g, '-');
                    },
                },
                splitLine: {
                    show: false,
                },
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                    alignWithLabel: true,
                },
            };
            if (this.xAxisPointer) {
                xAxis.axisPointer = this.xAxisPointer;
            }
            if (_self.selectedPeriodOption !== 'month') {
                xAxis.axisLabel.splitNumber = 7;
            } else {
                xAxis.axisLabel.splitNumber = _self.chartData.completed.length;
            }
            const yAxis = {
                type: 'value',
                show: true,
                scale: true,
                splitLine: {
                    show: true,
                    lineStyle: {
                        // color: '#D8D8D8',
                        color: this.yAxisSplitLineColor,
                    },
                },
                axisLine: {
                    show: false,
                },
                axisTick: {
                    show: false,
                },
                axisLabel: {
                    show: false,
                    width: 0,
                },
            };
            const series = [
                {
                    name: this.seriesNameArr[0],
                    type: 'line',
                    smooth: true,
                    symbol: `image://${this.sendSymbol}`,
                    symbolSize: this.symbolSize,
                    showSymbol: false,
                    sampling: 'average', // 数据过多，降采样率：采用平均策略
                    itemStyle: {
                        normal: {
                            color: this.sendLineColor,
                        },
                    },
                    lineStyle: {
                        normal: {
                            width: 2,
                        },
                    },
                    /* 线条区域使用渐变色 */
                    areaStyle: {
                        normal: {
                            opacity: 1,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: this.sendLineOffsetColor[0], // '#b9e7d1',
                                },
                                {
                                    offset: 1,
                                    color: this.sendLineOffsetColor[1], // 'rgba(185,231,209,0)',
                                },
                            ]),
                        },
                    },
                    data: _self.chartData.send,
                },
                {
                    name: this.seriesNameArr[1], // '合同签署量',
                    type: 'line',
                    smooth: true,
                    symbol: `image://${this.signSymbol}`,
                    symbolSize: this.symbolSize,
                    showSymbol: false,
                    sampling: 'average', // 数据过多，降采样率：采用平均策略
                    itemStyle: {
                        normal: {
                            color: this.signLineColor,
                        },
                    },
                    lineStyle: {
                        normal: {
                            width: 2,
                        },
                    },
                    /* 线条区域使用渐变色 */
                    areaStyle: {
                        normal: {
                            opacity: 1,
                            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                {
                                    offset: 0,
                                    color: this.signOffsetLineColor[0], // '#b1ccff',
                                },
                                {
                                    offset: 1,
                                    color: this.signOffsetLineColor[1], // 'rgba(176,203,255,0)',
                                },
                            ]),
                        },
                    },
                    data: _self.chartData.completed,
                },
            ];
            const config = {
                tooltip,
                grid,
                yAxis,
                xAxis,
                animation: true,
                series,
            };

            return config;
        },
    },
    created() {
        // 默认按天计算
        this.selectedPeriodOption = 'day';
    },
    mounted() {
        const chartBlock = this.$refs['completed-chart-block'];
        const { startDate, endDate } = this.dateRange;
        const formatedStarted = dayjs(startDate).format('YYYY-MM');
        const formatedEnd = dayjs(endDate).format('YYYY-MM');
        this.completeChart = echarts.init(chartBlock);

        document.getElementById('completed-chart-block').addEventListener('mousemove', e => {
            this.pageX = e.pageX;
        });

        this.chartData = this.generateDataForChart();
        this.updateChartOption();
        this.selectedPeriodOption = 'day';
        if (formatedEnd !== formatedStarted) {
            this.isMonthDisabled = false;
        } else {
            this.isMonthDisabled = true;
        }
    },
};

</script>

<style lang="scss" src="./index.scss">
</style>
