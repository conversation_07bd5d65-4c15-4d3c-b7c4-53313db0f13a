<!-- 业务组件：实名认证-实名认证获得权限部分 -->
<!-- 引用位置  用户中心页面：certification-->
<template>
    <div class="certification-permission-notice-static clear showBorder">
        <h1>{{ $t('certificationPermissionNoticeStatic.authPermissions.0') }}</h1>
        <div class="fl per-permission">
            <i class="el-icon-ssq-hetong"></i>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.1') }}</p>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.2') }}</p>
        </div>
        <div class="fl line"></div>
        <div class="fl per-permission">
            <i class="el-icon-ssq-anquan"></i>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.3') }}</p>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.4') }}</p>
        </div>
        <div class="fl line"></div>
        <div class="fl per-permission">
            <i class="el-icon-ssq-1"></i>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.5') }}</p>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.6') }}</p>
        </div>
        <div class="fl line"></div>
        <div class="fl per-permission">
            <i class="el-icon-ssq-iconziti29"></i>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.7') }}</p>
            <p>{{ $t('certificationPermissionNoticeStatic.authPermissions.8') }}</p>
        </div>
    </div>
</template>

<style lang="scss">
    .en-page .certification-permission-notice-static.showBorder .per-permission {
        height: 148px;
    }
    .certification-permission-notice-static {
        margin-top: 139px;
        font-size: 14px;
            & > h1 {
                width: 100%;
                height: 43px;
                padding-left: 30px;
                line-height: 43px;
                background-color: $background-color-dark;
            }
            & > div {
                margin-top: 40px;
            }
            .per-permission {
                margin-left: 55px;
                margin-right: 55px;
                // width: 125px;
                text-align: center;
                i {
                    font-size: 55px;
                    color: $--color-primary-light-8;
                }
                p {
                    line-height: 20px;
                    color: $--color-text-primary;
                }
                p:nth-of-type(1) {
                    margin-top: 18px;
                }
                &:nth-of-type(1) {
                    margin-left: 15px;
                }
            }
            .line {
                height: 110px;
                border-right: 1px solid $--border-color-lighter;
            }
    }

    .certification-permission-notice-static.showBorder{
        h1{
            font-size: 14px;
            color: $--color-text-regular;
            background: $--color-white;
        }

        .per-permission{
            margin: 5px 18px 0 0;
            // padding: 15px 50px 0;
            padding: 15px 0 0;
            width: 23%;
            height: 138px;
            border: 1px solid $--border-color-lighter;

            &:last-child{
                margin-right: 0;
            }
        }

        .line{
            display: none;
        }

        p:nth-of-type(1) {
            margin-top: 14px;
            line-height: 18px;
        }
    }
</style>
