// 印章ocr识别 与企业名字不一致dialog
<template>
    <div>
        <el-dialog
            :visible.sync="isErrorDialogShow"
            :title="$t('sealInconformityDialog.errorSeal.title')"
            append-to-body
            width="500px"
            class="seal-ocr-error-dialog"
            :class="{'ja-seal-ocr-error-dialog': isJa}"
        >
            <template v-if="!ocrEntData.entNameMatched">
                <p>{{ $t('sealInconformityDialog.errorSeal.tip') }}</p>
                <h5>{{ ocrEntData.entName || '未识别' }}</h5>
                <h5 v-if="ocrEntData.testKeywords"> {{ $t('sealInconformityDialog.errorSeal.tip3',{keyWord:ocrEntData.testKeywords }) }}</h5>
                <p>{{ $t('sealInconformityDialog.errorSeal.tip2') }}</p>
            </template>
            <template v-else-if="ocrEntData.testKeywords">
                <h5> {{ $t('sealInconformityDialog.errorSeal.tip4',{keyWord:ocrEntData.testKeywords }) }} </h5>
            </template>
            <p class="guide" @click="isExampleDialogShow = true">{{ $t('sealInconformityDialog.errorSeal.guide') }}</p>
            <span slot="footer" class="dialog-footer">
                <el-button @click="close">{{ $t('sealInconformityDialog.cancel') }}</el-button>
                <el-button type="primary" @click="goContinue">{{ $t('sealInconformityDialog.errorSeal.next') }}</el-button>
            </span>
        </el-dialog>
        <el-dialog
            :visible.sync="isExampleDialogShow"
            :title="$t('sealInconformityDialog.exampleSeal.title')"
            append-to-body
            width="588px"
            class="seal-example-dialog"
        >
            <h5>{{ $t('sealInconformityDialog.exampleSeal.way1')[0] }}</h5>
            <el-row>
                <el-col :span="12">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.way1')[1] }}</p>
                    <img src="~pub-images/sealExample/way1_1.png" alt="" height="80px">
                </el-col>
                <el-col :span="12">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.way1')[2] }}</p>
                    <img src="~pub-images/sealExample/way1_2.png" alt="" height="80px">
                </el-col>
            </el-row>
            <h5>{{ $t('sealInconformityDialog.exampleSeal.way2')[0] }}</h5>
            <p>{{ $t('sealInconformityDialog.exampleSeal.way2')[1] }}</p>
            <el-row>
                <el-col :span="12">
                    <img src="~pub-images/sealExample/way2_1.png" alt="" height="80px">
                </el-col>
                <el-col :span="12">
                    <img src="~pub-images/sealExample/way2_2.png" alt="" height="80px">
                </el-col>
            </el-row>

            <h5>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[0] }}</h5>
            <el-row :gutter="20">
                <el-col :span="8">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[1] }}</p>
                    <img src="~pub-images/sealExample/right.png" alt="" height="80px">
                </el-col>
                <el-col :span="8">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[2] }}</p>
                    <img src="~pub-images/sealExample/error.png" alt="" height="80px">
                </el-col>
                <el-col :span="8">
                    <p>{{ $t('sealInconformityDialog.exampleSeal.errorWay')[3] }}</p>
                    <img src="~pub-images/sealExample/businessLinese.png" alt="" height="80px">
                </el-col>
            </el-row>
            <span slot="footer" class="dialog-footer">
                <el-button @click="isExampleDialogShow = false">{{ $t('sealInconformityDialog.cancel') }}</el-button>
                <el-button type="primary" @click="isExampleDialogShow = false">{{ $t('sealInconformityDialog.confirm') }}</el-button>
            </span>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
        ocrEntData: {
            default: () => ({}),
            type: Object,
        },
    },
    data() {
        return {
            isExampleDialogShow: false,
        };
    },
    computed: {
        isErrorDialogShow: {
            get() {
                return this.value;
            },
            set(val) {
                this.$emit('input', val);
            },
        },
        lang() {
            return this.$i18n.locale;
        },
        isJa() {
            return this.lang === 'ja';
        },
    },
    methods: {
        close() {
            this.$emit('input', false);
        },
        goContinue() {
            this.close();
            this.$emit('confirm');
        },
    },
};
</script>
<style lang="scss">
.seal-ocr-error-dialog {
    line-height: 3;
    .el-dialog {
        position: relative;
        h5 {
            font-size: 14px;
            color: $--color-text-primary;
            font-weight: 500;
        }
        .guide {
            position: absolute;
            bottom: 14px;
            left: 30px;
            color: $--color-primary;
        }
    }
}
.ja-seal-ocr-error-dialog{
    .el-dialog {
        .guide {
            bottom: 64px;
        }
    }
}
.seal-example-dialog {
    h5 {
        font-size: 14px;
        color: $--color-text-primary;
        font-weight: 500;
        margin: 20px 0 15px 0;
    }
    p {
        font-size: 12px;
        color: $--color-text-primary;
        margin-bottom: 10px;
        font-weight: 400;
    }
}
</style>
