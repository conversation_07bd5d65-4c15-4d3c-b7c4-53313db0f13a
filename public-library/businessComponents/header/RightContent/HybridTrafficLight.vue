<!-- 业务组件：混合云红绿灯 -->
<template>
    <span>
        <el-tooltip class="item" effect="dark" :content="$t('commonHeader.viewDetail')" placement="bottom">
            <span v-if="hybridUser" class="hybridNetStatus" @click="dialogVisible = true">
                <i class="el-icon-ssq-lianjiegongyouyun icon-padding" :class="hybridInLAN && 'active'"></i>
            </span>
        </el-tooltip>

        <el-dialog
            width="700px"
            custom-class="hybrid-network__dialog"
            :title="$t('commonHeader.hybridNetworkDetail')"
            :visible.sync="dialogVisible"
            :append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
        >
            <div>
                <p class="network-tip" v-if="hybridInLAN">{{ $t('commonHeader.connectToPrivateNetTip') }}</p>
                <p class="network-tip" v-else>
                    {{ $t('commonHeader.noConnectToPrivateNetTip') }}<br />
                    {{ $t('commonHeader.advice1') }}{{ $t('commonHeader.checkNetTip1') }}
                </p>
                <el-table
                    class="hybrid-echo-table"
                    height="400"
                    :data="getHybridEchoItems"
                    :cell-class-name="tableCellClassName"
                >
                    <el-table-column
                        prop="date"
                        :label="$t('common.time')"
                        width="170"
                    >
                    </el-table-column>
                    <el-table-column
                        :label="$t('commonHeader.statusCode')"
                        width="80"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.status }}
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="timeCost"
                        :label="$t('commonHeader.timeCost')"
                        width="90"
                    >
                        <template slot-scope="scope">
                            {{ scope.row.timeCost }} ms
                        </template>
                    </el-table-column>
                    <el-table-column
                        :label="$t('commonHeader.tip')"
                    >
                        <template slot-scope="scope">
                            <p>{{ getTooltipContent(scope.row) }}</p>
                            <p v-if="scope.row.status !== 200">{{ scope.row.statusText }}</p>
                        </template>
                    </el-table-column>
                    <el-table-column
                        prop="url"
                        :label="$t('commonHeader.requestUrl')"
                    >
                    </el-table-column>
                </el-table>
            </div>
            <div slot="footer">
                <el-button type="primary" @click="dialogVisible = false">{{ $t('common.close') }}</el-button>
            </div>
        </el-dialog>
    </span>
</template>
<script>
import { mapGetters } from 'vuex';

export default {
    name: '',
    data() {
        return {
            dialogVisible: false,
        };
    },
    computed: {
        ...mapGetters([
            'getHybridUserType',
            'getInLAN',
            'getHybridEchoItems',
        ]),
        // 判断和公有云 public 搭边的 getHybridUserType 全都不显示小图标
        hybridUser() {
            return (
                this.getHybridUserType !== 'public'  &&
                this.getHybridUserType !== 'publicAliyun'
            );
        },
        // 混合云网络状态
        hybridInLAN() {
            return this.getInLAN;
        },
    },
    methods: {
        getTooltipContent(row) {
            return this.$t('commonHeader.hybridStatusCodeExplain')[row.status] || this.$t('commonHeader.hybridStatusCodeExplain.500');
        },
        tableCellClassName({ row, columnIndex }) {
            let className = '';
            if (columnIndex === 1) {
                if (row.status === 200) {
                    className = 'success-cell';
                } else if (row.status >= 400 && row.status < 500) {
                    className = 'warning-cell';
                } else {
                    className = 'danger-cell';
                }
            }
            return className;
        },
    },
};
</script>
<style lang='scss'>
.common-herder-right-content {
    .hybridNetStatus {
        cursor: pointer;
        i {
            color: $--color-text-regular;
            font-size: 16px;

            &.active {
                color: $--color-success;
            }
        }
    }
}
.hybrid-network__dialog{
    .network-tip{
        padding-bottom: 20px;
        border-bottom: 1px solid $--border-color-lighter;
        line-height: 25px;
        color: $--color-text-primary;
    }

    .hybrid-echo-table{
        .el-table__body-wrapper .el-table__body{
            .success-cell{
                color: $--color-success;
            }
            .warning-cell{
                color: $--color-warning;
            }
            .danger-cell{
                color: $--color-danger;
            }
            .el-icon-ssq-bangzhu1{
                cursor: pointer;
                color: $--color-text-primary;
            }
        }

    }
}
</style>
