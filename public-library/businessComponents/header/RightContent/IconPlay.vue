<template>
    <i class="iconfont el-icon-ssq-icon_play icon-play" @click="handleRoute"></i>
</template>

<script>
export default {
    name: 'IconPlay',
    data() {
        return {
        };
    },
    methods: {
        handleRoute() {
            this.$emit('clickPlay');
            this.$router.push('/account-center/operation-video');
        },
    },
};
</script>
<style scoped lang="scss">
    .icon-play.el-icon-ssq-icon_play {
        font-size: 16px;
        cursor: pointer;
        color: $--color-white;
        &:hover {
            color: $--color-primary-light-2;
        }
    }
</style>
