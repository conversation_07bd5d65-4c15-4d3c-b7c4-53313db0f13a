<template>
    <div class="common-herder-right-content" v-if="!noNavList">
        <div class="console-entrance-con inline-block">
            <!-- 集团控制台 -->
            <router-link
                v-if="userInfo.hasGroupConsole && !isReceiverView"
                tag="p"
                :to="`${GLOBAL.rootPathName}/console/group/account/setting`"
                class="console-entrance btn-type-one console-entrance_group"
            ><!-- 集团控制台 --><span @click="commonPoint('集团控制台')">{{ $t('commonHeader.groupConsole') }}</span></router-link>
            <!-- 企业控制台 -->
            <router-link
                v-if="userInfo.hasManager && !isReceiverView"
                tag="p"
                :to="`${GLOBAL.rootPathName}/console/enterprise/account/setting`"
                class="console-entrance btn-type-one"
            ><span @click="commonPoint('企业控制台')">{{ $t('commonHeader.enterpriseConsole') }}</span></router-link>
            <div
                v-if="createEntVisible"
                class="console-entrance btn-type-one"
                @click="handleCreateCompany"
            ><span @click="commonPoint('创建企业')">{{ $t('commonHeader.createEnt') }}</span></div>
        </div>
        <!-- 混合云网络状态图标 -->
        <HybridTrafficLight></HybridTrafficLight>

        <!-- 通知消息图标 -->

        <el-badge :value="noticeCount.unreadMessageCount" :max="99" class="notice-badge" :hidden="noticeCount.unreadMessageCount == 0">
            <el-tooltip class="item" effect="dark" :content="$t('commonHeader.message')" placement="bottom">
                <i @click="noticeShowFn" v-if="!isEn && !getIsForeignVersion" class="iconfont el-icon-ssq-tongzhi notice-icon icon-padding">
                </i>
            </el-tooltip>
        </el-badge>

        <!-- 通知消息面板 -->
        <transition enter-active-class="animated fadeIn" leave-active-class="animated fadeOut">
            <NoticeDec
                v-if="noticeShow"
                :noticeShow="noticeShow"
                :noticeCount="noticeCount"
                @updateNoticeCount="updateNoticeCount"
                @changeStatus="noticeShowFn"
            ></NoticeDec>
        </transition>
        <!--操作视频-->
        <el-tooltip v-if="!isEn && !getIsForeignVersion"
            class="item operate-video"
            effect="dark"
            :content="$t('commonHeader.video')"
            placement="bottom"
            :disabled="playTextDisabled"
        >
            <IconPlay @clickPlay="commonPoint('操作视频')" class="icon-padding" :class="{'verti-Middle':!isCanSwitchLanguage}"></IconPlay>
        </el-tooltip>
        <!-- 语言切换 -->
        <LangSwitch @changeLanguage="(item) => commonPoint(item.text)" v-if="isCanSwitchLanguage"></LangSwitch>
        <!-- 账户信息 -->
        <UserInfo
            ref="userInfo"
            :userInfo="userInfo"
            :currentEnt="currentEnt"
            :currentEmpName="currentEmpName"
            :presentIdentityClass="presentIdentityClass"
            :userSwitchPopShow="userSwitchPopShow"
            @switchPresentIdentityClick="switchPresentIdentityClick"
            @createCompany="handleCreateCompany"
            @commonPoint="(iconName) => commonPoint(iconName)"
        >
        </UserInfo>
        <div
            class="mask"
            v-if="userSwitchPopShow || noticeShow"
            @click="noticeShow ? noticeShow=!noticeShow : userSwitchPopShow = !userSwitchPopShow"
        ></div>
        <!-- 创建企业 -->
        <!-- <CreateCompany :title="createCompany.title" :visible.sync="createCompany.visible"></CreateCompany> -->
        <component
            :is="createCompany.componentName"
            :title="createCompany.title"
            :visible.sync="createCompany.visible"
            :createType="createCompany.createType"
        >
        </component>
    </div>
    <div class="right-content noNavList" v-else @click="$router.push('/account-center/home');commonPoint('返回首页')"><!-- 返回首页 -->{{ $t('commonHeader.backToHome') }}</div>
</template>
<script>
import NoticeDec from './NoticeDec.vue';
import IconPlay from './IconPlay';
import UserInfo from './UserInfo.vue';
import CreateCompany from './DialogCreateCompany.vue';
import CreateCompanyJa from './CreateCompanyJa';
import HybridTrafficLight from './HybridTrafficLight.vue';
import LangSwitch from 'pub-businessComponents/langSwitch';

import { mapState, mapGetters } from 'vuex';
export default {
    components: {
        NoticeDec,
        IconPlay,
        UserInfo,
        CreateCompany,
        CreateCompanyJa,
        LangSwitch,
        HybridTrafficLight,
    },
    props: {
        noNavList: {
            type: Boolean,
            default: false,
        },
        userInfo: {
            type: Object,
            default: function() {
                return {};
            },
        },
        currentEnt: {
            type: String,
            default: '',
        },
        currentEmpName: {
            type: String,
            default: '',
        },
        isReceiverView: {
            type: Boolean,
            default: false,
        },
        getActivatedMenu: {
            type: Function,
            default: function() {
                return () => {};
            },
        },
    },
    data() {
        return {
            noticeShow: false, // 消息展示
            userSwitchPopShow: false,
            createCompany: {
                componentName: 'CreateCompany',
                title: this.$t('commonHeader.createEnt'),
                visible: false,
                createType: 'create', // create: 创建企业, auth: 日文版发送合同前企业实名
            },
        };
    },
    computed: {
        ...mapState({
            noticeCount: state => state.noticeCount,
            playTextDisabled: state => state.newFromExperience,
        }),
        ...mapState(['features']),
        ...mapGetters([
            'getAuthStatus',
            'getUserType',
            'getInLAN',
            'getIsForeignVersion',
        ]),
        // 创建企业按钮显示
        createEntVisible() {
            return (
                this.userInfo.enterprises.length === 1 &&
                this.userInfo.userType === 'Person'
            );
        },
        presentIdentityClass: function() {
            return {
                active: this.userSwitchPopShow,
            };
        },
        isEn() {
            return this.$i18n.locale === 'en';
        },
        isCanSwitchLanguage() { // 是否可以切换语言
            return true;
            // return this.features.includes('180');
        },
    },
    methods: {
        commonPoint(iconName) {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.getActivatedMenu(),
                    first_category: '顶部导航栏',
                    icon_name: iconName,
                },
            });
        },
        // 点击消息按钮
        noticeShowFn() {
            this.commonPoint('消息');
            this.noticeShow = !this.noticeShow;
        },
        // 更新消息数量
        updateNoticeCount(data) {
            this.$store.commit('pushNoticeCount', data);
            if (this.$route.path.indexOf('/notice/') > -1) {
                this.$bus.emit('update-notice');
            }
        },
        // 切换身份下拉
        switchPresentIdentityClick() {
            this.userSwitchPopShow = !this.userSwitchPopShow;
        },
        /**
         * @desc 创建企业方法
         * @desc 根据是否是日文版 显示普通创建/日文版创建企业
        */
        handleCreateCompany() {
            if (this.getIsForeignVersion) {
                this.createCompany.componentName = 'CreateCompanyJa';
            } else {
                this.createCompany.componentName = 'CreateCompany';
            }
            this.createCompany.visible = true;
        },
    },
    created() {
        this.$http.getNoticeCount();
    },
};
</script>
<style lang="scss">
    .en-page .common-herder-right-content .console-entrance{
        width: 135px;
        &.console-entrance_group{
            width: 110px;
        }
    }
    .common-herder-right-content {
        position: absolute;
        right: 18px;
        top: 0;
        height: 100%;
        line-height: 63px;
        [dir="rtl"] & {
            left: 18px;
            right: auto;
        }
        .operate-video{
            position: relative;
            top:1px;
        }
        &.noNavList {
            cursor: pointer;
            line-height: $header-height;
            font-size: 14px;
            color: $--color-white;
        }
        .console-entrance-con {
            height: 100%;
            padding-top: 19px;
            padding-right: 10px;
            vertical-align: top;
            overflow: hidden;
            [dir="rtl"] & {
                padding-left: 10px;
                padding-right: 0;
            }
        }
        .console-entrance {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 100px;
            height: 26px;
            line-height: 26px;
            font-size: 14px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            &.console-entrance_group {
                float: left;
                margin-right: 10px;
                width: 100px;
            }
            [dir="rtl"] & {
                width: 140px;
            }
        }
        .icon-padding {
            margin-right: 5px;
            margin-left: 5px;
        }
        .logout {
            color: $--color-primary-light-2;
            margin-top: 0;
            vertical-align: text-bottom;
        }
        .notice-icon {
            color: $--color-white;
            font-size: 16px;
            cursor: pointer;
            position: relative;
            vertical-align: sub;
            &:hover {
                color: $--color-primary-light-2;
            }
            span {
                width: 6px;
                height: 6px;
                border-radius: 6px;
                background-color: $--color-danger;
                content: "";
                display: inline-block;
                position: absolute;
                top: 12px;
                right: -2px;
                [dir="rtl"] & {
                    right: auto;
                    left: -2px;
                }
            }
        }
        .notice-badge {
            .el-badge__content{
                top: 11px;
                right: 15px;
            }
        }
        .header-quit-tooltip {
            position: absolute;
            top: 25px;
            left: 188px;
            [dir="rtl"] & {
                left: auto;
                right: 188px;
            }
        }
        .verti-Middle{
            vertical-align: middle !important;
        }
    }
</style>
