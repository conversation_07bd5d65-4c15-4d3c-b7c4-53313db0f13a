<template>
    <!-- 创建/实名企业日文版 -->
    <div class="box-sizing-dialog">
        <el-dialog
            :title="title"
            :visible.sync="visible"
            :before-close="handleClose"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :modal-append-to-body="false"
            class="dialog-createCompany-ja el-dialog-bg"
        >
            <div>
                <!-- 注册地 -->
                <div class="register-area">
                    <div class="ent-label">{{ $t('commonHeader.createCompnayP.areaRegister') }}：</div>
                    <template>
                        <el-radio v-if="getIsJa" v-model="registerRadio" label="JPN">{{ $t('commonHeader.createCompnayP.jp') }}</el-radio>
                        <el-radio v-else-if="getIsUae" v-model="registerRadio" label="ARE">{{ $t('commonHeader.createCompnayP.are') }}</el-radio>
                        <el-radio v-model="registerRadio" label="CHN">{{ $t('commonHeader.createCompnayP.cn') }}</el-radio>
                        <el-radio v-model="registerRadio" label="OTHER">{{ $t('commonHeader.createCompnayP.other') }}
                            <el-select v-show="registerRadio === 'OTHER' " v-model="otherArea" filterable :placeholder="$t('commonHeader.createCompnayP.plsSelect')">
                                <el-option
                                    v-for="item in areaList"
                                    :key="item.abbreviation"
                                    :label="item[countryKey]"
                                    :value="item.abbreviation"
                                >
                                </el-option>
                            </el-select>
                        </el-radio>

                    </template>
                </div>
                <template v-if="registerRadio === 'CHN'">
                    <div class="cn-tip">{{ $t('commonHeader.createCompnayP.tip1') }}</div>
                    <div class="cn-tip">{{ $t('commonHeader.createCompnayP.tip2') }}</div>
                </template>
                <template v-else-if="isJaEnvSelectAre">
                    <div class="cn-tip">{{ $t('commonHeader.createCompnayP.uaeTip1') }}</div>
                    <div class="cn-tip">{{ $t('commonHeader.createCompnayP.uaeTip2') }}</div>
                </template>
                <template v-else-if="isAreEnvSelectOther && otherArea !== 'ARE'">
                    <div class="cn-tip">{{ $t('commonHeader.createCompnayP.uaeTip3') }}</div>
                </template>
                <template v-else>
                    <div class="top-hint">
                        {{ $t('commonHeader.createCompnayP.p5') }}
                    </div>
                    <div class="top-hint">
                        {{ $t('commonHeader.createCompnayP.p6') }}
                    </div>
                    <!-- 日文版form表单 -->
                    <el-form
                        class="setPhone-form-ja"
                        :model="createCompany"
                        label-position="right"
                        :label-width="formItemWidth"
                    >
                        <el-form-item :label="$t('commonHeader.entName')">
                            <div class="input-entName">
                                <el-input
                                    v-model="createCompany.name"
                                    :placeholder="$t('commonHeader.enterEnterprise')"
                                    v-focus
                                ></el-input>
                            </div>
                        </el-form-item>
                        <el-form-item :label="copNumLabel">
                            <div class="input-entName">
                                <el-input
                                    v-model="createCompany.businessLicenceNumber"
                                    :placeholder="copNumTip"
                                ></el-input>
                                <div v-if="registerRadio !== 'JPN'" class="tip-hint">{{ $t('commonHeader.createCompnayP.tip3') }}</div>
                            </div>
                        </el-form-item>
                        <el-form-item :label="picLabel" prop="fileId" class="upload-item">
                            <el-upload
                                :headers="uploadHeaders"
                                accept=""
                                action="/ents/charging/bill/pic"
                                :file-list="fileList"
                                :show-file-list="true"
                                :on-preview="handlePreview"
                                :before-upload="beforeUpload"
                                :on-progress="uploading"
                                :on-success="uploadSuccess"
                                :on-error="uploadError"
                                :on-remove="uploadRemove"
                                ref="elUpload"
                            >
                                <div>
                                    <el-button class="upload-btn" type="primary" plain :disabled="createCompany.fileId != null || uploadStatus > 0">+ <!-- 点击上传 -->{{ $t('recharge.entSpecialInvoiceMap.clickUpload') }}</el-button>
                                </div>
                            </el-upload>
                            <div v-if="registerRadio !== 'JPN'" class="tip-hint">
                                <div class="bold-font top-margin">{{ $t('commonHeader.createCompnayP.tip4') }}：</div>
                                <div><span>{{ $t('commonHeader.createCompnayP.tip5') }}</span></div>
                                <div><span>{{ $t('commonHeader.createCompnayP.tip6') }}</span></div>
                                <div><span>{{ $t('commonHeader.createCompnayP.tip7') }}</span></div>
                                <div><span>{{ $t('commonHeader.createCompnayP.tip8') }}</span></div>
                            </div>
                        </el-form-item>
                    </el-form>
                </template>
            </div>
            <div v-if="isShowBottomBtnMenu" slot="footer" class="dialog-footer">
                <el-button
                    type="primary"
                    @click="handleCreate"
                    v-loading.fullscreen.lock="loading"
                    :element-loading-text="$t('commonHeader.createCompnayP.loadingTxt')"
                >{{ $t('commonHeader.createEnt') }}</el-button>
                <el-button
                    @click="handleClose"
                >{{ $t('commonHeader.cancel') }}</el-button>
            </div>
        </el-dialog>
        <!-- 预览图片 -->
        <Preview
            v-model="dialogVisible"
            :title="$t('recharge.entSpecialInvoiceMap.certifyProvePic')"
        >
            <div slot="previewContent"><img class="preview-image" :src="dialogImageUrl" alt=""></div>
        </Preview>
    </div>
</template>

<script>
import regRules from 'pub-utils/regs.js';
import Preview from 'pub-components/preview';
import { mapGetters } from 'vuex';

export default {
    components: {
        Preview,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        title: {
            type: String,
            default: '',
        },
        createType: {
            type: String,
            default: 'create', // create: 创建企业, auth: 日文版发送合同前企业实名
        },
    },
    data() {
        return {
            createCompany: {
                name: '',
                businessLicenceNumber: '',
                fileId: null,
            },
            regRules: regRules,
            loading: false,
            fileList: [],
            uploadStatus: 0,
            uploadHeaders: {
                Authorization: `bearer ${this.$cookie.get('access_token')}`,
            },
            dialogVisible: false,
            dialogImageUrl: '',
            registerRadio: 'JPN',
            areaList: [],
            otherArea: '',
        };
    },
    computed: {
        ...mapGetters([
            'getIsJa',
            'getIsUae',
        ]),
        copNumLabel() {
            return this.registerRadio === 'JPN' ? this.$t('commonHeader.corporateNumber') : this.$t('commonHeader.createCompnayP.comNum');
        },
        copNumTip() {
            return this.registerRadio === 'JPN' ? this.$t('commonHeader.corporateNumberTip') : this.$t('commonHeader.createCompnayP.comNum');
        },
        picLabel() {
            return this.registerRadio === 'JPN' ? this.$t('commonHeader.createCompnayP.p7') : this.$t('commonHeader.createCompnayP.tip4');
        },
        formItemWidth() {
            const map = {
                zh: '82px',
                ja: '98px',
                en: '150px',
            };
            return map[this.$i18n.locale] || '82px';
        },
        countryKey() {
            return this.$i18n.locale === 'zh' ? 'chineseName' : 'englishName';
        },
        isShowBottomBtnMenu() {
            // 中文不能注册
            if (this.registerRadio === 'CHN') {
                return false;
            }
            // ja环境时，阿联酋不能注册
            if (this.getIsJa && this.registerRadio === 'OTHER' && this.otherArea === 'ARE') {
                return false;
            }
            return true;
        },
        // ja环境选择are
        isJaEnvSelectAre() {
            return this.getIsJa && this.registerRadio === 'OTHER' && this.otherArea === 'ARE';
        },
        // are环境下选择other
        isAreEnvSelectOther() {
            return this.getIsUae && this.registerRadio === 'OTHER';
        },
    },
    methods: {
        handlePreview(file) {
            if (file.url) {
                this.dialogImageUrl = file.url;
            } else {
                this.dialogImageUrl = URL.createObjectURL(file.raw);
            }
            this.dialogVisible = true;
        },
        // 检测图片格式
        checkImgFormat(file) {
            const checkSize = file.size / 1024 / 1024 < 5;
            const acceptType = ['image/png', 'image/jpeg', 'image/jpg'];
            const checkFormat = acceptType.some((type) => {
                return file.type === type;
            });

            // 判断图片大小
            if (!checkSize) {
                this.$MessageToast.error(this.$t('recharge.entSpecialInvoiceMap.picNotExceetTip')); // 图片大小不能超过5M
            }

            // 判断图片格式
            if (!checkFormat) {
                this.$MessageToast.error(this.$t('recharge.entSpecialInvoiceMap.picNotMeetRequire')); // 图片格式不符合要求
            }

            return checkSize && checkFormat;
        },
        // 上传前校验
        beforeUpload(file) {
            const checkFormat = this.checkImgFormat(file);
            return checkFormat;
        },
        uploading() {
            this.uploadStatus = 1;
        },
        uploadSuccess(res) {
            this.createCompany.fileId = res.value;
            // this.$refs['billingSubmitForm'].validateField('fileId');
            this.uploadStatus = 2;
        },
        uploadError(err) {
            const errMes = (err.response && err.response.data.message) ? err.response.data.message : this.$t('recharge.errorTip'); // 出错啦
            this.uploadStatus = 0;
            this.$MessageToast.error(errMes);
        },
        uploadRemove() {
            this.createCompany.fileId = null;
            this.uploadStatus = 0;
        },
        // 创建新企业
        handleCreate() {
            // eslint-disable-next-line eqeqeq
            const data = {};
            let country = this.registerRadio;
            if (this.registerRadio === 'OTHER') {
                if (!this.otherArea) {
                    return this.$MessageToast.error(this.$t('commonHeader.selectArea'));
                }
                country = this.otherArea;
            }
            data.country = country;
            const entName = this.createCompany.name.replace(/\s+/g, '');
            const businessLicenceNumber = this.createCompany.businessLicenceNumber.replace(/\s+/g, '');
            if (entName.length === 0) {
                this.$MessageToast(this.$t('commonHeader.plzEnterRightEnt'));
                return;
            } else {
                data.entName = this.createCompany.name;
            }
            if (businessLicenceNumber.length === 0) {
                if (this.registerRadio === 'JPN' && !this.regRules.corporateNumber.test(businessLicenceNumber)) {
                    return this.$MessageToast(this.$t('commonHeader.plzEnterRightCorporate'));
                }
                return this.$MessageToast(this.$t('commonHeader.createCompnayP.plsEnterComNum'));
            } else {
                data.businessLicenceNumber = this.createCompany.businessLicenceNumber;
            }
            if (!this.createCompany.fileId || this.createCompany.fileId.length === 0) {
                const msg = this.registerRadio === 'JPN' ? this.$t('commonHeader.createCompnayP.p8') : this.$t('commonHeader.createCompnayP.plsUploadBuyRecord');
                return this.$MessageToast(msg);
            } else {
                data.fileId = this.createCompany.fileId;
            }
            if (this.createType === 'create') {
                this.loading = true;
                this.$http.post('/authenticated/enterprise-register', data).then(res => {
                    // 自动切换到企业，需要替换 token
                    this.$token.save(res.data.access_token, res.data.refresh_token);
                    this.$MessageToast.success(this.$t('commonHeader.createSuccess'));
                    // 日文版不需要实名，跳转到企业控制台
                    this.$router.push('/console/enterprise/account/setting');
                }).finally(() => this.loading = false);
            } else {
                this.$http.post('/ents/auth/ja', data).then(() => {
                    // 自动切换到企业，需要替换 token
                    this.$MessageToast.success(this.$t('commonHeader.createSuccess'));
                    // 日文版不需要实名，刷新当前页面重新获取head-info等信息
                    window.location.reload();
                });
            }
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        getCountryList() {
            this.$http.get('/ents/countries').then(res => {
                this.areaList = res.data || [];
            });
        },
    },
    mounted() {
        this.getCountryList();
        if (this.getIsJa) {
            this.registerRadio = 'JPN';
        } else if (this.getIsUae) {
            this.registerRadio = 'ARE';
        }
    },
};
</script>

<style lang="scss">
.box-sizing-dialog{
    .preview-image {
        max-width: 100%;
        max-height: 100%;
    }
    .upload-btn{
        &:hover, &:focus, &:active{
            color: $--color-primary;
        }
    }
    .el-form-item__label{
        word-break: break-word;
        white-space: normal;
    }
    .register-area{
        display: flex;
        align-items: center;
        justify-content: flex-start;
        .ent-label{
            height: 40px;
            line-height: 40px;
        }
    }
    .cn-tip{
        padding: 10px 10px 30px 10px;
        line-height: normal;
        color: $--color-text-regular;
        word-break: break-word;
        white-space: normal;
    }
    .tip-hint{
        font-size: 12px;
        color: $--color-text-regular;
        word-break: break-word;
        white-space: normal;
    }
    .bold-font{
        font-weight: 600;
    }
    .nomal-font{
        font-weight: 300;
    }
    .top-margin{
        margin-top: 10px;
    }
    .top-hint{
        font-size: 14px;
        padding: 0px 20px;
        color: $--color-text-regular;
        line-height: 30px !important;
        word-break: break-word;
        white-space: normal;
    }

}
.en-page {
    .top-hint{
        padding: 20px 20px 40px 20px !important;
    }
    .el-dialog{
        width: 790px !important;
    }
}
.box-sizing-dialog .dialog-createCompany-ja.el-dialog-bg{
    .el-dialog{
        width: 610px;
        min-height: 312px;
        .el-dialog__header {
            padding: 0;
            padding-left: 20px;
            line-height: 70px;
            .el-dialog__headerbtn{
                line-height: 24px;
            }
        }
        .el-dialog__body{
            .dialog_body_header{
                text-align: left;
            }

            .setPhone-form-ja{
                    margin-top: 20px;
                .el-form-item{
                    p{
                        color: $--color-text-secondary;
                        font-size: 12px;
                    }
                }
            }
        }

        .el-dialog__footer{
            padding-bottom: 35px;
        }
    }
}
.are-page{
    .el-dialog{
        width: 680px !important;
    }
}
</style>
