<template>
    <!-- 选择企业 -->
    <el-dialog :title="$t('commonHeader.chooseEnterprise')" :visible.sync="visible" :before-close="handleClose" class="search-pop" :modal-append-to-body="false">
        <div class="search-input-box">
            <el-input
                class="search-input"
                :placeholder="$t('commonHeader.enterEnterprise')"
                suffix-icon="el-icon-ssq-sousuo"
                v-model="query"
                @change="search"
            >
            </el-input>
        </div>
        <div class="result-box">
            <div class="loading-box" v-if="loading === true">
                <i class="el-icon-loading"></i>
            </div>
            <div class="no-result-box" v-else-if="searchList.length === 0">
                <i class="el-icon-ssq-meiyoujieguo1"></i>
                <p class="no-result-text">
                    {{ $t('commonHeader.noResult') }}
                </p>
            </div>
            <ul class="search-ul" v-else>
                <!-- 未搜索 -->
                <template v-if="!query">
                    <!-- 集团信息 -->
                    <div
                        v-for="perGroup in groups"
                        :key="perGroup.entGroupId"
                    >
                        <!-- 集团标题 -->
                        <li class="group-item" @click="groupItemClick(perGroup)">
                            <div class="inline-block cursor-point content">
                                <i
                                    class="el-icon-ssq-jituantubiao group-mark"
                                ></i> {{ `${perGroup.entGroupName}(${perGroup.enterprises.length})` }}
                            </div>
                            <i
                                class=" group-arrow"
                                :class="perGroup.expand ?'el-icon-arrow-up' : 'el-icon-arrow-down'"
                            ></i>
                        </li>

                        <!-- 集团企业 -->
                        <template v-if="perGroup.expand">
                            <li
                                v-for="perEnterprise in perGroup.enterprises"
                                class="cursor-point"
                                :data-id="perEnterprise.entId"
                                :key="perEnterprise.entId"
                                @click="setSelectEntInfo(perEnterprise)"
                            >
                                <EntGroupItem
                                    :perEnterprise="perEnterprise"
                                    :userInfo="userInfo"
                                    :selectEntId="selectEntId"
                                    :isAllCompanyDialog="true"
                                    :isGroup="true"
                                ></EntGroupItem>
                            </li>
                        </template>
                    </div>

                    <!-- 非集团的其它企业显示 -->
                    <li v-for="perEnterprise in enterprises"
                        class="cursor-point"
                        :data-id="perEnterprise.entId"
                        :key="perEnterprise.entId"
                        @click="setSelectEntInfo(perEnterprise)"
                    >
                        <EntGroupItem
                            :perEnterprise="perEnterprise"
                            :userInfo="userInfo"
                            :selectEntId="selectEntId"
                            :isAllCompanyDialog="true"
                        ></EntGroupItem>
                    </li>
                </template>

                <!-- 已搜索匹配的企业 -->
                <template v-else>
                    <li v-for="perEnterprise in searchList"
                        class="cursor-point"
                        :data-id="perEnterprise.entId"
                        :key="perEnterprise.entId"
                        @click="setSelectEntInfo(perEnterprise)"
                    >
                        <EntGroupItem
                            :perEnterprise="perEnterprise"
                            :userInfo="userInfo"
                            :selectEntId="selectEntId"
                            :isAllCompanyDialog="true"
                        ></EntGroupItem>
                    </li>
                </template>
            </ul>
        </div>
        <div slot="footer" class="dialog-footer">
            <el-button
                class="search-ok"
                type="primary"
                @click="handleSwitch"
            >{{ $t('commonHeader.confirm') }}</el-button>
            <el-button
                class="search-cancel"
                @click="handleClose"
            >{{ $t('commonHeader.cancel') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
import { mapState } from 'vuex';
import EntGroupItem from './EntGroupItem.vue';

/* 模糊查询 */
function query(list, groupList, keyWord, searchFunction = item => item) {
    const reg =  new RegExp(keyWord);
    const arr = [];
    for (let i = 0; i < list.length; i++) {
        if (reg.test(searchFunction(list[i]))) {
            arr.push(list[i]);
        }
    }
    for (let i = 0; i < groupList.length; i++) {
        const { enterprises } = groupList[i];
        for (let j = 0; j < enterprises.length; j++) {
            if (reg.test(searchFunction(enterprises[j]))) {
                arr.push(enterprises[j]);
            }
        }
    }
    return arr;
}
/* 事件优化 */
function lazy(f) {
    const time = 300;
    let timeOut = null;
    function lazyFunc(...arg) {
        const self = this;
        if (timeOut === null) {
            this.loading = true;
            timeOut = setTimeout(() => {
                f.call(self, ...arg);
                timeOut = null;
                self.loading = false;
            }, time);
        } else {
            return false;
        }
    }
    return lazyFunc;
}
export default {
    name: 'DialogSelectCompany',
    components: {
        EntGroupItem,
    },
    props: {
        visible: {
            type: Boolean,
            default: false,
        },
        currentEntId: {
            type: String,
            default: null,
        },
        userInfo: {
            type: Object,
            default: () => ({
            }),
        },
    },
    data() {
        return {
            query: '',
            searchList: [], // 搜索的企业信息(集团和非集团打平)
            selectEntId: null,
            selectEnt: null,
            loading: true,
            enterprises: [], // 非集团的企业信息
            groups: [], // 集团信息
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo']),
    },
    watch: {
        visible(newValue, oldValue) {
            if (newValue !== oldValue && newValue === true) {
                this.clear();
                this.getEnts();
            }
        },
    },
    methods: {
        getEnts() {
            this.$http(`/users/chosen-enterprises/v2`)
                .then(res => {
                    const { groups, enterprises } = res.data;
                    this.enterprises = enterprises.map(item => {
                        const { bizName, entName } = item;
                        item.fullEntName = bizName ? `${entName}_${bizName}` : entName;
                        if (item.ecologyFlag) {
                            // 区分不同的生态版马甲
                            item.entId = `${item.entId}-${item.clientId}-ecology`;
                        }
                        return item;
                    });
                    this.groups = groups.map(element => {
                        element.expand = true; // 展开
                        element.enterprises.map(item => {
                            const { bizName, entName } = item;
                            item.fullEntName = bizName ? `${entName}_${bizName}` : entName;
                            if (item.ecologyFlag) {
                                // 区分不同的生态版马甲
                                item.entId = `${item.entId}-${item.clientId}-ecology`;
                            }
                            return item;
                        });
                        return element;
                    });
                    this.search();
                });
        },
        setSelectEntInfo(ent) {
            this.selectEntId = ent.entId;
            this.selectEnt = ent;
        },
        // 切换展示或隐藏集团子企业
        groupItemClick(group) {
            const index = this.groups.findIndex(a => a.entGroupId === group.entGroupId);
            group.expand = !group.expand;
            this.$set(this.groups, index, group);
        },
        handleSwitch() {
            if (this.selectEntId === null) {
                return this.$MessageToast.error(this.$t('common.plsSelectEnt')); // 请先选择企业
            }
            this.$emit('switchAccount', this.selectEnt);
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        search: lazy(function() {
            this.selectEntId = null;
            this.searchList = query(this.enterprises, this.groups, this.query, item => item.fullEntName);
            /* 如果用户企业存在搜索列表中，则默认勾选 */
            if (this.searchList.some(perEnterprise => perEnterprise.entId === this.currentEntId)) {
                this.selectEntId = this.currentEntId;
            }
        }),
        clear() {
            this.selectEntId = null;
            this.query = '';
            this.searchList = [];
            this.loading = true;
        },
    },

};
</script>

<style scoped lang="scss">
    .search-pop {
        width: 100vw;
        height: 100vh;
    }
    .result-box {
        height: calc(85vh - 300px);
        margin-top: 30px;
        min-height: 150px;
        max-height: 335px;
        overflow-y: auto;
        /*box-shadow: 0 0 5px rgba(221,221,221,.7);*/
        .loading-box {
            margin-top: 25px;
            text-align: center;
            color: $--color-text-secondary;
            .el-icon-loading {
                font-size: 50px;
            }
        }
        .no-result-box {
            text-align: center;
            margin-top: 20px;
            color: $--color-text-secondary;
            .el-icon-ssq-meiyoujieguo1 {
                font-size: 56px;
            }
            .no-result-text {
                margin-top: 33px;
                font-size: 14px;
                width: 100%;
            }
        }
        .search-ul {
            li {
                padding: 10px 33px;
                position: relative;
                max-height: 62px;
                font-size: 14px;
                font-family: "Adobe Heiti Std";
                color: rgb(51, 51, 51);
                vertical-align: middle;
                line-height: 21px;
                &:first-child {
                    border-top: none;
                }
                &:hover {
                    background-color: $--color-primary-light-8;
                }
               .group-arrow {
                    position: absolute;
                    right: 37px;
                    top: 0;
                    color: #CCCCCC;
                    font-weight: bold;
                    font-size: 16px;
                    line-height: 45px;
                    vertical-align: top;
                }
            }
        }
    }
    .dialog-footer {
        .search-ok {
            padding: 10px 36px;
            border-radius: 4px;
        }
        .search-cancel {
            padding: 10px 15px;
            border-radius: 4px;
        }
    }
    .cursor-point {
        cursor: pointer;
    }
</style>
<style lang="scss">
    .search-pop {
        line-height: 1.2;
        .el-dialog {
            width: 600px;
            .el-dialog__header {
                padding: 15px 20px;
                .el-dialog__headerbtn {
                    top: 18px;
                }
            }
            .el-dialog__body {
                background-color: $--color-primary-light-9;
                padding-left: 0 !important;
                padding-right: 0 !important;
                .search-input-box  {
                    padding-right: 33px;
                    padding-left: 33px;
                    box-sizing: border-box;
                }
            }
            .el-dialog__footer {
                background-color: $--color-primary-light-9;
            }
        }
    }
</style>
