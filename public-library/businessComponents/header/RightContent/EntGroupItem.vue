<template>
    <!-- 查看全部企业弹窗中的item -->
    <div v-if="isAllCompanyDialog">
        <div class="select-company">
            <p>
                <span :class="{'is-group-ent':isGroup}">
                    <!-- 企业完成意愿性认证后显示企业名称 -->
                    <template v-if="perEnterprise.entName">
                        <template v-if="perEnterprise.authStatus == 2">
                            {{ perEnterprise.empName }}
                            <template v-if="perEnterprise.empName"> - </template>
                            <el-tooltip
                                effect="dark"
                                placement="top-start"
                                :content="perEnterprise.fullEntName"
                            >
                                <span>{{ perEnterprise.fullEntName }}</span>
                            </el-tooltip>
                            <template v-if="perEnterprise.isGroupProxyAuthStatus">{{ $t('common.groupAgentAuth') }}</template>
                        </template>
                        <template v-else-if="perEnterprise.ecologyFlag">
                            <template v-if="perEnterprise.entId === `0-${perEnterprise.clientId}-ecology`">
                                {{ commonHeaderInfo.platformUser.account }} （{{ $t('commonHeader.ecologyPerson',{developerName:perEnterprise.developerName}) }}）
                            </template>
                            <template v-else>
                                {{ perEnterprise.empName }}
                                <template v-if="perEnterprise.empName"> - </template>
                                {{ perEnterprise.entName }}（{{ $t('commonHeader.ecology',{developerName:perEnterprise.developerName}) }}）
                            </template>
                        </template>
                        <template v-else-if="perEnterprise.authStatus == 1">
                            <el-tooltip
                                effect="dark"
                                placement="top-start"
                                :content="perEnterprise.fullEntName"
                            >
                                <span> {{ perEnterprise.fullEntName }}（{{ $t('commonHeader.authenticating') }}）</span>
                            </el-tooltip>
                        </template>
                        <template v-else>
                            <el-tooltip
                                effect="dark"
                                placement="top-start"
                                :content="perEnterprise.fullEntName"
                            >
                                <span> {{ perEnterprise.fullEntName }}（{{ perEnterprise.ifAuthRejected ? $t('commonHeader.rejectAuthenticate') : $t('commonHeader.unAuthenticate') }}）</span>
                            </el-tooltip>
                        </template>
                    </template>
                    <template v-else>
                        {{ $t('commonHeader.entAccount') }} - {{ userInfo.platformUser.account }}
                    </template>
                </span>
            </p>
            <!-- 当前企业小图标 -->
            <i v-show="selectEntId === perEnterprise.entId"
                class="el-icon-ssq-qianyuewancheng curAccountInfo"
            ></i>
            <!-- 群主企业小图标 -->
            <i
                v-if="perEnterprise.isGroupAdmin"
                class="el-icon-ssq-wujiaoxing1  groupOwnerEnt"
                :class="{'isSelectEnt':selectEntId === perEnterprise.entId}"
            ></i>
        </div>
    </div>

    <!-- header右上角切换主体的item -->
    <div v-else>
        <div class="inline-block cursor-point content" :class="{'is-group-ent':isGroup}">
            <!-- 企业账号 -->
            <template v-if="perEnterprise.entId !== '0-ecology'">
                <!-- 企业完成意愿性认证后显示企业名称 或者生态版显示马甲 -->
                <template v-if="perEnterprise.entName">
                    <template v-if="perEnterprise.authStatus == 2">
                        <p
                            class="content-paragraph"
                            v-if="perEnterprise.empName"
                        >
                            {{ perEnterprise.empName
                            }}<span class="account-type-text">（{{ $t('commonHeader.entAccount') }}）</span>
                        </p>
                        <p class="content-paragraph">
                            {{ perEnterprise.bizName ? `${perEnterprise.entName}_${perEnterprise.bizName}` : perEnterprise.entName }}
                            <template v-if="perEnterprise.isGroupProxyAuthStatus">（<!-- 集团代认证 -->{{
                                $t('common.groupAgentAuth')
                            }}）</template>
                        </p>
                    </template>
                    <template v-else-if="perEnterprise.ecologyFlag">
                        <p
                            class="content-paragraph"
                            v-if="perEnterprise.empName"
                        >
                            {{ perEnterprise.empName
                            }}<span class="account-type-text">（{{ $t("commonHeader.ecology",{developerName:perEnterprise.developerName}) }}）</span>
                        </p>
                        <p class="content-paragraph">
                            {{ perEnterprise.entName }}
                        </p>
                    </template>
                    <template v-else-if="perEnterprise.authStatus == 1">
                        {{ perEnterprise.bizName ? `${perEnterprise.entName}_${perEnterprise.bizName}` : perEnterprise.entName }}
                        <span class="account-type-text">（{{ $t('commonHeader.authenticating') }}）</span>
                    </template>
                    <template v-else>
                        {{ perEnterprise.bizName ? `${perEnterprise.entName}_${perEnterprise.bizName}` : perEnterprise.entName }}
                        <span class="account-type-text">（{{ perEnterprise.ifAuthRejected ? $t('commonHeader.rejectAuthenticate') : $t('commonHeader.unAuthenticate') }}）</span>
                    </template>
                </template>

                <template v-else>
                    {{ userInfo.platformUser.account }}
                    <span class="account-type-text">（{{ $t('commonHeader.entAccount') }}）</span>
                </template>
            </template>
        </div>
        <!-- 当前企业小图标 -->
        <i
            v-show="userInfo.currentEntId === perEnterprise.entId"
            class="el-icon-ssq-qianyuewancheng curAccountInfo"
        ></i>
        <i v-if="perEnterprise.isGroupAdmin"
            class="el-icon-ssq-wujiaoxing1 groupOwnerEnt"
            :class="{'isSelectEnt':userInfo.currentEntId == perEnterprise.entId}"
        ></i>
    </div>

</template>

<script>
export default {
    name: 'NavItem',
    props: {
        // 企业信息
        perEnterprise: {
            type: Object,
            default: () => ({
            }),
        },
        // 用户信息
        userInfo: {
            type: Object,
            default: () => ({
            }),
        },
        // 是否是集团
        isGroup: {
            type: Boolean,
            default: false,
        },
        // 是否是从选择全部企业的弹窗中过来
        isAllCompanyDialog: {
            type: Boolean,
            default: false,
        },
        // 选中的entId(全部企业的弹窗中用)
        selectEntId: {
            type: String,
            default: '',
        },
    },
    methods: {
    },
};
</script>

<style lang="scss" scoped>
.select-company{
     p {
        line-height: 35px;
        width: 525px !important;
        overflow: hidden;
        text-overflow: ellipsis;
        text-align: left;
        padding-right: 24px;
        vertical-align: middle;
        max-height: 42px;
        span {
            overflow: hidden; // 溢出隐藏
            white-space: nowrap; // 限制一行显示
            text-overflow: ellipsis;// 显示不下省略号显示
        }
    }
    .is-group-ent{
        padding-left: 25px !important;
    }
    .group-mark{
        color: #333333;
        font-size: 14px;
        line-height: 40px;
        margin-right: 5px;
    }

    .curAccountInfo {
        position: absolute;
        right: 37px;
        top: 50%;
        transform: translateY(-7px);
        color: #127fd2;
        font-weight: bold;
        font-size: 14px;
        vertical-align: middle;
    }
    .groupOwnerEnt{
        position: absolute;
        right: 37px;
        top: 50%;
        transform: translateY(-7px);
        color: $--color-primary;
        font-weight: bold;
        font-size: 14px;
        vertical-align: middle;
    }
    .isSelectEnt{
        top: 73% !important;
    }
}
</style>
