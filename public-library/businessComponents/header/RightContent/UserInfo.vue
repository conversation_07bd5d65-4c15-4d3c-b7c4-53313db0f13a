<template>
    <!-- 账户信息 -->
    <div class="present-identity inline-block multi-identity"
        :class="presentIdentityClass"
    >
        <div
            class="user-info-wrapper"
            @click="switchPresentIdentityClick"
        >
            <p class="inline-block">
                <template v-if="userInfo.currentEntId !== '0'">
                    <template v-if="currentEnt">
                        <template v-if="getAuthStatus == 2">
                            {{ currentEmpName }}
                            <template v-if="currentEmpName">-</template>
                            {{ currentEnt }}
                            <template v-if="userInfo.enterprises[0].isGroupProxyAuthStatus"><!-- （集团代认证） -->{{ $t('common.groupAgentAuth') }}</template>
                        </template>
                        <template v-else-if="getAuthStatus == 1">{{ currentEnt }}（{{ $t('commonHeader.authenticating') }}）</template>
                        <template v-else> {{ unAuthEntName }}</template>
                    </template>
                    <template v-else>{{ $t('commonHeader.entAccount') }} - {{ userInfo.platformUser.account }}</template>
                </template>

                <template v-else>
                    {{ getAuthStatus == 2 ? userInfo.platformUser.fullName : userInfo.platformUser.account }}（{{ $t('commonHeader.personAccount') }}）
                </template>
            </p>
            <i v-if="canChangeUserInfo" class="inline-block arrow animated"></i>
            <ul v-if="userSwitchPopShow" class="user-switch-pop animated">
                <!---->
                <template v-if="!getIsLoginFromDeveloper">

                    <!-- 选中的非集团的其它企业，排第一位 -->
                    <template v-if="selectEnterprise">
                        <li
                            class="cursor-point"
                            :data-id="selectEnterprise.entId"
                            :key="selectEnterprise.entId"
                            @click="switchAccount(selectEnterprise)"
                        >
                            <EntGroupItem
                                :perEnterprise="selectEnterprise"
                                :userInfo="userInfo"
                                :isGroup="false"
                            ></EntGroupItem>
                        </li>
                    </template>

                    <!-- 集团 -->
                    <template v-if="firstGroupInfo">
                        <!-- 集团标题 -->
                        <li class="group-item">
                            <div class="inline-block cursor-point content">
                                <i
                                    class="el-icon-ssq-jituantubiao group-mark"
                                ></i> {{ firstGroupInfo.entGroupName }}
                            </div>
                            <i
                                class="el-icon-arrow-up group-arrow"
                            ></i>
                        </li>
                        <!-- 集团下子企业显示 -->
                        <li
                            v-for="perEnterprise in firstGroupInfo.enterprises"
                            class="cursor-point"
                            :data-id="perEnterprise.entId"
                            :key="perEnterprise.entId"
                            @click="switchAccount(perEnterprise)"
                        >

                            <EntGroupItem
                                :perEnterprise="perEnterprise"
                                :userInfo="userInfo"
                                :isGroup="true"
                            ></EntGroupItem>
                        </li>
                    </template>

                    <!-- 非集团的其它企业显示 -->
                    <template v-if="additionEnterprises.length>0">
                        <li
                            v-for="perEnterprise in additionEnterprises"
                            class="cursor-point"
                            :data-id="perEnterprise.entId"
                            :key="perEnterprise.entId"
                            @click="switchAccount(perEnterprise)"
                        >
                            <EntGroupItem
                                :perEnterprise="perEnterprise"
                                :userInfo="userInfo"
                                :isGroup="false"
                            ></EntGroupItem>
                        </li>
                    </template>

                    <!-- 个人主体-显示在下面 -->
                    <li
                        class="cursor-point"
                        :data-id="personSubject.entId"
                        :key="personSubject.entId"
                        @click="switchAccount(personSubject)"
                    >
                        <div class="inline-block cursor-point content">
                            <!-- 生态版个人账号 -->
                            <template v-if="personSubject.ecologyFlag">
                                {{
                                    $store.getters.getPlatformUserAuthStatus === 2
                                        ? userInfo.platformUser.fullName
                                        : userInfo.platformUser.account
                                }}
                                <span class="account-type-text">（{{ $t("commonHeader.ecologyPerson",{developerName:perEnterprise.developerName}) }}）</span>
                            </template>
                            <!-- 个人账号 -->
                            <template v-else>
                                {{
                                    $store.getters.getPlatformUserAuthStatus === 2
                                        ? userInfo.platformUser.fullName
                                        : userInfo.platformUser.account
                                }}
                                <span class="account-type-text">（{{ $t('commonHeader.personAccount') }}）</span>
                            </template>
                        </div>
                        <!-- 当前企业小图标 -->
                        <i
                            v-show="userInfo.currentEntId == personSubject.entId"
                            class="el-icon-ssq-qianyuewancheng curAccountInfo"
                        ></i>
                    </li>

                    <!-- 如果企业多于5个，或者多个集团（SAAS-26765），则"显示更多" -->
                    <li
                        v-if="userInfo.enterprises.length > 5 || userInfo.entGroups.groups.length > 1"
                        class="extra-ent-line extra-ent-more cursor-point"
                        @click="selectCreateEnt"
                    >
                        <span>{{ $t('commonHeader.viewAllEnt') }}</span>
                    </li>
                    <!-- 新增企业 -->
                    <li
                        class="extra-ent-line cursor-point"
                        @click="handleCreateEnt"
                    >
                        <i class="el-icon-ssq-jia"></i>
                        <span>{{ $t('commonHeader.addEnt') }}</span>
                    </li>
                </template>
                <li class="extra-ent-line" @click="toLogout">
                    <span class="logout cursor-point el-icon-ssq-tuichu"></span>
                    {{ $t('commonHeader.exit') }}
                </li>
            </ul>
        </div>
        <!-- 查询企业 -->
        <SelectCompany
            :visible.sync="selectCompany.visible"
            @switchAccount="switchAccount"
            :currentEntId="userInfo.currentEntId"
            :userInfo="userInfo"
        ></SelectCompany>
    </div>
</template>
<script>
/* eslint-disable eqeqeq */
import { mapState, mapGetters } from 'vuex';
import SelectCompany from './DialogSelectCompany.vue';
import EntGroupItem from './EntGroupItem.vue';
import { switchHandler } from 'pub-utils/business/switchEnt.js';

export default {
    components: {
        SelectCompany,
        EntGroupItem,
    },
    props: {
        userInfo: {
            type: Object,
            default: function() {
                return {};
            },
        },
        currentEnt: {
            type: String,
            default: '',
        },
        currentEmpName: {
            type: String,
            default: '',
        },
        presentIdentityClass: {
            type: Object,
            default: function() {
                return {};
            },
        },
        userSwitchPopShow: {
            type: Boolean,
        },
    },
    data() {
        return {
            selectCompany: {
                visible: false,
            },
        };
    },
    computed: {
        ...mapGetters([
            'getAuthStatus',
            'getIsLoginFromDeveloper',
            'getIsForeignVersion',
        ]),
        ...mapState(['commonHeaderInfo']),

        /* 排序，并只展示排序后的前5个 */
        enterprises() {
            // eslint-disable-next-line vue/no-side-effects-in-computed-properties
            return this.userInfo.enterprises
                .sort((a, b) => {
                    /* 既不是个人账号也不是被选中账号的，不进行排序 */
                    let compare = 0;
                    /* 个人账号排在第二优先级 */
                    if (a.entId == '0' || b.entId == '0') {
                        compare = a.entId == '0' ? -1 : 1;
                    }
                    /* 被选中账号排在第一优先级 */
                    if (
                        this.userInfo.currentEntId == a.entId ||
                        this.userInfo.currentEntId == b.entId
                    ) {
                        compare = this.userInfo.currentEntId == a.entId ? -1 : 1;
                    }
                    return compare;
                })
                .slice(0, 5);
        },
        // 是否加入集团
        hasGroup() {
            return this.userInfo.entGroups.groups.length > 0;
        },
        // 第一个集团信息
        firstGroupInfo() {
            return this.userInfo.entGroups.groups[0];
        },
        // 是否选中了集团
        selectGroup() {
            return this.userInfo.entGroups.groups.find(e => e.selectedGroup);
        },
        // 是否选中了非集团的企业
        selectEnterprise() {
            return this.userInfo.entGroups.enterprises.find(e => e.selectedFlag);
        },
        // 个人主体
        personSubject() {
            return  this.userInfo.entGroups.enterprises.find(e => e.entId === '0');
        },
        // 除了集团，补充的非集团的企业
        additionEnterprises() {
            let num = 5;
            if (this.selectEnterprise) {
                num--;
            }
            if (this.firstGroupInfo) {
                if (this.firstGroupInfo.enterprises.length >= num) {
                    num = 0;
                } else {
                    num = num  - this.firstGroupInfo.enterprises.length;
                }
            }

            // 过滤个人主体
            const enterpriseList = this.userInfo.entGroups.enterprises.filter(item => {
                if (this.selectEnterprise) {
                    return  item.entId !== '0' &&  item.entId !== this.selectEnterprise.entId;
                }
                return  item.entId !== '0';
            });
            // 过滤生态版个人主体
            const ecologyEntList = this.commonHeaderInfo.ecologyEnterprises.filter(item => {
                if (this.selectEnterprise) {
                    return  item.entId !== '0-ecology' &&  item.entId !== this.selectEnterprise.entId;
                }
                return  item.entId !== '0-ecology';
            });
            // 将生态版企业主体和旗舰版正常的企业合并在一起
            return [...enterpriseList, ...ecologyEntList].slice(0, num);
        },
        unAuthEntName() {
            let currentEnterprise = this.selectEnterprise;
            if (this.selectGroup) {
                currentEnterprise = this.firstGroupInfo.enterprises.find(ent => ent.entId == this.userInfo.currentEntId);
            }
            if (!currentEnterprise.entName) {
                return `${this.currentEnt}（${this.$t('commonHeader.unAuthenticate')}）`;
            }
            const entName = currentEnterprise.bizName ? `${currentEnterprise.entName}_${currentEnterprise.bizName}` : currentEnterprise.entName;
            return  `${entName}（${currentEnterprise.ifAuthRejected ? this.$t('commonHeader.rejectAuthenticate') :  this.$t('commonHeader.unAuthenticate')}）`;
        },
    },
    inject: ['canChangeUserInfo'],
    methods: {
        // 切换身份下拉
        switchPresentIdentityClick() {
            if (this.canChangeUserInfo) {
                this.$emit('switchPresentIdentityClick');
            }
        },
        // 登出账号
        toLogout() {
            this.$emit('commonPoint', '退出');
            this.$cookie.delete('foundation_account_into');
            this.$http.logOut().then(() => {
                this.$router.push(`${this.GLOBAL.rootPathName}/account-center/login`);
            });
        },
        // 新增企业
        handleCreateEnt() {
            this.$http('/ents/new-ent/check').then(res => {
                const { data: { ifCanCreate, unAuthedEntId } } = res;
                if (!ifCanCreate) {
                    this.$confirm(this.$t('commonHeader.createCompnayP.hasUnAuthEnt'), this.$t('commonHeader.tip'), {
                        confirmButtonText: this.$t('commonHeader.createCompnayP.toAuth'),
                        cancelButtonText: this.$t('commonHeader.createCompnayP.toCancel'),
                    }).then(async() => {
                        await this.$http.switchEntId(unAuthedEntId);
                        if (this.getIsForeignVersion) {
                            this.$router.push('/console/enterprise/account/setting');
                        } else {
                            this.$router.push('/auth-p/enterprise');
                        }
                    }).catch(async() => {
                        await this.$http.switchEntId(unAuthedEntId);
                        this.$router.push('/console/enterprise/account/setting');
                    });
                } else {
                    this.$emit('commonPoint', '新增企业');
                    this.switchPresentIdentityClick();
                    this.$emit('createCompany');
                }
            });
        },
        // 选择企业
        selectCreateEnt() {
            this.selectCompany.visible = true;
        },
        switchPersonConfirm() {
            return new Promise((resolve, reject) => {
                this.$confirm(`
                <h6>${this.$t('commonHeader.switchConfirm.main')}</h6>
                <p>${this.$t('commonHeader.switchConfirm.tip')}</p>
                `, this.$t('commonHeader.switchConfirm.title'), {
                    confirmButtonText: this.$t('commonHeader.switchConfirm.confirm'),
                    cancelButtonText: this.$t('commonHeader.switchConfirm.cancel'),
                    dangerouslyUseHTMLString: true,
                    type: 'warning',
                    customClass: 'sso-switch-person-dialog',
                    distinguishCancelAndClose: true,
                    beforeClose: (action, instance, done) => done(action),
                }).then(reject).catch(action => {
                    if (action !==  'close') {
                        resolve();
                    }
                });
            });
        },
        // 切换身份
        async switchAccount(needSwitchEnterprise) {
            this.$emit('commonPoint', '切换账号');
            // SAAS-28647 有企业主体的账号sso登录，切换至个人的时候二次提醒
            if (needSwitchEnterprise.entId === '0' && this.commonHeaderInfo.loginFromSSOFlag) {
                await this.switchPersonConfirm();
            }
            // 需要切换的企业主体
            const needSwitchEnterpriseId = needSwitchEnterprise.entId;
            // 需要切换的企业主体 id 与当前 id 相等时，不需要切换
            if (needSwitchEnterpriseId === this.userInfo.currentEntId) {
                return;
            }
            // 如果是生态版的马甲，进入生态版相关界面
            if (needSwitchEnterprise.ecologyFlag) {
                const tempWindow = window.open();
                // 生态版切换主体 跳转至生态版合同列表页
                return this.$http.get(`/ents/ecology-contract-url?entId=${needSwitchEnterprise.entId.split('-')[0]}&clientId=${needSwitchEnterprise.clientId}`).then(res => {
                    tempWindow.location = res.data.value;
                    this.selectCompany.visible = false;
                }).catch(() => {
                    tempWindow.close();
                    this.selectCompany.visible = false;
                });
            }
            this.$http
                .post(`/authenticated/switch-ent`, {
                    entId: needSwitchEnterpriseId,
                    refreshToken: this.$cookie.get('refresh_token'),
                })
                .then(({ data }) => {
                    switchHandler(data);
                });
        },
    },
};
</script>
