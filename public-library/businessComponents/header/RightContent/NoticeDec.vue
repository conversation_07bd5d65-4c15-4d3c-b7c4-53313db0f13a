<template>
    <div class="notice-dec">
        <div class="notice-dec-up"></div>
        <div class="notice-dec-main">
            <div class="notice-dec-title">
                <!-- 消息 -->{{ $t('noticeMap.notice') }}（{{ noticeCount.unreadMessageCount > 0 && !isRead?`${noticeCount.unreadMessageCount}`: 0 }}<!-- 条未读） -->{{ $t('noticeMap.notRead') }}
                <span class="fr notice-dec-read-all" @click="readAll"><!-- 全部已读 -->{{ $t('noticeMap.allRead') }}</span>
            </div>
            <ul class="notice-dec-list" v-loading="isLoading">
                <li @click="readNotice(item, index)" :class="{'no-read': !isRead && !item.isRead}" v-for="(item,index) in noticeList" :key="item.id">
                    <span class="line-title">{{ item.title }}</span><span class="fr line-time">{{ item.dataCreateTime | timeToString }}</span>
                    <p class="line-content">{{ item.content }}</p>
                </li>
            </ul>
            <div class="notice-dec-more" @click="goNotice"><!-- 查看更多 -->{{ $t('noticeMap.viewMore') }}</div>
        </div>
    </div>
</template>
<script>
import 'pub-filters';
import { goToAddr } from 'pub-utils/business/notice';
import { mapGetters } from 'vuex';
export default {
    props: {
        noticeCount: {
            type: Object,
            default: function() {
                return {};
            },
        },
    },
    data() {
        return {
            isRead: false,
            isLoading: false,
            noticeList: [],
        };
    },
    computed: {
        ...mapGetters(['getCurrentEntInfo']),
    },
    methods: {
        getUnReadList() {
            this.isLoading = true;
            this.$http.get('/ents/message/centre/unread', { params: {
                pageNum: 1,
                pageSize: 20,
            } }).then(res => {
                if (res && res.data) {
                    const noticeCount = this.noticeCount;
                    noticeCount.unreadMessageCount = res.data.total || 0;
                    this.$emit('updateNoticeCount', noticeCount);
                    this.noticeList = res.data.list || [];
                }
            }).finally(() => {
                this.isLoading = false;
            });
        },
        // 全部已读
        async readAll() {
            if (this.noticeCount.unreadMessageCount <= 0 || this.isRead) {
                return;
            }

            this.isRead = true;
            this.$http.post('/ents/message/centre/all', {
                noticeType: '',
            }).then(() => {
                this.$emit('updateNoticeCount', {
                    noticeUnreadMessageCount: 0,
                    contractUnreadMessageCount: 0,
                    unreadMessageCount: 0,
                    approvalUnreadMessageCount: 0,
                });
            });
        },
        // 点击消息
        async readNotice(item, index) {
            // 更新已读消息数量
            if (!item.isRead && !this.isRead) {
                item.isRead = true;
                this.noticeList.splice(index, 1, item);

                const noticeCount = this.noticeCount;
                noticeCount.unreadMessageCount--;
                this.$http.post(`/ents/message/centre/${item.id}/read?from=0`).then(() => {
                    this.$emit('updateNoticeCount', noticeCount);
                });
            }
            // 跳转详情页不需要切换主体，所以取当前的entId
            item.entId = this.getCurrentEntInfo.entId;
            goToAddr(item);// 跳转详情页
        },
        // 点击查看更多消息
        goNotice() {
            this.$emit('changeStatus');
            this.$router.push('/account-center/notice');
        },
    },
    mounted() {
        this.getUnReadList();
    },
};
</script>
<style lang="scss">
    .notice-dec{
        position: absolute;
        top: 48px;
        right: 1px;
        display: block;
        width: 410px;
        height: 413px;
        z-index: 2;
        .notice-dec-up{
            width: 0;
            height: 0;
            border-width: 0 5px 5px;
            border-style: solid;
            border-color: transparent transparent $--color-white;
            position: absolute;
            top: 0;
            right: 40px
        }
        .notice-dec-main{
            background: $--color-white;
            margin-top: 5px;
            margin-right: 5px;
            box-shadow: 0 0 5px rgba(221, 221, 221, 0.7);
            border-radius: 4px;
            height: 407px;
            .notice-dec-title{
                padding: 0 24px;
                line-height: 58px;
                font-weight: bold;
                font-size: 16px;
                color: rgba(0,0,0,0.85);
                .notice-dec-read-all{
                    color: $--color-primary-light-1;
                    cursor: pointer;
                    font-size: 14px;
                    font-weight:normal;
                }
            }
            .notice-dec-list{
                padding: 3px 24px 0 38px;
                height: 283px;
                overflow-y:auto;
                border-bottom: 1px solid $--border-color-lighter;
                border-top: 1px solid $--border-color-lighter;
                li{
                    padding: 6px 0 10px 0;
                    border-bottom: 1px solid $--border-color-lighter;
                    position: relative;
                    cursor: pointer;
                    line-height: 18px;
                    color: rgba(0,0,0,0.75);
                    font-size: 12px;
                    &:hover{
                        color: $--color-primary-light-2;
                        .line-title,.line-time{
                            color: $--color-primary-light-2;
                        }
                    }
                    &.no-read::before{
                        width: 6px;
                        height: 6px;
                        border-radius: 6px;
                        background-color:$--color-danger;
                        content: '';
                        display: inline-block;
                        position: absolute;
                        top: 14px;
                        left: -14px;
                    }
                    .line-title{
                        font-size: 14px;
                        color: $--color-text-primary;
                        line-height: 22px;
                    }
                    .line-time{
                        color: $--color-text-secondary;
                        line-height: 22px;
                    }
                }
            }
            .notice-dec-more{
                border-radius: 4px;
                background-color: $--color-primary-light-1;
                color: $--color-white;
                text-align: center;
                box-sizing: content-box;
                cursor: pointer;
                width: 88px;
                line-height: 30px;
                margin: 18px auto 0;
                font-size: 14px;
            }

        }
    }
</style>
