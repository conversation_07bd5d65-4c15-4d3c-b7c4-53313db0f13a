<template>
    <ul>
        <template v-for="(item, index) in navList">
            <li
                :key="item.id"
                v-if="item.visible"
                v-module-id="item.moduleId"
                :data-href="item.href"
                :class="['inline-block' ,'cursor-point','nav__li', item.class, {
                    'router-link-active nav__li-active': activeIndex === index
                }]"
                :data-id="item.id"
            >
                <div class="nav__li-name" @click="goToLink(item.href, item.router, index, item.zhText)">{{ item.text }}</div>
                <NavList
                    v-if="item.children && item.children.filter(v => v.visible).length"
                    class="subnav__list"
                    :navList="item.children"
                />
            </li>
        </template>
    </ul>
</template>

<script>
export default {
    name: 'NavList',
    props: {
        navList: {
            type: Array,
            default: function() {
                return [];
            },
        },
    },
    data() {
        return {
            activeIndex: 0,
        };
    },
    methods: {
        isActive(href) {
            return href && location.pathname.indexOf(href) > -1;
        },
        goToLink(hostName, isSpa, index, zhText) {
            if (!hostName) {
                return;
            }
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.activeIndex === -1 ? location.pathname : this.navList[this.activeIndex].zhText,
                    first_category: '顶部导航栏',
                    icon_name: zhText,
                },
            });
            this.activeIndex = index;
            if (isSpa) {
                this.$router.push(hostName);
            } else {
                window.location.href = `//${window.location.host}${hostName}`; // ie10 一下不支持 location.origin
            }
        },
        initActiveIndex() {
            this.activeIndex = this.navList.findIndex(el => {
                return this.isActive(el.href);
            });
        },
    },
    created() {
        this.initActiveIndex();
    },
};
</script>

<style lang="scss">
.common-header-nav__list {
    display: inline-block;
    margin-left: 55px;
    height: $header-height;
    line-height: $header-height;
    [dir='rtl'] & {
        margin-left: 0;
        margin-right: 55px;
    }
    .nav__li {
        text-align: center;
        color: $--color-text-placeholder;
        font-size: 14px;
        position: relative;
        &.router-link-active,
        &:hover {
            background-color: $header-color-light;
            background: -webkit-gradient(
                linear,
                left top,
                left bottom,
                from($header-color-light),
                to($header-color)
            );
            background: -webkit-linear-gradient(to bottom, $header-color-light, $header-color);
            background: -moz-linear-gradient(to bottom, $header-color-light, $header-color);
            background: -ms-linear-gradient(to bottom, $header-color-light, $header-color);
            background: -o-linear-gradient(to bottom, $header-color-light, $header-color);
            background: linear-gradient(to bottom, $header-color-light, $header-color);
            color: $--color-white;
        }
        .nav__li-name {
            @include nav-li-padding;
        }
        &:hover > .subnav__list {
            display: block;
        }
        & > .subnav__list {
            display: none;
            position: absolute;
            top: 100%;
            left: 50%;
            z-index: 99;
            transform: translateX(-50%);
            height: auto;
            width: 110px;
            padding: 5px 0;
            margin: 0;
            margin-top: -3px;
            white-space: nowrap;
            background: $--color-white;
            border: 1px solid #d1dbe5;
            border-radius: 3px;
            box-shadow: 0 2px 4px 0 rgba(0,0,0,.12), 0 0 6px 0 rgba(0,0,0,.04);

            .nav__li {
                display: block;
                padding: 0;
                height: auto;
                text-align: center;
                font-size: 12px;
                color: $--color-text-regular;
                line-height: 30px;
                &:hover,
                &.nav__li-active {
                    background: $--color-primary-light-9;
                    color: $--color-primary;
                }
            }
        }
    }

}

</style>
