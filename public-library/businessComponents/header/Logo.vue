<template>
    <h1 class="common-header-logo inline-block cursor-point" @click="$emit('clickLogo')" v-if="!logoConfig || logoConfig.visible">
        <template v-if="!logoConfig || logoConfig.clickAble">
            <template v-if="currentLogoStatus == 0">
                <router-link tag="div" :to="`${GLOBAL.rootPathName}/account-center/home`">
                    <img
                        class="default-logo cur-pointer"
                        :src="GLOBAL.WHITE_LOGO"
                        width="86"
                        height="37"
                        alt
                    >
                </router-link>
            </template>
            <router-link
                v-else
                class="entLogo"
                tag="img"
                :to="`${GLOBAL.rootPathName}/account-center/home`"
                :src="currentLogoSrc"
            ></router-link>
        </template>
        <!-- 不可点击 -->
        <template v-else>
            <img
                v-if="currentLogoStatus == 0"
                class="default-logo"
                :src="GLOBAL.WHITE_LOGO"
                width="86"
                height="37"
                alt
            >
            <!-- <i v-if="currentLogoStatus == 0" class="el-icon-ssq-fenzu3"></i> -->
            <img v-else class="entLogo" :src="currentLogoSrc" alt="企业logo">
        </template>
    </h1>
</template>
<script>
export default {
    props: {
        currentLogoSrc: {
            type: String,
            default: '',
        },
        logoConfig: {
            type: Object,
            default: function() {
                return {
                    visible: true,
                    clickAble: true,
                };
            },
        },
        currentLogoStatus: {
            type: Number,
            default: 0,
        },
    },
};
</script>
<style lang="scss">
.common-header-logo {
    margin-left: 18px;
    // width: 124px;
    height: $header-height;
    line-height: $header-height;
    color: $--color-white;
    font-size: 12px;
    [dir="rtl"] & {
        margin-left: 0;
        margin-right: 18px;
    }
    i {
        color: $--color-white;
        font-size: 37px;
        vertical-align: middle;
    }
    img.entLogo {
        width: 90px;
        height: 30px;
        vertical-align: middle;
    }
}
</style>
