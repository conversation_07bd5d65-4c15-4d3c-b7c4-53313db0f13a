<!-- 业务组件：公共头部 -->
<!-- 引用位置 基本页面均用-->
<template>
    <div class="common-header" :class="{'common-header-ja': isJa, 'mini-mode': userInfo.hasGroupConsole && userInfo.hasManager && !isReceiverView}">
        <div class="common-header-width">
            <!-- 左侧logo -->
            <Logo
                :currentLogoSrc="currentLogoSrc"
                :logoConfig="logoConfig"
                :currentLogoStatus="currentLogoStatus"
                @clickLogo="clickLogo"
            >
            </Logo>
            <!-- 菜单列表 -->
            <NavList v-if="!noNavList"
                ref="nav"
                class="common-header-nav__list"
                :navList="navList"
            >
            </NavList>

            <!-- 右侧图标内容 -->
            <RightContent
                ref="rightContent"
                :noNavList="noNavList"
                :userInfo="userInfo"
                :currentEnt="currentEnt"
                :currentEmpName="currentEmpName"
                :isReceiverView="isReceiverView"
                :getActivatedMenu="getActivatedMenu"
            ></RightContent>
        </div>
    </div>
</template>
<script>
import resRules from 'pub-utils/regs.js';

import NavList from './NavList.vue';
import Logo from './Logo.vue';
import RightContent from './RightContent/';

import { mapState, mapGetters } from 'vuex';
import { lightenColor, addCssRule } from 'pub-utils/index.js';

export default {
    components: {
        NavList,
        RightContent,
        Logo,
    },
    props: {
        logoConfig: {
            type: Object,
            default: function() {
                return {
                    visible: true,
                    clickAble: true,
                };
            },
        },
        pageModule: {
            type: String,
            default: '',
        },
        noNavList: {
            type: Boolean,
            default: false,
        },
        isReceiverView: {
            type: Boolean,
            default: false,
        },
        canChangeUserInfo: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            //   hostIsCn: window.location.host.includes("bestsign.cn"),
            hostIsCn: this.GLOBAL.ENV_NAME === 'PE_ENV',
            HOST_ENV: this.GLOBAL.HOST_ENV,
            refresh_token: this.$cookie.get('refresh_token'),
            currentEnt: '',
            currentEmpName: '',
            currentAuthStatus: '',
            currentLogoStatus: 0, // 0 没有 1 有
            currentLogoSrc: '',
            userInfo: {
                currentEntId: '',
                hasManager: false,
                isCanCreateEnterprise: false,
                enterprises: [],
                platformUser: {
                    fullName: '',
                },
                userType: '', // Enterprise
            },
            resRules: resRules,
            couldRevokeIdList: [],
        };
    },
    provide() {
        return {
            canChangeUserInfo: this.canChangeUserInfo,
        };
    },
    computed: {
        ...mapState(['features', 'commonHeaderInfo']),
        ...mapGetters([
            'getHybridUserType',
            'checkFeat',
            'getUserPermissons',
            'getIsForeignVersion',
        ]),
        navList() {
            if (this.isReceiverView) {
                return [
                    {
                        visible: true,
                        router: true,
                        text: this.$t('commonHeader.home'),
                        href: `${this.GLOBAL.rootPathName}/statistics/receiver-perspective/home`,
                        id: 'home',
                        moduleId: this.pageModule + '_1_home',
                        zhText: '首页',
                    },
                    {
                        visible: true,
                        router: true,
                        text: this.$t('commonHeader.businessStaff'),
                        href: `${this.GLOBAL.rootPathName}/statistics/receiver-perspective/business-staff/detail`,
                        id: 'business',
                        moduleId: this.pageModule + '_1_sender',
                        zhText: '按业务对接人管理',
                    },
                    {
                        visible: true,
                        router: true,
                        text: this.$t('commonHeader.sender'),
                        href: `${this.GLOBAL.rootPathName}/statistics/receiver-perspective/sender-manage`,
                        id: 'sender',
                        moduleId: this.pageModule + '_1_sender',
                        zhText: '按发件方管理',
                    },
                ];
            }
            return [
                {
                    visible: true,
                    router: true,
                    text: this.$t('commonHeader.home'),
                    href: `${this.GLOBAL.rootPathName}/account-center/home`,
                    id: 'home',
                    moduleId: this.pageModule + '_1_home',
                    zhText: '首页',
                },
                {
                    visible: true,
                    router: true,
                    text: this.$t('commonHeader.contractManagement'),
                    href: `${this.GLOBAL.rootPathName}/sign-flow/doc-manage/list`,
                    id: 'doc',
                    moduleId: this.pageModule + '_1_doc',
                    zhText: '合同管理',
                },
                {
                    visible: this.userInfo.userType === 'Enterprise' && this.checkFeat.template,
                    router: true,
                    text: this.$t('commonHeader.templateManage'),
                    href: `${this.GLOBAL.rootPathName}/sign-flow/template/list`,
                    id: 'tem',
                    moduleId: this.pageModule + '_1_tpl',
                    zhText: '模板管理',
                },
                {
                    visible: this.userInfo.userType === 'Enterprise' && this.getUserPermissons.statistics && !this.getIsForeignVersion,
                    router: true,
                    text: this.$t('commonHeader.statisticCharts'),
                    href: `${this.GLOBAL.rootPathName}/statistics`,
                    id: 'statistics',
                    moduleId: this.pageModule + '_1_stats',
                    zhText: '统计报表',
                },
                {
                    visible: true,
                    router: true,
                    text: this.$t('commonHeader.userCenter'),
                    href: `${this.GLOBAL.rootPathName}/usercenter`,
                    id: 'usercenter',
                    moduleId: this.pageModule + '_1_usc',
                    zhText: '用户中心',
                },
                {
                    visible: this.userInfo.currentEntId !== '0' && this.features.includes('86'),
                    router: true,
                    text: this.$t('commonHeader.fileService'),
                    href: `${this.GLOBAL.rootPathName}/box/`,
                    id: 'databox',
                    moduleId: this.pageModule + '_1_dtb',
                    class: 'nav-item-box',
                    zhText: '档案+',
                },
            ];
        },
        cmVisible() {
            const isPublic = this.getHybridUserType === 'public';
            const isEnterprise = this.userInfo.userType === 'Enterprise';
            // src/pages/common/doc/list/common/Doc-Slider.vue 中的 couldSeeContract 逻辑
            // const couldSeeContract = !!(this.$store.getters.doc_getUserRole || []).reduce((prev, cur) => {
            //     return prev.concat(cur.privileges);
            // }, []).find(v => v.name === 'VIEW_CONTRACT');
            return isPublic && isEnterprise;
        },
        lang() {
            return this.$i18n.locale;
        },
        isJa() {
            return this.lang === 'ja';
        },
    },

    watch: {
        commonHeaderInfo: {
            handler(val) {
                this.initData(val);
            },
            deep: true,
        },
    },
    methods: {
        getActivatedMenu() {
            const nav = this.$refs.nav;
            if (nav) {
                const { activeIndex } = nav;
                return activeIndex === -1 ? location.pathname : this.navList[activeIndex].zhText;
            }
            return location.pathname;
        },
        clickLogo() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.getActivatedMenu(),
                    first_category: '顶部导航栏',
                    icon_name: '返回云平台首页图标',
                },
            });
        },
        initData(value) {
            // eslint-disable-next-line new-cap
            this.userInfo = value;

            // let personalIndex;
            for (var i = 0; i < this.userInfo.enterprises.length; i++) {
                const temO = this.userInfo.enterprises[i];
                // if (temO.entId === '0') {
                //     personalIndex = i;
                // }
                if (temO.entId === this.userInfo.currentEntId) {
                    this.currentEnt = temO.fullEntName;
                    this.currentAuthStatus = temO.authStatus;
                    this.currentEmpName = temO.empName;
                    if (temO.logoFileId || this.$cookie.get('homeLogoFileId')) {
                        this.currentLogoStatus = 1;
                        this.currentLogoSrc = `/ents/logo?t=${Date.parse(new Date())}`;
                    } else {
                        this.currentLogoStatus = 0;
                    }
                }
            }
        },
    },
    created: function() {
        this.initData(this.commonHeaderInfo);
    },
    mounted() {
        if (this.commonHeaderInfo.mainMenuColor) {
            addCssRule('.common-header', `background: #${this.commonHeaderInfo.mainMenuColor} !important`);

            const startColor = `#${this.commonHeaderInfo.mainMenuColor}`;
            const endColor = lightenColor(this.commonHeaderInfo.mainMenuColor, 10);
            const backgroundActive = ['-webkit-', '-moz-', '-o-', '-ms-', ''].map(prefix => `background:${prefix}linear-gradient(to bottom, ${startColor}, ${endColor})`).join(';') + ' !important';
            addCssRule(`.common-header .common-header-nav__list .nav__li.router-link-active,
            .common-header .common-header-nav__list .nav__li:hover,
            .common-header .present-identity.multi-identity:hover,
            .common-header .present-identity.multi-identity.active`, backgroundActive);
        }
    },

};
</script>
<style lang="scss">
.common-header {
    width: 100%;
    height: $header-height;
    background-color: $header-color;
    font-size: 12px;
    position: relative;
    z-index: 100;
    * {
        box-sizing: border-box;
    }
    .unautherized-con {
        position: absolute;
        top: 0;
        left: 0;
        width: 100%;
        height: $header-height;
        line-height: $header-height;
        text-align: center;
        background-color: $header-color;
        i:nth-of-type(1) {
            padding-right: 3px;
            vertical-align: middle;
            color: $--color-danger;
            font-size: 17px;
        }
        i:nth-of-type(2) {
            position: relative;
            left: 20%;
            color: $--color-text-regular;
            font-size: 15px;
            cursor: pointer;
            &:hover {
                color: $--color-white;
            }
        }
        [dir=rtl] & {
            right: 0;
            left: auto;
            i:nth-of-type(1) {
                padding-right: 0;
                padding-left: 3px;
            }
            i:nth-of-type(2) {
                left: auto;
                right: 20%;
            }
        }
        span,
        p {
            font-size: 12px;
        }
        span:nth-of-type(1) {
            color: $--color-white;
            em {
                font-size: 14px;
                font-weight: bold;
                text-decoration: underline;
            }
        }
        span:nth-of-type(2) {
            color: $--color-text-secondary;
        }
        p {
            display: inline-block;
            width: 56px;
            height: 26px;
            line-height: 26px;
        }
    }
    .common-header-width {
        position: relative;
        height: 100%;
        margin: 0 auto;
        font-size: 0;
    }
    .cursor-point {
        cursor: pointer;
    }
    .text-align-right {
        text-align: right;
        [dir=rtl] & {
            text-align: left;
        }
    }
    .text-align-center {
        text-align: center;
    }
    .no-cursor-point {
        cursor: auto;
    }

    .present-identity {
        position: relative;
        width: 240px;
        height: 100%;
        line-height: $header-height;
        //margin-left: 15px;
        padding-left: 15px;
        padding-right: 15px;
        color: $--color-white;
        text-align: left;
        vertical-align: top;
        font-size: 12px;
        [dir='rtl'] & {
            text-align: right;
        }
        &.multi-identity {
            cursor: pointer;
        }
        &.multi-identity:hover,
        &.multi-identity.active {
            background-color: $header-color-light;
            background: -webkit-gradient(
                    linear,
                    left top,
                    left bottom,
                    from($header-color-light),
                    to($header-color)
            );
            background: -webkit-linear-gradient(to bottom, $header-color-light, $header-color);
            background: -moz-linear-gradient(to bottom, $header-color-light, $header-color);
            background: -ms-linear-gradient(to bottom, $header-color-light, $header-color);
            background: -o-linear-gradient(to bottom, $header-color-light, $header-color);
            background: linear-gradient(to bottom, $header-color-light, $header-color);
        }
        p {
            width: 200px;
            overflow: hidden;
            white-space: nowrap;
            text-overflow: ellipsis;
        }
        .arrow {
            position: absolute;
            top: 29px;
            width: 0;
            height: 0;
            border: 4px solid transparent;
            border-top: 6px solid $--color-white;
            transition: transform 0.2s ease-in;
        }
        .user-info-wrapper{
            height: 100%;
        }
    }
    .user-switch-pop {
        position: absolute;
        top: $header-height;
        right: 0;
        display: block;
        width: auto;
        min-width: 240px;
        min-height: 45px;
        // max-width: 265px;
        box-shadow: 0 0 5px rgba(221, 221, 221, 0.7);
        background-color: $--color-white;
        z-index: 2;
        li {
            position: relative;
            padding: 12px 30px 12px 15px;
            border-top: 1px solid $--border-color-lighter;
            color: $--color-text-primary;
            line-height: 1.5;
            &:first-child {
                border-top: none;
            }
            &:hover {
                background-color: $--background-color-base;
            }
            .content {
                min-width: 193px;
                width: auto;
                display: block;
                white-space: nowrap;
                text-overflow: ellipsis;
                text-align: left;
                .content-paragraph {
                    width: auto;
                    font-size: 12px;
                    line-height: 1.5;
                }
            }

            &.extra-ent-line {
                color: $--color-primary;
                font-size: 14px;
                text-align: center;
                p {
                    text-align: center;

                    i {
                        font-weight: bold;
                    }
                }
            }

            .log-off-btn {
                position: absolute;
                right: 10px;
                top: 0;
                color: $--color-primary;
                font-size: 12px;
                line-height: 45px;
                vertical-align: top;

                &.logOffCurEnt {
                    right: 35px;
                }
            }
        }
        .is-group-ent{
            padding-left: 25px;
        }
        .group-mark{
            color: #333333;
            font-size: 14px;
            line-height: 40px;
            margin-right: 5px;
        }
        .group-arrow{
            position: absolute;
            right: 10px;
            top: 7px;
            color: #CCCCCC;
            font-weight: bold;
            font-size: 16px;
            line-height: 45px;
            vertical-align: top;
        }
        .curAccountInfo {
            position: absolute;
            right: 10px;
            top: 15px;
            color: $--color-primary;
            font-weight: bold;
            font-size: 14px;
            vertical-align: top;
            [dir=rtl] & {
                right: auto;
                left: 10px;
            }
        }
        .groupOwnerEnt{
            position: absolute;
            right: 10px;
            top: 25px;
            color: $--color-primary;
            font-weight: bold;
            font-size: 14px;
            vertical-align: top;
            [dir=rtl] & {
                right: auto;
                left: 10px;
            }
        }
        .isSelectEnt{
            top: 30px !important;
        }
        .account-type-text {
            color: $--color-text-secondary;
        }
    }
    .mask {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        z-index: 1;
    }

    .right-content{
        position: absolute;
        right: 18px;
        top: 0;
        height: 100%;
        background-color: #002b45;
        [dir='rtl'] & {
            right: auto;
            left: 18px;
        }
    }
    .noNavList{
        cursor: pointer;
        line-height: 63px;
        font-size: 14px;
        color: #fff;
    }
}

// 日文环境样式兼容 开始
.common-header-ja{
    .common-header-width{
        width: 100%;
        .common-header-logo{
            margin-left: 40px;
        }
        .common-header-nav__list{
            margin-left: 25px;
            .nav__li{
                .nav__li-name{
                    padding: 0 15px;
                    font-size: 12px;
                    font-weight: 400;
                }
            }
        }
        .common-herder-right-content{
            .console-entrance.btn-type-one{
                width: 150px;
                font-size: 12px;
                font-weight: 400;
            }
        }
    }
}
// 当同时存在集团控制台和企业控制台时
.mini-mode{
    @media only screen and (max-width: 1440px) {
		.common-header-width{
            width: 100%;
            .common-header-logo{
                margin-left: 22px;
            }
            .common-header-nav__list{
                .nav__li{
                    .nav__li-name{
                        padding: 0 12px;
                    }
                }
            }
        }
	}
}
// 日文环境样式兼容 结束

.hybridConnectedDialog {
    width: 296px;

    .el-message-box__message {
        line-height: 25px;
    }
}
.sso-switch-person-dialog {
    .el-message-box__container {
        .el-message-box__status {
            position: absolute;
            font-size: 24px !important;
            top: 0px;
            transform: translateY(0);
            left: -10px;
            [dir=rtl] & {
                left: auto;
                right: -10px;
            }
        }
        .el-message-box__message {
            p {
                font-size: 14px;
                color: $--color-text-secondary;
                line-height: 22px;
            }
            h6 {
                color: $--color-text-primary;
                margin-bottom: 10px;
            }
        }
    }
}
</style>
