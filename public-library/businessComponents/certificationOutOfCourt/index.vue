<!-- 业务组件：实名认证-被驳回部分 -->
<!-- 引用位置  用户中心页面：certification-->
<template>
    <div class="certification-out-of-court clear"
        :class="{
            'isPC': isPC,
            'isPhone': !isPC
        }"
    >
        <div class="ent-out-of-court">
            <h3 v-if="showTip">
                <i class="el-icon-ssq-wrong-filling"></i>
                <span>{{ $t('certificationOutOfCourt.your') }}{{ formateRejectInfo }}{{ $t('certificationOutOfCourt.dataRejected') }}</span>
            </h3>
            <div class="rejectInfoList" v-if="filteredRejectInfo.length">
                <p v-for="(item, index) in filteredRejectInfo" :key="index">
                    <span class="error-label">{{ $t('certificationOutOfCourt.authRejectReason') }}{{ index + 1 }}：</span>
                    {{ item.auditInfo }}
                </p>
            </div>
        </div>
        <div class="person-out-of-court" v-if="type === '1'">
            <div class="line">
            </div>
            <div>
                <h1>{{ $tc('certificationOutOfCourt.yourAuthInfo', 0) }}</h1>
                <p>
                    <span class="inline-block">{{ $tc('certificationOutOfCourt.yourAuthInfo', 1) }}</span>
                    <span>{{ name }}</span>
                </p>
                <p>
                    <span class="inline-block">{{ $tc('certificationOutOfCourt.yourAuthInfo', 2) }}</span>
                    <span class="inline-block">{{ idNumber }}</span>
                </p>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        isPC: {
            type: Boolean,
            default: true,
        },
        type: {
            type: String,
            default: '1', // 0 企业 1 个人
        },
        name: {
            type: String,
            default: '',
        },
        idNumber: {
            type: String,
            default: '',
        },
        rejectInfo: {
            type: Array,
            default: function() {
                return [];
            },
        },
        showTip: {
            type: Boolean,
            default: true,
        },
    },
    computed: {
        // 过滤掉驳回信息为空的数据
        filteredRejectInfo() {
            return this.rejectInfo.length > 0 ? this.rejectInfo.filter(item => {
                // eslint-disable-next-line eqeqeq
                return (item.auditInfo && item.auditInfo != '');
            }) : [];
        },
        // 根据驳回数据找出对应驳回的是哪种认证方式
        formateRejectInfo() {
            let rejectInfoStr = '';
            for (let i = this.rejectInfo.length - 1; i >= 0; i--) {
                const temO = this.rejectInfo[i];
                switch (temO.authinfoType) {
                    case 'allInfo':
                        rejectInfoStr = this.$t('certificationOutOfCourt.baseInfo');
                        break;
                    case 'baseInfo':
                        rejectInfoStr =  this.$t('certificationOutOfCourt.baseInfo');
                        break;
                    case 'inHand':
                        rejectInfoStr =  this.$t('certificationOutOfCourt.handheldIdNum'); // 手持身份证认证
                        break;
                    case 'face':
                        rejectInfoStr =  this.$t('certificationOutOfCourt.faceAuth'); // 刷脸认证
                        break;
                    case 'notMainland':
                        rejectInfoStr =  this.$t('certificationOutOfCourt.noMainlandAuth'); // 非大陆认证
                        break;
                }
                if (rejectInfoStr) {
                    return rejectInfoStr;
                }
            }
            return rejectInfoStr;
        },
    },
};
</script>

<style lang="scss">
    .certification-out-of-court {
        padding: 30px 0;
        background-color: $--background-color-regular;
        border: 1px solid $--border-color-light;
        .ent-out-of-court, .line, .person-out-of-court {
            display: inline-block;
            vertical-align: middle;
        }
        .ent-out-of-court {
            h3{
                margin-left: 60px;
                margin-bottom: 12px;
                color: $--color-text-primary;
                span, i{
                    font-size: 22px;
                    vertical-align: middle;
                }

                i{
                    margin-right: 10px;
                    color: $--color-danger;
                }
            }

            .rejectInfoList {
                p{
                    line-height: 20px;
                    font-size: 12px;
                    color: $--color-text-regular;
                }
                .error-label{
                    font-size: 12px;
                    color: $--color-danger;
                }
            }
        }

        .person-out-of-court{
            div:nth-child(1), div:nth-child(2) {
                vertical-align: top;
            }
            div:nth-child(1) {
                width: 45px;
                height: 90px;
                border-right: 1px dashed $--border-color-light;
            }
            div:nth-child(2) {
                display: inline-block;
                margin-left: 20px;
                h1 {
                    margin-bottom: 10px;
                    font-weight: bold;
                }
                p {
                    margin-bottom: 2px;
                    color: $--color-text-regular;
                    span:nth-child(1) {
                        min-width: 60px;
                        text-align:justify;
                        text-justify:distribute-all-lines;/*ie6-8*/
                        text-align-last:justify;/* ie9*/
                        -moz-text-align-last:justify;/*ff*/
                        -webkit-text-align-last:justify;/*chrome 20+*/
                    }
                    &:nth-of-type(3) {
                        span:nth-child(2) {
                            color: $--color-primary-light-2;
                        }
                    }
                }
            }
        }
        &.isPC {
            width: 600px;
            .ent-out-of-court, .person-out-of-court{
                display: block;
                text-align: center;
            }
            .rejectInfoList p{
                font-size: 14px;
                .error-label{
                    font-size: 14px;
                }
            }
        }
        &.isPhone {
            width: auto;
           margin-left: 19px;
           margin-right: 19px;
           .rejectInfoList {
                margin-left: 10px;
            }
        }

    }
</style>
