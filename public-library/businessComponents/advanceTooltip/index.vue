<!-- 业务组件：高级功能试用tooltip -->
<template>
    <el-tooltip
        :value="showToolTip && checkTrialInfoStatus(featureId,1)"
        :manual="true"
        effect="light"
        :placement="placement"
        :popper-class="popperClass"
    >
        <div slot="content" @click="handleTrialClick(featureId)" class="content"><i class="el-icon-ssq-jingshitanhaox"></i>{{ checkTrialInfoStatus(featureId,3) }}</div>
        <template v-if="isBtnType">
            <el-button
                :plain="btnIsPlain"
                @click="handleBtnClick"
                :class="btnClass"
            >
                <i v-if="showAddIcon" class="el-icon-ssq-jia"></i>
                {{ btnText }}
                <span v-if="moreConditionShowNew && checkTrialInfoStatus(featureId,2)" class="trial">
                    <span v-if="showNewBg" class="icon-bg-box"></span>
                    <i class="el-icon-ssq-biaoqiannew"></i>
                </span>
            </el-button>
        </template>
        <template v-else>
            <span @click="handleBtnClick">
                <slot name="tipContent"></slot>
                <span v-if="showNew && checkTrialInfoStatus(featureId,2)" class="trial">
                    <i class="el-icon-ssq-biaoqiannew"></i>
                </span>
            </span>
        </template>
    </el-tooltip>
</template>

<script>
import { advancedFeatureMixin } from 'pub-mixins/advancedFeature.js';
import { mapGetters } from 'vuex';

export default {
    name: 'AdvanceTooltip',
    mixins: [advancedFeatureMixin],
    props: {
        // 对应功能的featureId
        featureId: {
            default: '',
            type: String,
        },
        // 试用tooltip是否显示,
        showToolTip: {
            default: false,
            type: Boolean,
        },
        // tooltip的class
        popperClass: {
            default: 'dropdown-tool-tip',
            type: String,
        },
        // tooltip的placement,默认'left'(top, right,left)
        placement: {
            default: 'left',
            type: String,
        },
        // 是否是按钮
        isBtnType: {
            default: false,
            type: Boolean,
        },
        // 按钮的标题
        btnText: {
            default: '',
            type: String,
        },
        // 按钮的class
        btnClass: {
            default: '',
            type: String,
        },
        // 按钮的是否是plain
        btnIsPlain: {
            default: false,
            type: Boolean,
        },
        // 是否是展示new
        showNew: {
            default: true,
            type: Boolean,
        },
        // 是否是展示new
        showAddIcon: {
            default: false,
            type: Boolean,
        },
        // 是否是展示new背景色
        showNewBg: {
            default: false,
            type: Boolean,
        },
        // 展示new是否有其它条件
        moreConditionShowNew: {
            default: true,
            type: Boolean,
        },
    },
    computed: {
        ...mapGetters([
            'checkAdvancedFeatureData',
        ]),
    },
    methods: {
        // 点击处理
        handleBtnClick() {
            if (!this.checkFeatureCanUseInfo(this.featureId)) {
                // 高级功能未开启，提示
                return;
            }
            this.$emit('handleFeatureCanUse');
        },
    },
};
</script>

