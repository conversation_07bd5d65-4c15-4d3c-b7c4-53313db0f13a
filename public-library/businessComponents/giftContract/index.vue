<!-- 业务组件：合同奖励 -->
<!-- 引用位置  登录注册模块-注册成功页：register; 企业实名/个人实名 fe-auth-->
<template>
    <div :class="`${isPC ? '' : 'is-phone'} gift-contract ${giftType === 'register' ? '' : 'not-register'}`">
        <div class="gift-contract_img" :class="`${isPC ? 'gift-contract-pc_img' : 'gift-contract-mobile_img'}`">
            <img :src="giftImgUrl" alt="">
            <span v-if="giftsNum && giftsNum > 0">+{{ giftsNum }}</span>
        </div>
        <p class="gift-contract_success-text">
            {{ successText[giftType] }}
        </p>
        <p class="gift-contract-sum__line" v-if="giftsNumText && !getIsForeignVersion">
            <span class="gift-contract_gifts-num" v-html="giftsNumText"></span>
            <span class="gift-contract_end-time">&nbsp;<!-- 使用期限至 -->{{ '（' + $t('giftContract.expiryDateUtil') }}{{ endTime + '）' }} </span>
            <img src="~pub-images/giftContract/gift-give-icon.png" alt="">
        </p>
        <div v-if="wwwRecharge" class="gift-contract_gifts-num www-recharge__tip"><!-- 购买电子合同套餐的用户，建议完成实名认证后购买，默认购买是个人账户 -->{{ $t('giftContract.buyTip') }}</div>

        <p class="gift-contract_tip">
            {{ giftType === 'register' ? $t('giftContract.registerTip1') : (['entAuth','personAuth'].includes(giftType) ? $t('giftContract.sendContractTip') : '') }}
        </p>
        <div class="gift-contract_icons" :class="`${isPC ? 'gift-contract_icons-pc' : 'gift-contract_icons-mobile'}`">
            <div class="gift-contract_icons-ent" v-show="isCompany" @click="goToCertification('ent')">{{ $t('giftContract.entAuth') }}</div>
            <div class="gift-contract_icons-person" v-show="isPerson" alt="" @click="goToCertification('person')">{{ $t('giftContract.personAuth') }}</div>
        </div>
    </div>
</template>

<script>
import dayjs from 'dayjs';
import { mapState, mapGetters } from 'vuex';
import { isPC } from 'pub-utils/device.js';
import i18n from 'src/lang';

export default {
    props: {
        giftType: {
            type: String,
            default: 'register',
        },
        wwwRecharge: {
            type: Boolean,
            default: false,
        },
        isCompany: {
            type: Boolean,
            default: false,
        },
        isPerson: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isPC: isPC(),
            successText: {
                personAuth: i18n.t('giftContract.personAuthTip'), // 恭喜您成功完成个人实名认证
                entAuth: i18n.t('giftContract.entAuthTip'), // 恭喜您成功完成企业实名认证
                register: i18n.t('giftContract.openSsqTip'), // 恭喜您成功开通上上签服务！
            },
            giftRules: {},
            giftsNumTextMap: {
                register: ['REG_GIVE_2C_2USER', 'REG_GIVE_2B_2USER'],
                personAuth: ['AUTH_PASS_2C_2USER', 'AUTH_PASS_2B_2USER'],
                entAuth: ['AUTH_PASS_2C_2ENT', 'AUTH_PASS_2B_2ENT'],
            },
            toCnum: 0,
            toBnum: 0,
            validMonths: 0,
        };
    },
    computed: {
        ...mapState({
            registerTime: state => state.commonHeaderInfo.platformUser.registerTime,
            authPassTime: state => state.authPassTime,
        }),
        ...mapGetters([
            'getIsForeignVersion',
        ]),
        giftsNumText() {
            if (+this.giftsNum === 0) {
                return '';
            }
            return `<span>${this.$t('giftContract.giftTip1')}${this.toCnum > 0 ? `<i>${this.toCnum}</i>${this.$t('giftContract.giftTip2')}` : ''}${this.toBnum > 0 && this.toCnum > 0 ? this.$t('giftContract.and') : ''}${this.toBnum > 0 ? `<i>${this.toBnum}</i>${this.$t('giftContract.giftTip3')}` : ''}</span>`;
        },
        giftsNum() {
            return (this.toCnum + this.toBnum).toFixed(0);
        },
        endTime() {
            const isRegister = this.giftType === 'register';
            const startTime = isRegister ? this.registerTime : this.authPassTime;
            return dayjs(startTime).add(this.validMonths, 'M').format('YYYY-MM-DD');
        },
        giftImgUrl() {
            return ['entAuth', 'personAuth'].includes(this.giftType) ?  require('pub-images/giftContract/auth-gift-flower.gif') : require('pub-images/giftContract/register-gift-flower.gif');
        },
    },
    methods: {
        getGiftRules() {
            this.$http.get('/users/configs/give-rules').then(res => {
                (res.data || res || []).forEach(el => {
                    this.giftRules[el.name] = el.value;
                });
                this.toCnum = +this.giftRules[this.giftsNumTextMap[this.giftType][0]];
                this.toBnum = +this.giftRules[this.giftsNumTextMap[this.giftType][1]];
                this.validMonths = this.giftRules[ this.giftType === 'register' ? 'REGISTER_GIVE_MONTH' : 'AUTH_PASS_GIVE_MONTH' ];
            });
        },
        goToCertification(type) {
            this.$emit('goToCertification', type);
        },
    },
    created() {
        this.getGiftRules();
    },
};
</script>

<style lang="scss">
    .gift-contract{
        width: 555px;
        margin: 0 auto 30px;
        text-align: center;
        &_img{
            margin: 0 auto;
            position: relative;
            span{
                position: absolute;
                color: $--color-white;
                width: 27px;
                line-height: 27px;
                font-size: 12px;
                display: inline-block;
                width: 18px;
                height: 18px;
                line-height: 18px;
                text-align: center;
                border-radius: 9px;
                background-color: #FF5500;
            }
        }
        &-pc_img {
            width: 400px;
            img {
                width: 100%;
            }
            span {
                right: 155px;
                bottom: 17px;
            }
        }
        &-mobile_img {
            img {
                width: 80%;
            }
            span {
                right: 120px;
                bottom: 7px;
            }
        }
        &_success-text{
            font-size: 18px;
            color: $--color-danger;
            margin: 12px 0 6px;
        }
        &-sum__line{
            background-color: #E8FFEA;
            border-radius: 20px;
            font-size: 12px;
            width: 320px;
            margin: 20px auto;
            position: relative;
            img {
                width: 26px;
                height: 26px;
                position: absolute;
                top: -10px;
                right: -10px;
            }
            span {
                display: inline-block;
                line-height: 26px;
                height: 26px;
                color: #00B42A;
                font-size: 12px;
            }
        }
        &_tip {
            color: #3D3D3D;
            font-size: 14px;
            margin: 15px 0 25px;
        }
        &_icons {
            &-ent, &-person {
                width: 240px;
                height: 50px;
                line-height: 50px;
                text-align: center;
                color: #fff;
                font-size: 14px;
                cursor: pointer;
                background-image: url(~pub-images/giftContract/icon-bg.gif);
                background-size: 100% 100%;
            }
        }
        &_icons-pc {
            display: flex;
            justify-content: center;
        }
        &_icons-mobile {
            display: flex;
            flex-direction: column;
            align-items: center;
        }
        .www-recharge__tip{
            line-height: 30px;
        }
        &_gifts-num{
            color: $--color-black;
            font-size: 14px;
            i{
                color: $--color-danger;
                font-style: normal;
            }
        }
        &_end-time{
            color: $--color-text-secondary;
            font-size: 14px;
        }
        &.is-phone{
            width: 100%;
            padding: 0 8%;
            box-sizing: border-box;
            .gift-contract_gifts-num{
                font-size: 15px;
            }
            .gift-contract_end-time{
                font-size: 12px;
            }
        }
        &.not-register{
            margin-top: 32px;
        }
    }
</style>
