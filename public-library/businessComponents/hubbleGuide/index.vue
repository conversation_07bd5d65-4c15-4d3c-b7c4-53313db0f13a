<template>
    <el-dialog
        class="hubble-component__guide"
        :visible.sync="value"
        :before-close="handleClose"
    >
        <div class="hubble-component__guide-header">
            <span>让Hubble帮你读文档</span>
            <i class="el-icon-ssq-delete" @click="handleClose"></i>
        </div>
        <ul class="hubble-component__guide-banner">
            <li class="hubble-component__guide-banner-item" v-for="(banner, i) in bannerList" :key="i">
                <img :src="bannerImg(i)" alt="">
                <span>{{ banner }}</span>
            </li>
        </ul>
        <el-button class="submit-btn" type="primary" @click="handleUse">立即试用</el-button>
    </el-dialog>
</template>

<script>
export default {
    props: {
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            bannerList: ['段落翻译', '内容查找', '归纳总结', '风险审查'],
        };
    },
    methods: {
        bannerImg(i) {
            return require(`pub-images/hubble/banner${i + 1}.png`);
        },
        handleClose() {
            this.$emit('input', false);
            this.$http.post('/users/configs/HUBBLE_GUIDE', {
                value: '1',
                name: 'HUBBLE_GUIDE',
            });
        },
        handleUse() {
            this.handleClose();
            window.open(`${location.origin}/hubble/upload`);
        },
    },
};
</script>

<style lang="scss">
.hubble-component__guide{
    .el-dialog{
        width: 1000px;
        &__header{
            display: none;
        }
        &__body{
            padding: 0 0 30px !important;
        }
    }
    &-header{
        font-size: 16px;
        text-align: center;
        padding: 35px 35px 0;
        position: relative;
        font-weight: bold;
        i{
            position: absolute;
            right: 35px;
            cursor: pointer;
        }
    }
    &-banner{
        display: flex;
        justify-content: space-between;
        padding: 25px 30px;
        &-item{
            width: 200px;
            text-align: center;
            img{
                width: 200px;
            }
        }
    }
    .submit-btn{
        display: block;
        margin: 0 auto;
    }
}
</style>
