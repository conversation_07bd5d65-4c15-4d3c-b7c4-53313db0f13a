<template>
    <el-dialog
        class="sso-tip-dialog"
        :title="$t('ssoLoginConfig.tip')"
        :visible.sync="params.visible"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        append-to-body
        :show-close="false"
        width="30%"
    >
        <div class="sso-tip-content">
            <span>{{ $t('ssoLoginConfig.notBelongToEntTip') }}</span>
        </div>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="toContinue">{{ $t('ssoLoginConfig.continue') }}</el-button>
            <el-button @click="toCancel">{{ $t('ssoLoginConfig.cancel') }}</el-button>
            <div class="footer-tip">
                <div>{{ $t('ssoLoginConfig.operationStep.one') }}</div>
                <div>{{ $t('ssoLoginConfig.operationStep.two') }}</div>
                <div>{{ $t('ssoLoginConfig.operationStep.three') }}</div>
            </div>
        </span>
    </el-dialog>
</template>

<script>
export default {
    name: 'SSONotBelongToEntDialog',
    props: {
        params: {
            type: Object,
            default: () => ({
                visible: false,
                developerName: '',
            }),
        },
    },
    methods: {
        toContinue() {
            this.$emit('closeDialog');
            // 清除登录信息
            this.$cookie.delete('access_token');
            this.$cookie.delete('refresh_token');
            // 前往登录页
            this.$router.replace('/account-center/login');
        },
        toCancel() {
            this.$emit('closeDialog');
            if (this.$route.path.includes('/account-center/home')) {
                return;
            }
            this.$router.replace('/account-center/home');
        },
    },
};
</script>

<style lang="scss">
.sso-tip-dialog{
    .sso-tip-content{
        color: #000;
    }
    .footer-tip{
        color: #666666;
        font-size: 14px;
        margin-top: 20px;
        div{
            text-align: left;
            padding-left:85px;
        }
    }
    .el-dialog__footer{
        text-align: center;
    }
}
</style>
