<!--
    批量增加下拉框备选项
-->
<template>
    <el-dialog
        :title="$t('batchAddOptionsDialog.batchAddOption')"
        :visible.sync="visible"
        width="388px"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
        :modal-append-to-body="false"
        :append-to-body="appendToBody"
        class="batch-add-combobox-wrapper"
    >
        <el-form :model="formData">
            <i class="el-icon-warning"></i><span class="batch-add-combobox-tip1">{{ this.$t('batchAddOptionsDialog.batchAddTip1') }}</span>
            <div class="batch-add-combobox-tip2">{{ this.$t('batchAddOptionsDialog.batchAddTip2') }}</div>
            <el-form-item>
                <el-input
                    type="textarea"
                    :rows="3"
                    :placeholder="$t('batchAddOptionsDialog.batchAddPlaceHolder')"
                    v-model="formData.addItems"
                >
                </el-input>
            </el-form-item>
        </el-form>
        <div slot="footer" class="dialog-footer font-size-zero">
            <el-button @click="handleClose" size="small">{{ $t('batchAddOptionsDialog.cancel') }}</el-button>
            <el-button @click="handleConfirm" type="primary" size="small">{{ $t('batchAddOptionsDialog.confirm') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        visible: {
            default: true,
            type: Boolean,
        },
        appendToBody: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            formData: { addItems: '' },
        };
    },
    watch: {
        visible(v) {
            if (v) {
                this.formData.addItems = '';
            }
        },
    },
    methods: {
        // 关闭弹窗
        handleClose() {
            this.$emit('close');
        },
        getInputParam() {
            const filterStrs = this.formData.addItems.split('\n');
            const result = filterStrs.map(str => {
                return str.trim();
            }).filter(a => a !== '');
            return {
                textValues: [...new Set(result)],
            };
        },
        handleConfirm() {
            if (!this.formData.addItems) {
                return this.$MessageToast.info(this.$t('batchAddOptionsDialog.searchContentPlaceholder'));
            }
            const params = this.getInputParam();
            if (params.textValues.length > 500) {
                this.$confirm(this.$t('batchAddOptionsDialog.searchContentLengthLimitTip'));
                return;
            }
            this.$emit('addComboBoxItems', params);
        },
    },
};
</script>

<style lang="scss">
.batch-add-combobox-wrapper {
    .el-icon-warning {
        color: #F2A93E;
        margin-right: 5px;
    }
    .batch-add-combobox-tip2 {
        margin-left: 19px;
        color: #999;
        font-size: 12px;
    }
    .el-form-item {
        margin-top: 10px;
        padding: 10px 20px 0;
    }
    .el-dialog__body {
        padding: 10px 20px 0;
    }
}
</style>
