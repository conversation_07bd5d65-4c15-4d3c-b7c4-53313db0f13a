<template>
    <div class="block-preview-page" :class="isRealDataMode ? 'real-mode' : 'preview-mode'">
        <div class="block-preview-page__upper-half">
            <div class="block-preview-page__upper-half__inner">
                <header class="preview-page__header">
                    <h2>{{ layout.title }}</h2>
                    <p>{{ layout.copyWriting }}</p>
                    <div class="preview-company-logos">
                        <div class="preview-company-logos__left">
                            <img :src="layout.logoImageHref" class="preview-company-logo other" />
                        </div>
                        <span class="sep"></span>
                        <img :src="ssqLogo" class="preview-company-logo ssq" />
                    </div>
                    <img :src="validIcon" class="review-page__header__approved" />
                </header>
                <main class="preview-page__body">
                    <div class="preview-page__body__section preview-page__body__basic-info">
                        <h3 class="section-title">{{ bodyContent.blockBasicInfo.term1 }}状态：<span class="content__item__value">{{ valueInfos.term1Val }}</span></h3>
                    </div>
                    <div class="preview-page__body__section preview-page__body__basic-info">
                        <h3 class="section-title">基本信息</h3>
                        <ul class="preview-page__body__section__content">
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term2 }}名称：</label><span class="content__item__value">{{ valueInfos.term2Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term3 }}编号：</label><span class="content__item__value">{{ valueInfos.term3Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term4 }}：</label><span class="content__item__value">{{ valueInfos.term4Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term5 }}发送时间：</label><span class="content__item__value">{{ valueInfos.term5Val }}</span>
                            </li>
                            <li
                                class="preview-page__body__section__content__item"
                            >
                                <label class="content__item__label">截止签约时间：</label><span class="content__item__value">{{ valueInfos.termTimeVal }}</span>
                            </li>
                            <li v-if="layout.showTerm10" class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term10 }}：</label><span class="content__item__value">{{ valueInfos.term10Val }}</span>
                            </li>
                        </ul>
                    </div>
                    <div class="preview-page__body__section preview-page__body__auhtorized-to">
                        <h3 class="section-title">{{ bodyContent.authorizedBy.term6 }}</h3>
                        <ul class="preview-page__body__section__content">
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.authorizedBy.term7 }}主体：</label><span class="content__item__value">{{ valueInfos.term7Val }}</span>
                            </li>

                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.authorizedBy.term8 }}状态：</label><span class="content__item__value">{{ valueInfos.term8Val }}</span>
                            </li>
                        </ul>
                    </div>
                </main>
            </div>
        </div>
        <footer class="preview-page__footer">
            <el-button class="preview-page__footer__btn" type="primary">{{ layout.buttonCopyWriting }}</el-button>
        </footer>
    </div>
</template>
<script>
export default {
    name: 'QRPreviewWechatProgram',
    props: {
        isRealDataMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            defaultImg: require('pub-images/default-preview.png'),
            ssqLogo: this.GLOBAL.LOGO,
            validIcon: require('pub-images/verified-and-valid.png'),
            bodyContent: {
                // 基础信息
                blockBasicInfo: {},
                // 授权方
                authorizedBy: {},
            },
            // 页头页脚
            layout: {},
            valueInfos: {},
        };
    },
    methods: {
        rawDataToPreviewData(style, info) {
            // default
            const { copyWriting, buttonCopyWriting, definitionTerms, title, logoImageHref, tickItems } = style;
            this.bodyContent.blockBasicInfo = {
                ...this.bodyContent.blockBasicInfo,
                term1: definitionTerms.term1,
                term2: definitionTerms.term2,
                term3: definitionTerms.term3,
                term4: definitionTerms.term4,
                term5: definitionTerms.term5,
                term10: definitionTerms.term10,
            };
            this.bodyContent.authorizedBy = {
                ...this.bodyContent.authorizedBy,
                term6: definitionTerms.term6,
                term7: definitionTerms.term7,
                term8: definitionTerms.term8,
            };
            this.layout = {
                ...this.layout,
                title,
                copyWriting,
                logoImageHref: logoImageHref ||  require('pub-images/bestsign-en.png'),
                buttonCopyWriting,
                ...tickItems,
            };
            this.valueInfos = info;
        },

        getDefaultPreviewMode(content, layout) {
            content.blockBasicInfo = {
                term1: '单据',
                term2: '单据',
                term3: '单据',
                term4: '发货方',
                term5: '单据',
                term10: '公司内部编号',
            };
            content.authorizedBy = {
                term6: '签收方',
                term7: '签收',
                term8: '签收',
            };
            layout = {
                title: '物流单签收情况公示书',
                copyWriting: '上上签为您的合同保障签署安全真实有效',
                logoImageHref: require('pub-images/bestsign-en.png'),
                buttonCopyWriting: '去签收',
                showTerm10: false,
            };
            return {
                content,
                layout,
            };
        },
        getDefaultRealMode(content, layout) {
            return this.getDefaultPreviewMode(content, layout);
        },
        resetDefault() {
            const { bodyContent: content, layout } = this;
            const reset = this.isRealDataMode ? this.getDefaultRealMode(content, layout) : this.getDefaultPreviewMode(content, layout);
            this.bodyContent = { ...content, ...reset.content };
            this.layout = { ...layout, ...reset.layout };
        },
        fetchStyle(templateId) {
            const defaultSetting = {
                title: '物流单签收情况公示书',
                copyWriting: '上上签为您的合同保障签署安全真实有效',
                logoImageHref: require('pub-images/bestsign-en.png'),
                buttonCopyWriting: '去签收',
                tickItems: {
                    showTerm10: false,
                },
                definitionTerms: {
                    term1: '授权书',
                    term2: '合同',
                    term3: '合同',
                    term4: '合同',
                    term5: '签约方',
                    term6: '签约主体',
                    term10: '公司内部编号',
                },
            };
            return this.$http.get(`/template-api/v2/verify-code/template-style/${templateId}`).catch(() => {
                return {
                    data: defaultSetting,
                };
            });
        },
        // 取数据
        fetchData() {
            const infoVals = {
                term1Val: 'XXXX',
                term2Val: 'XXXX',
                term3Val: 'XXXX',
                term4Val: 'XXXX',
                term5Val: 'XXXX-XX-XX XX:XX',
                termTimeVal: 'XXXX-XX-XX',
                term7Val: 'XXXX',
                term8Val: 'XXXX',
                term10Val: 'XXXXXXXXXXXXXX',
            };
            // TODO 如果是真实数据，要通过ajax换成实际数据
            return {
                data: infoVals,
            };
        },
        fetchInfo(templateId) {
            if (!templateId) {
                return;
            }
            this.resetDefault();
            return Promise.all([this.fetchStyle(templateId), this.fetchData()]).then((_resList) => {
                const [styleRes, infoRes] = _resList;
                const styleData = styleRes.data;
                const infoData = infoRes.data;
                this.rawDataToPreviewData(styleData, infoData);
            });
        },
    },
};
</script>
