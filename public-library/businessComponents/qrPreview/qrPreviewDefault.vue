<template>
    <div class="block-default-preview-page">
        <header class="preview-page__header">
            <div class="preview-page__logoWrap">
                <img :src="GLOBAL.LOGO" class="bestsign-logo" />
            </div>
            <div class="preview-page__header__titleWrap">
                <h2 class="header__titleWrap__major">合同签署信息公示书</h2>
                <p class="header__titleWrap__sub">上上签为您的合同保障签署安全真实有效</p>
                <i class="iconfont el-icon-ssq-shimingrenzheng2" />
            </div>
        </header>
        <main class="preview-page__main">
            <div class="preview-page__main__statusWrap">
                <p>
                    <label>合同状态:</label>
                    <span>签署中</span>
                </p>
            </div>
            <div class="preview-page__main__basicWrap">
                <h3>基本信息</h3>
                <p class="preview-page__main__basicWrap__item" v-for="(item,index) in defaultBaseInfo" :key="index">
                    <label>{{ item.title }}：</label>
                    <span>{{ item.value }}</span>
                </p>
                <template v-for="item in baseInfo">
                    <p class="preview-page__main__basicWrap__item" :key="item.title" v-if="!item.hide">
                        <label>{{ item.title }}：</label>
                        <span>{{ item.value }}</span>
                    </p>
                </template>
            </div>
            <div class="preview-page__main__receiverWrap">
                <h3>合同收件方</h3>
                <div>
                    <p class="preview-page__main__receiverWrap__item">
                        <label>签约主体：</label>
                        <span>XXXX</span>
                    </p>
                    <p class="preview-page__main__receiverWrap__item">
                        <label>签约状态：</label>
                        <span>XXXX</span>
                    </p>
                </div>
            </div>
        </main>
    </div>
</template>
<script>
export default {
    name: 'QRPreview',
    props: {
        isRealDataMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            defaultBaseInfo: [
                { title: '合同名称', value: 'XXXX' },
                { title: '合同编号', value: 'XXXX' },
                { title: '发件方', value: 'XXXX' },
                { title: '合同发送时间', value: 'XXXX-XX-XX XX:XX' },
                { title: '签约截止时间', value: 'XXXX-XX-XX XX:XX' },
            ],
            baseInfo: {
                term10: {
                    value: 'XXXXXXXXXXXX',
                    title: '公司内部编号',
                    hide: false,
                },
            },
        };
    },
    methods: {
        // eslint-disable-next-line no-unused-vars
        fetchInfo(templateId) {
            this.fetchStyle(templateId).then(({ data }) => {
                data.definitionTerms && Object.keys(data.definitionTerms).forEach(item => {
                    if (data.definitionTerms[item] && this.baseInfo[item]) {
                        this.baseInfo[item].title = data.definitionTerms[item];
                    }
                });
                this.baseInfo.term10.hide = !(data.tickItems && data.tickItems.showTerm10);
            });
        },
        fetchStyle(templateId) {
            const defaultSetting = {
                definitionTerms: {
                    term10: '公司内部编号',
                },
            };
            return this.$http.get(`/template-api/v2/verify-code/template-style/${templateId}`).catch(() => {
                return {
                    data: defaultSetting,
                };
            });
        },
    },
};
</script>
<style lang="scss">
.block-default-preview-page {
    margin-bottom: 20px;
    padding: 20px;
    width: 345px;
    min-height: 600px;
    background-image: url(~pub-images/qrPreview/qr-background.png);
    background-size: 100% 100%;
    box-sizing: border-box;
    background-repeat: no-repeat;
    .preview-page__logoWrap {
        display: flex;
        flex-flow: row nowrap;
        justify-content: flex-start;
        align-items: center;
        .bestsign-logo {
            height: 22px;
            width: auto;
        }
    }
    .preview-page__header__titleWrap {
        text-align: center;
        margin-top: 28px;
        .header__titleWrap__major {
            font-family: PingFangSC-Medium,sans-serif;
            font-size: 24px;
            color: #374249;
            line-height: 27px;
            font-weight: 500;
        }
        .header__titleWrap__sub {
            font-family: PingFangSC-Regular,sans-serif;;
            font-size: 10px;
            color: #8c959c;
            line-height: 15px;
            font-weight: 400;
            margin-top: 9px;
        }
        .header__titleWrap__sub::before {
            content: '·';
            font-size: 16px;
        }
        .header__titleWrap__sub::after {
            content: '·';
            font-size: 16px;
        }
        .el-icon-ssq-shimingrenzheng2 {
            color: $--color-primary;
            margin-top: 17px;
            font-size: 17px;
        }
    }
    .preview-page__main {
        .preview-page__main__statusWrap {
            border-bottom: 1px solid #dbedfb;
            padding-bottom: 15px;
            margin-bottom: 20px;
            font-family: PingFangSC-Medium,sans-serif;;
            font-size: 14px;
            color: #333333;
            font-weight: 500;
            label {
                margin-right: 10px;
            }
        }
        .preview-page__main__basicWrap {
            margin-bottom: 20px;
            padding-bottom: 20px;
            border-bottom: 1px solid #dbedfb;
            h3 {
                font-family: PingFangSC-Medium,sans-serif;;
                font-size: 14px;
                color: #333333;
                font-weight: 500;
                margin-bottom: 15px;
            }
            .preview-page__main__basicWrap__item {
                min-height: 25px;
                font-size: 12px;
                line-height: 25px;
                word-break: break-all;
                label {
                    color: #999;
                    margin-right: 5px;
                }
                span {
                    color: #333;
                }
            }
        }
        .preview-page__main__receiverWrap {
            h3 {
                font-family: PingFangSC-Medium,sans-serif;;
                font-size: 14px;
                color: #333333;
                font-weight: 500;
                margin-bottom: 15px;
            }
            .preview-page__main__receiverWrap__item {
                height: 25px;
                font-size: 12px;
                line-height: 25px;
                label {
                    color: #999;
                    margin-right: 5px;
                }
                span {
                    color: #333;
                }
            }
        }
    }
}
</style>
