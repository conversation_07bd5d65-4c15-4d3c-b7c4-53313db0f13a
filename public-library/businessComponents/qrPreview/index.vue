<template>
    <el-dialog
        :visible.sync="isDialogVisible"
        custom-class="verify-qr-block-preview"
        id="verify-qr-block-preview"
        width="320px"
        :before-close="handleClose"
        :close-on-press-escape="true"
        :close-on-click-modal="true"
        :append-to-body="true"
    >
        <div class="verify-qr-block-preview__inner" :class="blockType">
            <DefaultPreview ref="defaultQRPreview" v-if="blockType === 'default'" :isRealDataMode="isRealDataMode" />
            <WechatProgramPreview ref="wechatProgramPreview" v-else-if="blockType === 'customized'" :isRealDataMode="isRealDataMode" />
            <LogisticsProgramPreview ref="logisticsProgramPreview" v-else-if="blockType === 'logistics'" :isRealDataMode="isRealDataMode" />
            <div v-else></div>
        </div>
    </el-dialog>
</template>
<script>
import DefaultPreview from './qrPreviewDefault.vue';
import WechatProgramPreview from './qrPreviewWechatProgram.vue';
import LogisticsProgramPreview from './qrPreviewLogisticsProgram.vue';
export default {
    name: 'QRPreview',
    components: {
        DefaultPreview,
        WechatProgramPreview,
        LogisticsProgramPreview, // 物流
    },
    props: {
        block: {
            type: Object,
            default: () => {},
        },
        isPreviewVisible: {
            type: Boolean,
            default: false,
        },
        // 取真实数据还是样本
        isRealDataMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            blockType: 'default',
        };
    },
    computed: {
        isDialogVisible() {
            return this.isPreviewVisible;
        },
    },
    watch: {
        block: {
            deep: true,
            immediate: true,
            handler(v) {
                if (!v) {
                    return;
                }
                const blockTyp = this.evaluateBlockType(v);
                this.blockType = blockTyp;
                this.fetchPreviewInfo(blockTyp, v.verifyCodeTemplateId);
            },
        },
    },
    methods: {
        evaluateBlockType(block) {
            let type = '';
            switch (block.checkType) {
                case 0:
                    type = 'default';
                    break;
                case 1:
                    type = 'customized';
                    break;
                case 2:
                    type = 'logistics';
                    break;
            }
            return type;
        },
        handleClose() {
            this.$emit('close-block-preview');
        },
        fetchPreviewInfo(blockType, verifyCodeTemplateId) {
            this.$nextTick(() => {
                if (blockType === 'default') {
                    // 留一个口子，默认样式后面在加上之后，我们通过refs去分布处理
                    this.$refs['defaultQRPreview'].fetchInfo(verifyCodeTemplateId);
                    return;
                }
                if (blockType === 'customized') {
                    this.$refs['wechatProgramPreview'].fetchInfo(verifyCodeTemplateId);
                } else if (blockType === 'logistics') {
                    this.$refs['logisticsProgramPreview'].fetchInfo(verifyCodeTemplateId);
                }
            });
        },
    },
};
</script>
<style lang="scss">
// cert-background
.block-preview-page {
    .preview-page__header {
        padding-top: 43px;
        background-image: url(~pub-images/cert-background.png);
        background-size: cover;
        position: relative;
        h2,
        p {
            text-align: center;
        }
        h2 {
            font-family: PingFangSC-Medium;
            font-size: 24px;
            color: #374249;
            line-height: 27px;
            font-weight: 500;
            margin-bottom: 9px;
        }
        p {
            font-family: PingFangSC-Regular;
            font-size: 10px;
            color: #8c959c;
            line-height: 15px;
            font-weight: 400;
            margin-bottom: 24px;
        }
        .preview-company-logos {
            width: 100%;
            display: flex;
            flex-flow: row nowrap;
            align-items: center;
            justify-content: flex-start;
            .sep {
                display: block;
                height: 18px;
                width: 0;
                border-left: 1px solid $--border-color-light;
                margin-right: 21px;
                margin-left: 12px;
            }
            .preview-company-logos__left {
                width: 50%;
                padding-left: 11px;
                display: flex;
                flex-flow: row nowrap;
                align-items: center;
                justify-content: flex-end;
            }
            .preview-company-logo.other,
            .preview-company-logo.ssq {
                width: 69px;
                height: auto;
            }
        }
    }
    .review-page__header__approved {
        height: 70px;
        width: 70px;
        position: absolute;
        top: 50px;
        right: 20%;
    }
}
.block-preview-image {
    width: 100%;
    display: block;
}
#verify-qr-block-preview .verify-qr-block-preview {
    background-color: transparent;
    .block-preview-page__upper-half {
        background-color: $--color-white;
        border: 2px solid $--border-color-light;
        box-sizing: border-box;
    }
    .el-dialog__header {
        display: none;
    }
    .el-dialog__body {
        padding: 0px;
    }
}
.verify-qr-block-preview__inner {
    padding-bottom: 0px;
}
// .verify-qr-block-preview__inner.customized {
//     width: 320px;
// }
.verify-qr-block-preview__inner.customized .block-preview-page__upper-half {
    padding: 5px;
    .block-preview-page__upper-half__inner {
        border: 2px solid #ccc;
    }
}
.preview-page__footer {
    margin-top: 12px;
    display: flex;
    .preview-page__footer__btn {
        flex: 1;
        height: 50px;
    }
}
.preview-page__body {
    padding: 0px 25px;
    box-sizing: border-box;
}
.preview-page__body__section {
    padding: 15px 0px;
    .section-title {
        font-family: PingFangSC-Medium;
        font-size: 14px;
        color: #374249;
        font-weight: 500;
        margin-bottom: 10px;
    }
    .preview-page__body__section__content__item {
        display: flex;
        flex-flow: row nowrap;
        align-items: center;
        justify-content: flex-start;
    }
    .content__item__label {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #8c959c;
        line-height: 25px;
        font-weight: 400;
    }
    .content__item__value {
        font-family: PingFangSC-Regular;
        font-size: 12px;
        color: #333333;
        line-height: 25px;
        font-weight: 400;
    }
}
.preview-page__body__auhtorized-to {
    border-top: 1px solid $--border-color-light;
    border-bottom: 1px solid $--border-color-light;
}
</style>
