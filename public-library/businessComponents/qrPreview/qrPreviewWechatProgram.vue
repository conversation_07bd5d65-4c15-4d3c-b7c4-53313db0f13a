<template>
    <div class="block-preview-page" :class="isRealDataMode ? 'real-mode' : 'preview-mode'">
        <div class="block-preview-page__upper-half">
            <div class="block-preview-page__upper-half__inner">
                <header class="preview-page__header">
                    <h2>{{ layout.title }}</h2>
                    <p>{{ layout.copyWriting }}</p>
                    <div class="preview-company-logos">
                        <div class="preview-company-logos__left">
                            <img :src="layout.logoImageHref" class="preview-company-logo other" />
                        </div>
                        <span class="sep"></span>
                        <img :src="ssqLogo" class="preview-company-logo ssq" />
                    </div>
                    <img :src="validIcon" class="review-page__header__approved" />
                </header>
                <main class="preview-page__body">
                    <div class="preview-page__body__section preview-page__body__basic-info">
                        <h3 class="section-title">{{ bodyContent.blockBasicInfo.term1 }}基本信息</h3>
                        <ul class="preview-page__body__section__content">
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term2 }}名称：</label><span class="content__item__value">{{ valueInfos.term2Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term3 }}系统编号：</label><span class="content__item__value">{{ valueInfos.term3Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term4 }}状态：</label><span class="content__item__value">{{ valueInfos.term4Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">发件方：</label><span class="content__item__value">{{ valueInfos.senderVal }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">发送时间：</label><span class="content__item__value">{{ valueInfos.sendTimeVal }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">签约完成时间：</label><span class="content__item__value">{{ valueInfos.signCompleteDateVal }}</span>
                            </li>
                            <li v-if="layout.showTerm10" class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.blockBasicInfo.term10 }}：</label><span class="content__item__value">{{ valueInfos.term10Val }}</span>
                            </li>
                        </ul>
                    </div>
                    <div class="preview-page__body__section preview-page__body__auhtorized-to">
                        <h3 class="section-title">{{ bodyContent.authorizedBy.term5 }}</h3>
                        <ul class="preview-page__body__section__content">
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.authorizedBy.term6 }}：</label><span class="content__item__value">{{ valueInfos.term6Val }}</span>
                            </li>

                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">签约状态：</label><span class="content__item__value">{{ valueInfos.signStateVal }}</span>
                            </li>

                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">签署时间：</label><span class="content__item__value">{{ valueInfos.signDateVal }}</span>
                            </li>
                        </ul>
                    </div>
                    <div class="preview-page__body__section preview-page__body__authorized-by">
                        <h3 class="section-title">{{ bodyContent.authorizedTo.term7 }}</h3>
                        <ul class="preview-page__body__section__content">
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.authorizedTo.term8 }}主体：</label><span class="content__item__value">{{ valueInfos.term8Val }}</span>
                            </li>
                            <li class="preview-page__body__section__content__item">
                                <label class="content__item__label">{{ bodyContent.authorizedTo.term9 }}时间：</label><span class="content__item__value">{{ valueInfos.term9Val }}</span>
                            </li>
                        </ul>
                    </div>
                </main>
            </div>
        </div>
        <footer class="preview-page__footer">
            <el-button class="preview-page__footer__btn" type="primary">{{ layout.buttonCopyWriting }}</el-button>
        </footer>
    </div>
</template>
<script>
export default {
    name: 'QRPreviewWechatProgram',
    props: {
        isRealDataMode: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            defaultImg: require('pub-images/default-preview.png'),
            ssqLogo: this.GLOBAL.LOGO,
            validIcon: require('pub-images/verified-and-valid.png'),
            bodyContent: {
                // 基础信息
                blockBasicInfo: {},
                // 授权方
                authorizedBy: {},
                // 被授权方
                authorizedTo: {},
            },
            // 页头页脚
            layout: {},
            valueInfos: {},
        };
    },
    methods: {
        rawDataToPreviewData(style, info) {
            // default
            const { copyWriting, buttonCopyWriting, definitionTerms, title, logoImageHref, tickItems } = style;
            this.bodyContent.blockBasicInfo = {
                ...this.bodyContent.blockBasicInfo,
                term1: definitionTerms.term1,
                term2: definitionTerms.term2,
                term3: definitionTerms.term3,
                term4: definitionTerms.term4,
                term10: definitionTerms.term10,
            };
            this.bodyContent.authorizedBy = {
                ...this.bodyContent.authorizedBy,
                term5: definitionTerms.term5,
                term6: definitionTerms.term6,
            };
            this.bodyContent.authorizedTo = {
                ...this.bodyContent.authorizedTo,
                term7: definitionTerms.term7,
                term8: definitionTerms.term8,
                term9: definitionTerms.term9,
            };
            this.layout = {
                ...this.layout,
                title,
                copyWriting,
                logoImageHref: logoImageHref ||  require('pub-images/bestsign-en.png'),
                buttonCopyWriting,
                ...tickItems,
            };
            this.valueInfos = info;
        },

        getDefaultPreviewMode(content, layout) {
            content.blockBasicInfo = {
                term1: '授权书',
                term2: '合同',
                term3: '合同',
                term4: '合同',
                term10: '公司内部编号',
            };
            content.authorizedBy = {
                term5: '签约方',
                term6: '签约主体',
            };
            content.authorizedTo = {
                term7: '抄送方',
                term8: '抄送',
                term9: '抄送',
            };
            layout = {
                title: '授权证书核验报告',
                copyWriting: 'XXX公司联合上上签担保此合同真实有效',
                logoImageHref: require('pub-images/bestsign-en.png'),
                buttonCopyWriting: '查看授权书原文',
                showTerm10: false,
            };
            return {
                content,
                layout,
            };
        },
        getDefaultRealMode(content, layout) {
            console.log(content, layout);
            // TODO change to new settings
            return this.getDefaultPreviewMode(content, layout);
        },
        resetDefault() {
            const { bodyContent: content, layout } = this;
            const reset = this.isRealDataMode ? this.getDefaultRealMode(content, layout) : this.getDefaultPreviewMode(content, layout);
            this.bodyContent = { ...content, ...reset.content };
            this.layout = { ...layout, ...reset.layout };
        },
        fetchStyle(templateId) {
            const defaultSetting = {
                buttonCopyWriting: '查看授权书原文',
                copyWriting: 'XXX公司联合上上签担保此合同真实有效',
                definitionTerms: {
                    term1: '授权书',
                    term2: '合同',
                    term3: '合同',
                    term4: '合同',
                    term5: '签约方',
                    term6: '签约主体',
                    term7: '抄送方',
                    term8: '抄送',
                    term9: '抄送',
                    term10: '公司内部编号',
                },
                logoImageFileId: 0,
                logoImageHref: require('pub-images/bestsign-en.png'),
                reporterStyleId: 0,
                title: '授权证书核验报告',
                tickItems: {
                    showTerm10: false,
                },
            };
            return this.$http.get(`/template-api/v2/verify-code/template-style/${templateId}`).catch(() => {
                return {
                    data: defaultSetting,
                };
            });
        },
        // 取数据
        fetchData() {
            const infoVals = {
                term2Val: 'XXXXXXXX合同示例',
                term3Val: '2697625449315434498',
                term4Val: '已签署',
                senderVal: 'XXXXX公司示例',
                sendTimeVal: '20XX年XX月XX日 10:04',
                signCompleteDateVal: '20XX年XX月XX日',
                term6Val: 'XXX公司示例',
                signStateVal: '已签署',
                signDateVal: '20XX年XX月XX日 10:31',
                term8Val: 'XXXXX公司示例',
                term9Val: '20XX年XX月XX日 10:31',
                term10Val: 'XXXXXXXXXXXXXX',
            };
            // TODO 如果是真实数据，要通过ajax换成实际数据
            return {
                data: infoVals,
            };
        },
        fetchInfo(templateId) {
            if (!templateId) {
                return;
            }
            this.resetDefault();
            return Promise.all([this.fetchStyle(templateId), this.fetchData()]).then((_resList) => {
                const [styleRes, infoRes] = _resList;
                const styleData = styleRes.data;
                const infoData = infoRes.data;
                this.rawDataToPreviewData(styleData, infoData);
            });
        },
    },
};
</script>
