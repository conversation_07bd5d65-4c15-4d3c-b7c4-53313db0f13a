<template>
    <!-- 请确认您的身份 -->
    <el-dialog
        :title="$t('authBeforeSignJa.plsConfirmIdentity')"
        :visible.sync="dialogVisible"
        width="460px"
        :before-close="handleClose"
        custom-class="identify-dialog el-dialog-bg"
    >
        <div class="dialog-content">
            <span class="label"><!--姓名-->{{ $t('recharge.entNomalInvoiceMap.name') }}：</span>
            <!--请输入姓名--><el-input v-model="userName" :placeholder="$t('recharge.entNomalInvoiceMap.plsInputName')" />
            <el-button type="primary" @click="handleConfirm"><!-- 提交 -->{{ $t('recharge.submit') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
export default {
    props: {
        dialogVisible: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            userName: '',
        };
    },
    methods: {
        handleClose() {
            this.$emit('update:dialogVisible', false);
        },
        handleConfirm() {
            // if (!this.userName || !this.userName.trim()) {
            //     return this.$MessageToast.error(this.$t('signTip.plsEnterName'));
            // }
            this.$http.put('/users/name', { newName: this.userName }).then(() => {
                this.$emit('handleConfirm');
            });
        },
    },
};
</script>

<style lang="scss">
.identify-dialog{
    .dialog-content{
        text-align: center;
        padding: 18px 0 38px 0;
        .el-input{
            display: inline-block;
            width: 300px;
        }
        .el-button{
            margin: 38px 0 0 0;
            width: 154px;
        }
    }
}
</style>
