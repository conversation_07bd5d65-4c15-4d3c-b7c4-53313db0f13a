<template>
    <div class="ssq-business-header">
        <template v-if="!logoConfig || logoConfig.visible">
            <img v-if="hasOwnLogo === false"
                class="default-logo"
                :src="GLOBAL.WHITE_LOGO"
                width="86"
                height="37"
                alt="logo"
                @click="goBackToHome"
            >
            <img v-else
                class="ultimateInfo"
                height="30"
                width="90"
                :src="imgSrc"
                @click="goBackToHome"
                alt="logo"
            >
        </template>
        <span class="line"></span>
        <span class="console-title">
            {{ title }}
        </span>
        <LangSwitch @changeLanguage="changeLanguage"></LangSwitch>
        <router-link to="/account-center/home" class="back-to-ssq">
            <span @click="back">{{ $t('commonHeader.backToHome') }}</span>
        </router-link>
        <div class="clear"></div>
    </div>
</template>

<script>
import { mapGetters } from 'vuex';
import LangSwitch from 'pub-businessComponents/langSwitch/index.vue';

export default {
    components: { LangSwitch },
    props: {
        title: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            hasOwnLogo: false,
            imgSrc: '',
        };
    },
    computed: {
        ...mapGetters([
            'getCurrentEntInfo',
            'getSsoConfig',
        ]),
        logoConfig() {
            const ssoConsole = this.getSsoConfig.console || {};
            return ssoConsole.console_1_logo;
        },
    },
    watch: {
        getCurrentEntInfo() {
            this.initLogo();
        },
    },
    methods: {
        // 初始化 logo
        initLogo() {
            if (this.getCurrentEntInfo?.logoFileId || this.$cookie.get('homeLogoFileId')) {
                this.setLogo();
            } else {
                this.hasOwnLogo = false;
            }
        },
        // 有 logo ，赋值路径
        setLogo() {
            this.hasOwnLogo = true;
            this.imgSrc = `/ents/logo?t=` + new Date().getTime();
        },
        back() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.title,
                    first_category: '顶部导航栏',
                    icon_name: '返回首页',
                },
            });
        },
        goBackToHome() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.title,
                    first_category: '顶部导航栏',
                    icon_name: '返回云平台首页图标',
                },
            });
            if (!this.logoConfig || this.logoConfig.clickAble) {
                this.$router.push('/account-center/home');
            }
        },
        changeLanguage(item) {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.title,
                    first_category: '顶部导航栏',
                    icon_name: item.text,
                },
            });
        },
    },
    mounted() {
        this.initLogo();
    },
};
</script>

<style lang="scss">
.ssq-business-header{
    box-sizing: border-box;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    padding: 0 18px;
    height: 63px;
    line-height: 63px;
    color: $--color-white;
    font-size: 16px;
    background: #002b45;
    z-index: 1;

    .console-title{
        margin-left: 28px;
        vertical-align: middle;
    }
    img{
        cursor: pointer;
        padding-right: 18px;
        vertical-align: middle;
    }
    .el-icon-ssq-fenzu3 {
        cursor: pointer;
        font-size: 37px;
        vertical-align: middle;
    }
    .default-logo{
        padding-right: 0;
    }
    span.line {
        display: inline-block;
        padding-left: 12px;
        height: 28px;
        border-right: 1px solid #5aa6e0;
        vertical-align: middle;
    }
    .back-to-ssq{
        float: right;
        padding-left: 5px;
        padding-right: 15px;
        height: 30px;
        line-height: 63px;
        text-align: center;
        vertical-align: middle;
        color: $--color-white;
        border-radius: 2px;
        transition: all ease 0.3s;
        font-size: 14px;
    }
    .lang-switch {
        float: right;
        padding-right: 10px;
    }
    [dir=rtl] & {
        left: auto;
        right: 0;
        .console-title {
            margin-right: 28px;
            margin-left: 0;
        }
        img {
            padding-left: 18px;
            padding-right: 0;
        }
        span.line {
            border-right: none;
            border-left: 1px solid #5aa6e0;
            padding-right: 12px;
            padding-left: 0;
        }
        .back-to-ssq {
            float: left;
            padding-right: 5px;
            padding-left: 15px;
        }
        .lang-switch {
            float: left;
            padding-right: 0;
            padding-left: 10px;
        }
    }
}
</style>
