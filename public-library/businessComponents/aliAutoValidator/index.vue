<template>
    <div id="aliValidator"></div>
</template>

<script>
export default {
    name: 'AliAutoValidator',
    props: {
        width: {
            type: Number,
            default: 320, // 最小值320，低于320的不生效
        },
    },
    data() {
        return {
            captcha: null,
        };
    },
    methods: {
        getInstance(instance) {
            this.captcha = instance;
        },
        async captchaVerifyCallback(captchaVerifyParam) {
            this.$store.state.captchaVerifyParam = captchaVerifyParam;
            return {
                captchaResult: true,
                bizResult: true,
            };
        },
        // 验证通过后调用
        onBizResultCallback() {
            console.log('onBizResultCallback');
        },
    },
    mounted() {
        this.captchaButton = document.getElementById('aliValidator');
        const SceneId = this.GLOBAL.ENV_NAME === 'PRE_ENV' ? '16f8lngn' : '1hh9tq9w';
        const width = document.body.clientWidth - 30;
        let rem = 1;
        if (width <= 320) {
            rem = Math.floor(width / 320 * 100) / 100;
        }
        window.initAliyunCaptcha({
            SceneId, // 场景ID。根据步骤二新建验证场景后，您可以在验证码场景列表，获取该场景的场景ID
            prefix: '1wj8cz', // 身份标。开通阿里云验证码2.0后，您可以在控制台概览页面的实例基本信息卡片区域，获取身份标
            mode: 'embed', // 验证码模式。popup表示要集成的验证码模式为弹出式。无需修改
            element: '#aliValidator', // 页面上预留的渲染验证码的元素，与原代码中预留的页面元素保持一致。
            button: '#aliValidator', // 触发验证码弹窗的元素。button表示单击登录按钮后，触发captchaVerifyCallback函数。您可以根据实际使用的元素修改element的值
            captchaVerifyCallback: this.captchaVerifyCallback, // 业务请求(带验证码校验)回调函数，无需修改
            onBizResultCallback: this.onBizResultCallback, // 业务请求结果回调函数，无需修改
            getInstance: this.getInstance, // 绑定验证码实例函数，无需修改
            slideStyle: {
                width: this.width,
                height: 40,
            }, // 滑块验证码样式，支持自定义宽度和高度，单位为px。其中，width最小值为320 px
            language: this.$i18n.locale === 'zh' ? 'cn' : 'en', // 验证码语言类型，支持简体中文（cn）、繁体中文（tw）、英文（en）
            immediate: true,
            autoRefresh: false,
            rem,
        });
    },
    beforeUnmount() {
        this.captchaButton = null;
        // 必须删除相关元素，否则再次mount多次调用 initAliyunCaptcha 会导致多次回调 captchaVerifyCallback
        document.getElementById('aliyunCaptcha-mask')?.remove();
        document.getElementById('aliyunCaptcha-window-popup')?.remove();
    },
};
</script>
<style lang="scss">
#aliyunCaptcha-sliding-wrapper{
    display: block !important;
}
#aliyunCaptcha-window-embed{
    z-index: 1 !important;
    transform-origin: left;
}
</style>

