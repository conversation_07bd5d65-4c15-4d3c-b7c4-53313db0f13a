<template>
    <div class="password-expiration" :class="{'en-page': lang.locale === 'en'}">
        <el-dialog
            visible
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="false"
            :class="{ 'isPhone': !isPC }"
        >
            <p class="notice">
                {{ lang.t('passwordExpiration.notice') }}
            </p>
            <span slot="footer" class="dialog-footer">
                <el-button class="btn-type-one" @click="goToChange">{{ lang.t('passwordExpiration.btn') }}</el-button>
            </span>

        </el-dialog>
    </div>
</template>

<script>
import { isPC } from 'pub-utils/device.js';
import i18n from 'src/lang';

export default {
    data() {
        return {
            isPC: isPC(),
            lang: i18n,
        };
    },
    methods: {
        goToChange() {
            location.href = '/usercenter/account';
        },
    },
};
</script>

<style lang="scss">
    .password-expiration{
        .el-dialog{
            width: 436px;
            border-radius: 4px;
            .el-dialog__header {
                display: none;
            }
            .el-dialog__title{
                font-weight: normal;
            }
            .el-dialog__body{
                padding: 40px 20px 20px;
            }
        }
        .isPhone .el-dialog{
            width: 90%;
        }
    }
</style>
