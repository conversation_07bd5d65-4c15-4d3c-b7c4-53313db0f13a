import Vue from 'vue';
import PasswordExpiration from 'pub-businessComponents/dialog/PasswordExpiration/index.vue';

const DialogController = Vue.extend(PasswordExpiration);

PasswordExpiration.install = function() {
    const instance = new DialogController().$mount();

    document.body.appendChild(instance.$el);

    Vue.nextTick(() => {
        instance.show = true;
    });
};

export default PasswordExpiration;
