<template>
    <el-dialog
        :title="dialogTitle"
        :visible.sync="show"
        :width=" isEn ? '560px' : '500px'"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleSaveClose"
        :class="{
            'console-dialog-bg':true,
            'field-edit':true,
            'belong-ja-page-console': isJa,
        }"
        :append-to-body="true"
    >
        <div class="field-block" :class="{'en-page': isEn}">
            <div class="left-side">
                <div class="up">
                    <p>{{ $t('editFieldDialog.chooseFieldType') }}</p>
                    <el-radio-group v-model="bizFieldType" :disabled="isDisabled">
                        <el-radio class="radio" :label="item.type" v-for="item in typeList" :key="item.type">{{ item.text }}</el-radio>
                    </el-radio-group>
                </div>
            </div>
            <div class="right-side">
                <el-form :model="fieldObj" label-position="top" class="config-label-form">
                    <el-form-item :label="propItem.label" v-for="propItem in propsList" :key="propItem.prop">
                        <template v-if="propItem.prop === 'bizName'">
                            <el-input v-model="bizName"
                                auto-complete="off"
                                :placeholder="$t('editFieldDialog.namePlace')"
                                :maxlength="30"
                                size="small"
                                @blur="checkName"
                            ></el-input>
                            <span class="name-error" v-show="errorMsg !== ''">
                                <i class="el-icon-ssq-warm-filling"></i>
                                {{ errorMsg }}
                            </span>
                        </template>
                        <!-- 备选项-->
                        <template v-else-if="propItem.prop === 'buttons'">
                            <div class="option-box" v-for="(item, index) in (buttons || [])" :key="index">
                                <el-input
                                    class="option-input"
                                    v-model.trim="item.text"
                                    @blur="checkOptionText(index)"
                                    auto-complete="off"
                                    :maxlength="100"
                                    size="small"
                                    :disabled="isDisabled"
                                ></el-input>
                                <span class="name-error" v-show="item.errorMsg">
                                    <i class="el-icon-ssq-warm-filling"></i>
                                    {{ item.errorMsg }}
                                </span>
                                <i v-show="buttons.length > 2"
                                    class="el-icon-ssq--bs-guanbi delete-icon"
                                    @click="deleteOption(index)"
                                ></i>
                            </div>
                        </template>

                        <template v-else-if="propItem.prop === 'addButtons'">
                            <span class="add-btn" @click="addOption('')"><i class="el-icon-ssq--bs-jia"></i>&nbsp;{{ $t('editFieldDialog.addOptions') }}</span>
                            <span class="batch-add-btn" @click="handleBatchAdd" v-if="['COMBO_BOX'].includes(bizFieldType)"><i class="el-icon-ssq--bs-jia"></i>&nbsp;{{ $t('batchAddOptionsDialog.batchAddOption') }}</span>
                        </template>

                        <!-- 内容填写人 -->
                        <el-radio-group v-model="writeBy"
                            :disabled="isDisabled || bizFieldType === 'DATE_TIME'"
                            v-else-if="propItem.prop ===
                                'writeBy'"
                        >
                            <el-radio label="SENDER">
                                {{ $t('editFieldDialog.sender') }}
                                <el-tooltip class="item" effect="dark" :content="$t('editFieldDialog.senderContent')" placement="top">
                                    <i class="el-icon-ssq-wenhao"></i>
                                </el-tooltip>
                            </el-radio>
                            <el-radio label="SIGNER" v-if="!getIsUae">
                                {{ $t('editFieldDialog.signer') }}
                                <el-tooltip class="item" effect="dark" :content="$t('editFieldDialog.signerContent')" placement="top">
                                    <i class="el-icon-ssq-wenhao"></i>
                                </el-tooltip>
                            </el-radio>
                        </el-radio-group>

                        <!-- 日期样式 -->
                        <el-radio-group v-model="dateFieldFormat" :disabled="isDisabled" v-else-if="propItem.prop === 'dateFieldFormat'">
                            <el-radio class="date-style-item"
                                v-for="item in formatOptions"
                                :label="item.value"
                                :key="item.value"
                            >
                                {{ item.text }}
                                <el-tooltip class="item"
                                    effect="dark"
                                    :content="getTooltipDateText(item.value)"
                                    placement="top"
                                >
                                    <i class="el-icon-ssq-wenhao"></i>
                                </el-tooltip>
                            </el-radio>
                        </el-radio-group>

                        <!-- 数字类型选择不同的整数位 -->
                        <el-radio-group v-model="decimalPlace" :disabled="isDisabled" v-else-if="propItem.prop === 'decimalPlace'">
                            <el-radio :label="0">{{ $t('editFieldDialog.integer') }}</el-radio>
                            <el-radio class="number-style-item" :label="-1">
                                <span class="limit-text">{{ $t('editFieldDialog.decimalLimit') }}</span>
                                <el-select v-model="numberDecimalFieldFormat">
                                    <el-option v-for="item in numberDecimalOptions" :key="item" :label="item" :value="item"></el-option>
                                </el-select>
                                <span class="decimal">{{ $t('editFieldDialog.decimal') }}</span>
                            </el-radio>
                        </el-radio-group>

                        <!-- 字号 -->
                        <el-select
                            popper-class="cus-field-editor-select"
                            v-model="fontSize"
                            :placeholder="$t('editFieldDialog.fontSizePlace')"
                            :disabled="isDisabled"
                            v-else-if="propItem.prop === 'fontSize'"
                        >
                            <el-option
                                v-for="(item, index) in fontSizeRange"
                                :key="index"
                                :label="item.label"
                                :value="convertPtToPx(item.pt, dpi)"
                            ></el-option>
                        </el-select>

                        <!-- 设置文字对齐方式 -->
                        <el-radio-group v-model="alignment" class="alignment-group" v-else-if="propItem.prop === 'alignment'" :disabled="isDisabled">
                            <el-radio v-for="(item, i) in alignmentArr" :key="i" :label="item.label" :class="{'active': alignment==item.label}">
                                <i :class="item.class"></i>
                            </el-radio>
                        </el-radio-group>

                        <!-- 必填 -->
                        <el-checkbox v-model="necessary" :disabled="isDisabled" v-else-if="propItem.prop === 'necessary'">{{ $t('editFieldDialog.required') }}</el-checkbox>
                    </el-form-item>
                </el-form>
                <div slot="footer" class="dialog-footer">
                    <el-button
                        class="confirm"
                        type="primary"
                        @click="handleSaveFieldType"
                    >{{ $t('editFieldDialog.confirm') }}</el-button>
                    <el-button class="cancel" @click="handleSaveClose">{{ $t('editFieldDialog.cancel') }}</el-button>
                </div>
            </div>
        </div>
        <batchAddOptionsDialog :visible="showBatchAddOptionsDialog" :appendToBody="true" @close="showBatchAddOptionsDialog=false" @addComboBoxItems="handleBatchAddOption"></batchAddOptionsDialog>
    </el-dialog>
</template>

<script>
import { convertPtToPx, fontSizeRange } from 'pub-utils/fontSize.js';
import { mapGetters, mapState } from 'vuex';
import resRules from 'pub-utils/regs.js';
import { DATE_FORMATS, TIME_FORMATS } from 'pub-consts/const';
import dayjs from 'dayjs';
import batchAddOptionsDialog from 'pub-businessComponents/batchAddOptionsDialog/index.vue';

export default {
    name: 'DialogCreate',
    components: {
        batchAddOptionsDialog,
    },
    props: {
        curRow: {
            type: Object,
            default: function() {
                return {};
            },
        },
        isDescriptionField: {
            type: Boolean,
            default: false,
        },
        isNewGroupPath: { // 控制台过来需要传，模板的时候默认false
            type: Boolean,
            default: false,
        },
        templateId: { // 模版设置业务字段
            type: String,
            default: '',
        },
        isTemplateField: { // 模版设置业务字段
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            fieldObj: {},
            showBatchAddOptionsDialog: false,
            dialogTitle: this.$t('editFieldDialog.addField'),
            errorMsg: '',
            dpi: 96,
            fontSizeRange,
            convertPtToPx,
            show: true,
            bizFieldType: 'TEXT',
            fieldId: '',
            fontSize: 14,
            bizName: '',
            initName: '',
            necessary: false,
            writeBy: 'SENDER',
            buttons: [],
            dateOptions: [],
            dateFieldFormat: DATE_FORMATS[0].value,
            decimalPlace: 0,
            numberDecimalOptions: [1, 2, 3, 4, 5, 6, 7, 8, 9, 10],
            numberDecimalFieldFormat: 1,
            alignmentArr: [{
                label: 'left',
                class: 'el-icon-ssq-juzuoduiqi',
            }, {
                label: 'center',
                class: 'el-icon-ssq-juzhongduiqi',
            }, {
                label: 'right',
                class: 'el-icon-ssq-juyouduiqi',
            }],
            alignment: 'left',
            // hasApplied: undefined,
        };
    },
    computed: {
        ...mapGetters(['getIsUae']),
        isJa() {
            return this.$i18n.locale === 'ja';
        },
        // 是否为单选/复选业务字段
        isSelectField() {
            return ['SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX'].indexOf(this.bizFieldType) > -1;
        },
        getTooltipDateText() {
            return (formatType) => {
                if (this.bizFieldType === 'DATE_TIME') {
                    return TIME_FORMATS.find(a => a.value === formatType).tooltip;
                }
                return dayjs('2022-09-10').format(formatType.toUpperCase());
            };
        },
        formatOptions() {
            return this.bizFieldType === 'BIZ_DATE' ? this.dateOptions : TIME_FORMATS;
        },
        postData() {
            const { operateType, fieldId } = this.curRow || {};
            let obj = {
                bizFieldType: this.bizFieldType,
                necessary: this.necessary,
                bizName: this.bizName,
                buttons: this.isSelectField ? this.buttons.map(el => el.text) : undefined,
                fieldId: operateType === 'edit' ? fieldId : undefined,
            };
            if (!this.isDescriptionField) {
                obj = {
                    ...obj,
                    fontSize: this.fontSize,
                    writeBy: this.writeBy,
                    dateFieldFormat: this.dateFieldFormat,
                    locale: this.dateOptions.find(a => a.value === this.dateFieldFormat)?.locale || null,
                    decimalPlace: this.decimalPlace === 0 ? 0 : this.numberDecimalFieldFormat,
                    alignment: this.alignment,
                };
            }
            // 模版设置业务字段过来，增加类别参数
            if (this.isTemplateField) {
                obj.bizContractFieldType = this.isDescriptionField ? 'DESCRIPTION' : 'CONTENT';
            }
            return obj;
        },
        typeList() {
            const list = [
                {
                    type: 'TEXT',
                    text: this.$t('editFieldDialog.fieldType.text'),
                },
                {
                    type: 'NUMERIC_VALUE',
                    text: this.$t('editFieldDialog.fieldType.number'),
                },
                {
                    type: 'BIZ_DATE',
                    text: this.$t('editFieldDialog.fieldType.date'),
                },
                {
                    type: 'SINGLE_BOX',
                    text: this.$t('editFieldDialog.fieldType.single'),
                },
                {
                    type: 'MULTIPLE_BOX',
                    text: this.$t('editFieldDialog.fieldType.multi'),
                }, {
                    type: 'COMBO_BOX',
                    text: this.$t('editFieldDialog.fieldType.combo'),
                }, {
                    type: 'DATE_TIME',
                    text: this.$t('editFieldDialog.fieldType.datetime'),
                },
            ];
            return list.filter(item => {
                if (this.isDescriptionField) { // 描述字段目前只支持这三种类型
                    return ['TEXT', 'BIZ_DATE', 'SINGLE_BOX'].includes(item.type);
                }
                if (['SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX'].includes(item.type)) { // 不支持混1
                    return !this.isHybridV1;
                } else if (['NUMERIC_VALUE', 'DATE_TIME'].includes(item.type)) {
                    return this.commonHeaderInfo.openNewContractTemplate;
                }
                return true;
            });
        },
        propsList() {
            const props = [
                {
                    prop: 'bizName',
                    label: this.$t('editFieldDialog.name'),
                }, {
                    prop: 'buttons',
                    label: this.$t('editFieldDialog.options'),
                    isShow: this.isSelectField && this.buttons.length,
                },
                {
                    prop: 'addButtons',
                    isShow: !this.isDisabled && this.isSelectField && this.buttons.length < this.maxButtonsLength,
                },
                {
                    prop: 'writeBy',
                    label: this.$t('editFieldDialog.contentFiller'),
                },
                {
                    prop: 'dateFieldFormat',
                    label: this.$t('editFieldDialog.dateFormate'),
                    isShow: ['BIZ_DATE', 'DATE_TIME'].includes(this.bizFieldType),
                },
                {
                    prop: 'decimalPlace',
                    label: this.$t('editFieldDialog.dateFormate'),
                    isShow: this.bizFieldType === 'NUMERIC_VALUE',
                },
                {
                    prop: 'fontSize',
                    label: this.$t('editFieldDialog.fontSize'),
                    isShow: !['SINGLE_BOX', 'MULTIPLE_BOX'].includes(this.bizFieldType),
                },
                {
                    prop: 'alignment',
                    label: this.$t('editFieldDialog.labelAlign'),
                    isShow: this.bizFieldType === 'TEXT',
                },
                {
                    prop: 'necessary',
                    label: this.$t('editFieldDialog.requirements'),
                },
            ];
            // 描述字段包含 文本，日期和单选
            return props.filter(item => {
                let result = true;
                if (this.isDescriptionField) { // 描述字段目前只支持这三种类型
                    result = ['bizName', 'buttons', 'addButtons', 'necessary'].includes(item.prop);
                }
                return result && (item.isShow === undefined || item.isShow);
            });
        },
        postUrl() {
            const urlMap = {
                content: {
                    new: '/template-api/bizfield/content/add-bizfield',
                    edit: '/template-api/bizfield/content/edit-bizfield',
                },
                describe: {
                    new: '/template-api/bizfield/describe/add-bizfield',
                    edit: '/template-api/bizfield/describe/edit-bizfield',
                },
                templateField: `template-api/v2/custom-scene/${this.templateId}/biz-fields/`,
            };
            const fieldType = this.isDescriptionField ? 'describe' : 'content';
            return this.isTemplateField ? urlMap['templateField'] : urlMap[fieldType][this.curRow.operateType];
        },
        ...mapState(['commonHeaderInfo']),
        // 混合云1.0不支持单选框，复选框
        isHybridV1() {
            return !!this.commonHeaderInfo.hybridServer && this.$hybrid.isAlpha();
        },
        // 模版编辑业务字段的时候除名称外均不可以修改
        isDisabled() {
            return this.isTemplateField && this.curRow.operateType === 'edit';
        },
        maxButtonsLength() {
            let maxLength = 0;
            if (['SINGLE_BOX', 'MULTIPLE_BOX'].includes(this.bizFieldType)) {
                maxLength = 100;
            } else if (['COMBO_BOX'].includes(this.bizFieldType)) {
                maxLength = 500;
            }
            return maxLength;
        },
        isEn() {
            return this.$i18n.locale === 'en';
        },
    },
    watch: {
        bizFieldType(v) {
            if (v === 'BIZ_DATE') {
                // 如果切换为时刻后，再次切换回来时要重置format数据
                if (this.dateFieldFormat && !DATE_FORMATS.map(a => a.value).includes(this.dateFieldFormat)) {
                    // 老字段默认值为yyyy-MM-dd，新字段默认值为yyyy年MM月dd
                    this.dateFieldFormat = this.curRow.fieldId ? this.curRow.dateFieldFormat ||
                        DATE_FORMATS[1].value : DATE_FORMATS[0].value;
                }
                this.initDateFomat();
            } else if (v === 'DATE_TIME') {
                this.dateFieldFormat = TIME_FORMATS[0].value;
                this.writeBy = 'SIGNER';
            } else if (['SINGLE_BOX', 'MULTIPLE_BOX', 'COMBO_BOX'].includes(v)) {
                for (let i = 0; i < this.buttons.length; i++) {
                    this.checkOptionText(i);
                }
            }
        },
    },
    methods: {
        initDateFomat() {
            this.$http.get(`/template-api/bizfield/support-format?isHybrid3=${this.$hybrid.isGamma()}&isOldVer=${!this.commonHeaderInfo.openNewContractTemplate}`)
                .then(res => {
                    const formates = (res.data?.result || DATE_FORMATS);
                    this.dateOptions = this.getIsUae ? formates.filter(item => item.value !== 'yyyy年MM月dd日') : formates;
                });
        },
        deleteOption(index) {
            this.buttons.splice(index, 1);
        },
        addOption(text) {
            if (this.buttons.length >= this.maxButtonsLength) {
                this.$MessageToast.error(this.$t('editFieldDialog.errorMsg.overCountLimit'));
                return;
            }
            this.buttons.push({
                text: text || '',
                errorMsg: '',
            });
        },
        handleBatchAddOption(items) {
            // 先过滤重复项，SAAS-31331
            const nowValues = this.buttons.map(a => a.text);
            items.textValues.filter(text => !nowValues.includes(text)).forEach((item) => {
                this.addOption(item);
            });
            this.showBatchAddOptionsDialog = false;
        },
        handleBatchAdd() {
            this.showBatchAddOptionsDialog = true;
        },
        checkOptionText(index) {
            if (this.trim(this.buttons[index].text) === '')  {
                this.buttons[index].errorMsg = this.$t('editFieldDialog.errorMsg.optionEmpty');
                return;
            }
            if (['SINGLE_BOX', 'MULTIPLE_BOX'].includes(this.bizFieldType) && this.trim(this.buttons[index].text).indexOf(',') !== -1)  {
                this.buttons[index].errorMsg = this.$t('editFieldDialog.errorMsg.optionHasEnComma');
                return;
            }
            // 业务字段需要做格式校验，不能包括特殊字符, CFD-6264
            if (resRules.fieldValueReg.test(this.buttons[index].text)) {
                this.buttons[index].errorMsg = this.$t('editFieldDialog.errorMsg.optionNameFormat'); // 备选项名字不能包含特殊字符\\/#()
                return;
            }
            const sameIndex = this.buttons.findIndex((el, idx) => {
                return el.text === this.buttons[index].text && idx !== index;
            });
            if (sameIndex > -1) {
                // this.buttons[sameIndex].errorMsg = this.$t('editFieldDialog.errorMsg.optionNameRepeat');
                this.buttons[index].errorMsg = this.$t('editFieldDialog.errorMsg.optionNameRepeat');
            } else {
                this.buttons[index].errorMsg = '';
            }
            console.log(index);
        },
        trim(data) {
            return data.replace(/\s+/g, '');
        },
        checkName() {
            if (this.trim(this.bizName) === '') {
                this.errorMsg = this.$t('editFieldDialog.errorMsg.enterFieldName');
            } else if (this.isDescriptionField || this.isTemplateField) {
                this.errorMsg = '';
            } else if (this.curRow.operateType === 'new' || (this.curRow.operateType === 'edit' && this.bizName !== this.initName)) {
                const bizName = encodeURI(this.bizName);
                this.$http.get(`/template-api/bizfield/selectByName?bizName=${bizName}`, {
                    params: {
                        isNewGroup: this.isNewGroupPath,
                    },
                }).then(res => {
                    if (res.data.code !== '140008') {
                        if (res.data.result.onOff === true) {
                            this.errorMsg = this.$t('editFieldDialog.errorMsg.fieldNameExist');
                        } else if (res.data.result.onOff === false) {
                            this.errorMsg = this.$t('editFieldDialog.errorMsg.fieldNameExistToOn');
                        }
                    } else {
                        this.errorMsg = '';
                    }
                });
            }
        },
        // 添加新合同类型
        handleSaveFieldType() {
            this.checkName();
            if (this.errorMsg !== '') {
                return;
            }
            if (this.isSelectField) {
                // 添加自定义字段逻辑修改，单选框选项最少可以有一个选项
                if (['SINGLE_BOX'].indexOf(this.bizFieldType) > -1) {
                    if (this.buttons.length < 1) {
                        this.$MessageToast.error(this.$t('editFieldDialog.errorMsg.optionsNeed'));
                        return;
                    }
                } else {
                    if (this.buttons.length < 2) {
                        this.$MessageToast.error(this.$t('editFieldDialog.errorMsg.optionsNeed'));
                        return;
                    }
                }
                for (let i = 0; i < this.buttons.length; i++) {
                    this.checkOptionText(i);
                    if (this.buttons[i].errorMsg !== '') {
                        return;
                    }
                }
            }
            this.$http.post(
                this.postUrl,
                this.postData,
                {
                    params: {
                        isNewGroup: this.isNewGroupPath,
                    },
                }).then(() => {
                this.$MessageToast.success(this.curRow.operateType === 'new' ? this.$t('editFieldDialog.addSuccess') : this.$t('editFieldDialog.editSuccess'));
                this.handleSaveClose(true);
            });
        },
        // 关闭弹窗
        handleSaveClose(status) {
            this.$emit('close', status);
        },
    },
    created() {
        console.log(this.curRow);
        if (this.curRow.operateType === 'edit') {
            this.dialogTitle = this.$t('editFieldDialog.editField');
            this.bizFieldType = this.curRow.bizFieldType;
            // 默认是xxxx-xx-xx
            this.dateFieldFormat = this.curRow.dateFieldFormat || DATE_FORMATS[1].value;
            this.fieldId = this.curRow.fieldId;
            this.fontSize = this.curRow.fontSize;
            this.bizName = this.curRow.bizName;
            this.necessary = this.curRow.necessary;
            this.writeBy = this.curRow.writeBy;
            this.initName = this.curRow.bizName;
            this.alignment = this.curRow.alignment;
            if (this.bizFieldType === 'NUMERIC_VALUE') {
                this.decimalPlace = this.curRow.decimalPlace === 0 ?  0 : -1;
                this.numberDecimalFieldFormat = this.curRow.decimalPlace === 0 ? 1 : this.curRow.decimalPlace;
            }
            if (this.isSelectField) {
                this.buttons = this.curRow.buttons.map(el => {
                    return {
                        text: el,
                        errorMsg: '',
                    };
                });
            }
        }
    },
};
</script>

<style lang="scss">
.field-edit .el-dialog .en-page{
    .left-side {
        width: 180px  !important;
    }
    .right-side{
        width: 380px  !important;
    }
}
.belong-ja-page-console{
    .el-dialog{
        width: 554px !important;
        .el-dialog__body {
            width: 550px;
            .right-side{
                width: 384px  !important;
            }
        }
    }
}
.field-edit .el-dialog{
    font-weight: 500;
    box-sizing: border-box;
    .el-dialog__body {
        padding: 0 !important;
    }
    .el-radio+.el-radio {
        margin-left: 0;
        [dir=rtl] & {
            margin-left: 30px;
            margin-right: 0;
        }
    }
    .name-error{
        font-size: 12px;
        color: #f86b26;
        // position: absolute;
    }
    .el-input{
        display: block;
        input{
            display: block;
        }
    }
    .field-block {
        width: 100%;
        height: 450px;
        .left-side {
            width: 160px;
            height: 100%;
            padding: 24px 28px;
            background-color: #f6f9fc;
            float: left;
            box-sizing: border-box;
            .el-radio {
                margin-top: 16px;
            }
            .up {
                padding-bottom: 40px;
            }
            .down {
                padding-top: 20px;
            }

            [dir=rtl] & {
                float: right;
            }
        }
        .right-side{
            width: 340px;
            height: 100%;
            background-color: #fff;
            padding: 15px 10px 15px 20px;
            float: right;
            overflow: auto;
            box-sizing: border-box;
            [dir=rtl] & {
                float: left;
                padding: 15px 20px 15px 10px;
            }
            .config-label-form {
                max-height: calc(100% - 40px);
                overflow-y: auto;
            }
            .el-form-item {
                margin-bottom: 15px;
            }
            .el-form-item__content{
                line-height: unset;
                position: relative;
            }
            .option-box{
                position: relative;
                margin-top: 5px;
            }
            .delete-icon{
                position: absolute;
                left: 280px;
                font-size: 12px;
                color: $--color-text-secondary;
                top: 10px;
                cursor: pointer;
                &:hover{
                    color: $--color-primary;
                }
            }
            .add-btn{
                cursor: pointer;
                font-size: 14px;
                color: $--color-primary;
            }
            .batch-add-btn {
                cursor: pointer;
                font-size: 14px;
                color: $--color-primary;
                margin-left: 40px;
            }
            .el-button {
                height: 34px;
                font-size: 12px;
            }
            .el-input__inner{
                height: 30px;
                width: 270px;
            }

            .dialog-footer{
                position: absolute;
                bottom: 10px;
                right: 20px;
                [dir=rtl] & {
                    right: auto;
                    left: 20px;
                }
            }
            .cancel {
                padding: 0 21px;
                border-color: #ccc;
                background: #f8f8f8;
                &:hover {
                    border-color: #ccc;
                    background: #fff;
                }
            }
            .confirm {
                padding: 0 36px;
                border-color: #127fd2;
                background: #127fd2;
                color: #fff;
                &:hover {
                    background: #1687dc;
                    border-color: #1687dc;
                }
            }
            .el-radio+.el-radio:not(.date-style-item) {
                margin-left: 35px;
                [dir=rtl] & {
                    margin-left: 0;
                    margin-right: 30px;
                }
            }
            .el-icon-ssq-wenhao {
                color: #666;
            }
            .date-style-item {
                display: block;
                margin-top: 5px;
            }
            .number-style-item {
                .el-input__inner {
                    width: 60px;
                    display: inline-block;
                    padding-left: 5px;
                    padding-right: 10px;
                }

                [dir=rtl] & {
                    .el-radio__label {
                        display: inline-block;
                        .limit-text {
                            float: right;
                            line-height: 30px;
                        }
                        .decimal {
                            float: left;
                            line-height: 30px;
                        }
                    }
                    .el-input__inner {
                        padding-left: 10px;
                        padding-right: 5px;
                    }
                }
            }
            .alignment-group.el-radio-group {
                background: $--background-color-regular;
                border: 1px solid $--border-color-light;
                border-radius: 2px;
                width: 270px;
                .el-radio {
                    width: 32%;
                    margin: 0;
                    text-align: center;
                    padding: 4px 0;
                    [dir=rtl] & {
                        margin: 0;
                    }
                    i[class^="el-icon-ssq-"] {
                        font-size: 20px;
                    }
                    &.active {
                        background: $--color-white;
                        i[class^="el-icon-ssq-"] {
                            color: $theme-color;
                        }
                    }
                    &:nth-child(2) {
                        width: 34%;
                        border-left: 1px solid $--border-color-light;
                        border-right: 1px solid $--border-color-light;
                    }
                }
                .el-radio .el-radio__input .el-radio__inner {
                    display: none;
                }
            }

        }
    }
}
</style>
