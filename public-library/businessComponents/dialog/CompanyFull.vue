<!-- 业务组件：选择企业成员弹框-->
<!-- 引用位置 用户中心页面：contacts-->
<template>
    <el-dialog
        :title="title || `${$t('companyFull.addMember')}`"
        class="company-full-tree-dialog el-dialog-bg console-dialog-bg"
        v-show="state"
        :visible.sync="state"
        :close-on-click-modal="false"
        :close-on-press-escape="false"
        :before-close="handleClose"
    >
        <el-form @submit.native.prevent :model="companyFullDialogForm" ref="companyFullDialogForm" class="demo-dynamic">
            <!-- 模板授权tab -->
            <slot name="templateAuthorizationTab"></slot>
            <div class="member-block company-block">
                <p>{{ $t('companyFull.chooseMember') }}：</p>
                <div class="cloumn company-cloumn">
                    <!-- 部门树 -->
                    <div class="company-inner-cloumn company-tree-cloumn">

                        <CompanyDeptTree
                            :selectAble="false"
                            :onlyActive="onlyActive"
                            @getCurrentDept="handleGetCurrentDept"
                            @seachResult="handleMemberSearchResult"
                            ref="CompanyDeptTree"
                        ></CompanyDeptTree>
                    </div>
                    <!-- 部门列表 -->
                    <div class="company-inner-cloumn department-member-cloumn">

                        <div v-if="selectType === 'radio'">
                            <!-- 单选成员 -->
                            <el-radio-group
                                v-model="companyFullDialogForm.selectedMember"
                                @change="handleSelectChange"
                            >
                                <p v-for="member in cacheMembers" :key="member.userId">
                                    <el-radio :label="member.userId">{{ member.empName }}</el-radio>
                                </p>
                            </el-radio-group>
                        </div>
                        <div v-else-if="selectType === 'checkBox'">
                            <!-- 多选成员 -->
                            <p>
                                <el-checkbox :indeterminate="isSelectedAll" v-model="selectedAllMembers" @change="handleCheckAllChange">{{ $t('companyFull.selectAll') }}</el-checkbox>
                            </p>
                            <el-checkbox-group
                                v-model="companyFullDialogForm.selectedMembers"
                                @change="handleSelectChange"
                            >
                                <p v-for="member in cacheMembers" :key="member.userId">
                                    <el-checkbox :label="member.userId">{{ member.empName }}</el-checkbox>
                                </p>
                            </el-checkbox-group>
                        </div>

                    </div>
                    <div class="clear"></div>
                </div>
            </div>
            <!-- 已选成员列表 -->
            <div class="member-block selected-block">
                <p>{{ exsitedMemberTitle || `${$t('companyFull.choosedMember')}：` }}</p>
                <div class="cloumn selected-cloumn">
                    <div v-if="selectType === 'radio'">
                        <p v-if="companyFullDialogForm.selectedMember != null">
                            <span>{{ cacheMembers[companyFullDialogForm.selectedMember].empName }}</span>
                            <i class="el-icon-ssq-delete" @click="cancelSelectedMember"></i>
                        </p>
                    </div>
                    <div v-else-if="selectType === 'checkBox' && cacheExistedMembers.length">
                        <template v-for="(member, index) in cacheExistedMembers">
                            <p v-if="member" :key="member.userId || index">
                                <span>{{ member.empName }}</span>
                                <i class="el-icon-ssq-delete" @click="cancelSelectedMember(member.userId, index)"></i>
                            </p>
                        </template>
                    </div>
                </div>
            </div>
            <div class="clear"></div>
            <slot name="authDuration"></slot>
        </el-form>
        <div slot="footer" class="dialog-footer">
            <slot name="templateAuthorizationInfo"></slot>
            <el-button
                type="primary"
                class="console-btn console-btn-primary"
                @click="chooseMembers"
            >{{ $t('companyFull.confirm') }}</el-button>
        </div>
    </el-dialog>
</template>

<script>
const list = [];

import CompanyDeptTree from 'pub-businessComponents/companyDeptTree';

export default {
    components: {
        CompanyDeptTree,
    },
    props: {
        state: Boolean,
        selectType: {
            type: String,
            default: 'checkBox',
        },
        onlyActive: {
            type: Boolean,
            default: true,
        },
        title: {
            type: String,
            default: '',
        },
        // 已存在成员的 userId list
        exsitedUserIds: {
            type: Array,
            default: () => {
                return [];
            },
        },
        exsitedMemberTitle: {
            type: String,
            default: '',
        },
        submitEmpty: {
            type: Boolean,
            default: false,
        },
        // 过滤不展示的userId
        filterIgnoreUserIds: {
            type: Array,
            default: () => [],
        },
    },

    data() {
        return {
            cacheMembers: {

            },
            cacheExistedUserIds: [],
            cacheExistedMembers: [],
            companyFullDialogForm: {
                count: 1,
                members: list,
                selectedMember: null, // 只作为 radio 状态下单选成员的数据显示
                selectedMembers: [], // 放置已存在成员的 userId
                selectedMembersList: [], // 返回给父组件的选中的成员
            },
            // 是否选中了所有成员
            selectedAllMembers: false,
            // 全选按钮的不确定状态
            isSelectedAll: false,
        };
    },
    watch: {
        exsitedUserIds: {
            handler() {
                const allUserId = [...this.cacheExistedUserIds, ...this.exsitedUserIds];
                this.cacheExistedUserIds = this.companyFullDialogForm.selectedMembers = [...new Set(allUserId)];
                this.syncMembers(this.cacheExistedUserIds);
                this.syncCheckStatus();
            },
            immediate: true,
            deep: true,
        },
        state: {
            handler(val) {
                if (val) {
                    this.$refs.CompanyDeptTree && this.$refs.CompanyDeptTree.getDepartments({ type: 'init' });
                }
            },
        },
        'companyFullDialogForm.selectedMembersList': {
            handler(val) {
                this.$emit('selectedDataChange', {
                    membersData: val,
                    type: 'member',
                });
            },
            deep: true,
        },
    },
    methods: {
        // 从 <CompanyDeptTree> 组件中获取当前选中的树节点
        handleGetCurrentDept(val) {
            this.getMembers(val.deptId);
        },
        // 从 <CompanyDeptTree> 组件中获取搜索后的数据
        handleMemberSearchResult(val) {
            // this.companyFullDialogForm.members = val;

            this.cacheMembers = {};

            val.data.forEach(member => {
                // 通过 userId 标志缓存部门成员
                this.cacheMembers[(member || {}).userId] = member;
            });
        },
        // 获取部门成员
        getMembers(deptId) {
            // 判断获取所有用户还是所有可用用户
            if (this.onlyActive) {
                this.$http({
                    method: 'get',
                    url: `/ents/depts/${deptId}/out-employees`,
                }).then((res) => {
                    this.pushMember(res.data.filter(e => !this.filterIgnoreUserIds.includes(e.userId)));
                })
                    .catch(() => {

                    });
            } else {
                this.$http({
                    method: 'get',
                    url: `/ents/depts/${deptId}/in-employees`,
                }).then((res) => {
                    this.pushMember(res.data.filter(e => !this.filterIgnoreUserIds.includes(e.userId)));
                })
                    .catch(() => {

                    });
            }
        },
        // 处理获取到的成员信息
        pushMember(memberData) {
            const memberList = [];
            this.cacheMembers = {};

            memberData.forEach(member => {
                memberList.push(member);

                // 通过 userId 标志缓存部门成员
                this.cacheMembers[member.userId] = member;
            });

            // this.companyFullDialogForm.members = memberList;

            // 同步已存在的成员
            this.syncMembers(this.cacheExistedUserIds);

            this.syncCheckStatus();
        },
        resetValues() {
            this.companyFullDialogForm.selectedMembers = [];
            this.companyFullDialogForm.selectedMembersList = [];
            this.cacheExistedMembers = [];
            this.cacheExistedUserIds = [];
            this.companyFullDialogForm.selectedMember = null;
        },
        // 关闭弹框
        handleClose() {
            // 重置表单
            this.resetValues();
            this.$emit('close');
        },
        // 选择部门成员
        handleSelectChange(value) {
            if (this.selectType === 'radio') {
                this.companyFullDialogForm.selectedMembersList = [this.cacheMembers[value]];
                this.companyFullDialogForm.selectedMember = value;
            } else if (this.selectType === 'checkBox') {
                this.syncExistedUserIds(value);
                this.syncMembers(value);

                const checkedCount = value.length;

                let membersLength = 0;
                // eslint-disable-next-line no-unused-vars
                for (const i in this.cacheMembers) {
                    membersLength++;
                }

                // 全选按钮状态
                this.selectedAllMembers = checkedCount === membersLength;
                this.isSelectedAll = checkedCount > 0 && checkedCount < membersLength;
            }
        },
        // 同步 cacheExistedUserIds
        syncExistedUserIds(userIds) {
            this.cacheExistedUserIds.splice(0, this.cacheExistedUserIds.length);

            userIds.forEach(userId => {
                this.cacheExistedUserIds.push(userId);
            });
        },
        // 同步 cacheExistedMembers、selectedMembersList
        syncMembers(memberIds) {
            // this.companyFullDialogForm.selectedMembersList.splice(0, this.companyFullDialogForm.selectedMembersList.length);

            if (!memberIds.length) {
                this.cacheExistedMembers.splice(0, this.cacheExistedMembers.length);
                this.companyFullDialogForm.selectedMembersList.splice(0, this.companyFullDialogForm.selectedMembersList.length);
                return;
            }

            memberIds.forEach(userId => {
                // this.companyFullDialogForm.selectedMembersList.push(this.cacheMembers[userId]);

                let cacheMemberNum = 0;
                // eslint-disable-next-line no-unused-vars
                for (const i in this.cacheMembers) {
                    cacheMemberNum++;
                }

                if (cacheMemberNum > 0) {
                    if (!this.companyFullDialogForm.selectedMembersList.filter(member => {
                        // eslint-disable-next-line eqeqeq
                        return (member || {}).userId == userId;
                    }).length) {
                        this.companyFullDialogForm.selectedMembersList.push(this.cacheMembers[userId]);
                    }

                    this.companyFullDialogForm.selectedMembersList.filter((member, index) => {
                        if (memberIds.indexOf((member || {}).userId) === -1) {
                            this.companyFullDialogForm.selectedMembersList.splice(index, 1);
                        }
                    });

                    if (!this.cacheExistedMembers.filter(member => {
                        // eslint-disable-next-line eqeqeq
                        return (member || {}).userId == userId;
                    }).length) {
                        this.cacheExistedMembers.push(this.cacheMembers[userId]);
                    }

                    this.cacheExistedMembers.filter((member, index) => {
                        if (memberIds.indexOf((member || {}).userId) === -1) {
                            this.cacheExistedMembers.splice(index, 1);
                        }
                    });
                }
            });
        },
        // 同步全选状态
        syncCheckStatus() {
            let sum = 0;
            // eslint-disable-next-line no-unused-vars
            for (const i in this.cacheMembers) {
                sum++;
            }

            this.selectedAllMembers = (this.cacheExistedUserIds.length  === sum && this.cacheExistedUserIds.length !== 0);
            this.isSelectedAll = this.companyFullDialogForm.selectedMembers.length > 0 && this.companyFullDialogForm.selectedMembers.length < sum;
        },
        // checkBox 时全选部门成员
        handleCheckAllChange(event) {
            // 如果全选则将 cacheMember 中的成员添加到已选成员，否则将已选成员置空
            // 并且切换全选 checkBox 的 indeterminate 状态
            if (event) {
                for (const i in this.cacheMembers) {
                    const curMember = this.cacheMembers[i];

                    this.checkExistOrAppend(this.companyFullDialogForm.selectedMembersList, curMember);
                    this.checkExistOrAppend(this.cacheExistedMembers, curMember);

                    this.checkExistOrAppend(this.companyFullDialogForm.selectedMembers, curMember.userId);
                    this.checkExistOrAppend(this.cacheExistedUserIds, curMember.userId);
                }
            } else {
                for (const i in this.cacheMembers) {
                    const userId = this.cacheMembers[i].userId;

                    this.deleteArrayItem(this.cacheExistedMembers, userId);
                    this.deleteArrayItem(this.companyFullDialogForm.selectedMembersList, userId);

                    this.deleteArrayItem(this.cacheExistedUserIds, userId);
                    this.deleteArrayItem(this.companyFullDialogForm.selectedMembers, userId);
                }

                // this.companyFullDialogForm.selectedMembers = [];
            }

            this.syncCheckStatus();
        },

        checkExistOrAppend(array, member) {
            if (typeof member === 'object') {
                !array.filter(item => {
                    // eslint-disable-next-line eqeqeq
                    return item.userId == member.userId;
                }).length && array.push(member);
            }

            if (typeof member === 'string') {
                array.indexOf(member) === -1 && array.push(member);
            }
        },
        deleteArrayItem(array, userId) {
            array.filter((item, index) => {
                // eslint-disable-next-line eqeqeq
                if (typeof item === 'object' && item.userId == userId) {
                    array.splice(index, 1);
                }

                // eslint-disable-next-line eqeqeq
                if (typeof item === 'string' &&  item == userId) {
                    array.splice(index, 1);
                }
            });
        },
        // 取消选择已选成员
        cancelSelectedMember(userId) {
            if (this.selectType === 'radio') {
                // 如果是 radio 则直接将 selectedMembers 置空，并将 selectedMember 设为 null
                this.resetValues();
            } else if (this.selectType === 'checkBox') {
                // 如果是 checkBox 则将 selectedMembers 中对应的成员删除

                this.deleteArrayItem(this.cacheExistedMembers, userId);
                this.deleteArrayItem(this.companyFullDialogForm.selectedMembersList, userId);

                this.deleteArrayItem(this.cacheExistedUserIds, userId);
                this.deleteArrayItem(this.companyFullDialogForm.selectedMembers, userId);

                this.isSelectedAll = true;
            }
        },
        // 选择完成员，传回父组件
        chooseMembers() {
            // eslint-disable-next-line eqeqeq
            if (!this.submitEmpty && (this.companyFullDialogForm.selectedMembersList.length == 0 || this.companyFullDialogForm.selectedMembersList[0] == undefined)) {
                return this.$MessageToast.error(this.$t('companyFull.noneMemberChoosedTip'));
            }
            this.$emit('choose', this.companyFullDialogForm.selectedMembersList.filter(notNull => notNull));
            this.handleClose();
        },
    },
};
</script>
<style lang="scss">
	.company-full-tree-dialog{
         .el-dialog{
            width: 720px;
         }
		.member-block{
			display: inline-block;
			float: left;

			.cloumn{
				margin-top: 15px;
				height: 330px;
				border: 1px solid $--border-color-lighter;
				background: $--color-white;
			}
		}

		.company-block{
			width: 440px;
			margin-right: 15px;

			.company-inner-cloumn{
				display: inline-block;
				float: left;
                overflow-y: auto;
                box-sizing: border-box;
			}
			.company-tree-cloumn{
				width: 250px;
				height: 328px;
				padding: 10px;
                border-right: 1px solid $--border-color-lighter;
                .ssq-tree{
                    margin-top: 10px;
                    border:none;
                }
			}
			.department-member-cloumn{
				width: 185px;
				height: 100%;
				padding: 10px;

				p{
					position: relative;
					height: 40px;
					margin-bottom: 5px;
					line-height: 40px;

					.el-icon-ssq-user-filling{
						margin-left: 20px;
						margin-right: 5px;
						font-size: 30px;
                        line-height: 34px;
						background: $background-color-gray;
						color: $--color-info-light;
						vertical-align: middle;
					}

                    /* .el-radio{
                        margin-left: 20px;
                        vertical-align: middle;

                        .el-radio__input{
                            position: absolute;
                            left: -20px;
                            top: 10px;
                        }
                    } */

				}

				.el-checkbox-group{
					.el-checkbox{
						.el-checkbox__input{
							vertical-align: middle;
						}
                        .el-checkbox__label{
                            display: inline-block;
                            max-width: 150px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            vertical-align: middle;
                        }
					}
				}
				.el-radio-group{
					.el-radio{
						.el-radio__input{
							vertical-align: middle;
                        }
                        .el-radio__label{
                            display: inline-block;
                            max-width: 150px;
                            overflow: hidden;
                            white-space: nowrap;
                            text-overflow: ellipsis;
                            vertical-align: middle;
                            line-height: 2;
                        }
					}
				}
			}
		}

		.selected-block{
			width: 190px;

			.selected-cloumn{
				overflow-y: auto;

				p{
					position: relative;
					padding: 10px 10px 0;
					line-height: 30px;

					.el-icon-ssq-user-filling{
						float: left;
						width: 30px;
						height: 30px;
						margin-right: 10px;
						font-size: 30px;
                        line-height: 34px;
						background: $background-color-gray;
						color: $--color-info-light;
					}

					span{
						display: inline-block;
                        margin-right: 15px;
						line-height: 18px;
						font-size: 12px;
					}

					.el-icon-ssq-delete{
						position: absolute;
                        right: 10px;
                        top: 17px;
						line-height: 18px;
						color: $--color-text-secondary;
						cursor: pointer;
					}
				}

			}
		}

		.console-btn-primary{
            min-width: 100px;
            height: 34px;
			span{
				font-size: 14px;
			}
		}
	}
</style>
