<!-- 业务组件：密码复杂度过低提示弹框-->
<!-- 引用位置 路由拦截处理，基本左所有页面-->
<template>
    <div class="reset-password" v-if="show" :class="{'en-page': lang.locale === 'en'}">
        <el-dialog
            :title="lang.t('resetPassword.safeTip')"
            :visible.sync="show"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :show-close="canClose"
            :class="{ 'isPhone': !isPC }"
        >
            <p class="notice">
                <i class="el-icon-ssq-xuyaowoqianshu"></i>
                <span><!-- 系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。 -->{{ lang.t('resetPassword.weakPswTip') }}</span>
            </p>
            <el-form :model="formData" :rules="rules" ref="ruleForm" class="reset-password__form" :label-width="isPC ? '80px' : ''">
                <el-form-item :label="lang.t('resetPassword.oldPsw')" prop="oldPwd">
                    <el-input
                        type="password"
                        :placeholder="lang.t('resetPassword.plsInputOldPsw')"
                        auto-complete="new-password"
                        v-model="formData.oldPwd"
                    >
                    </el-input>
                </el-form-item>
                <el-form-item :label="lang.t('resetPassword.newPsw')" prop="newPwd">
                    <el-input
                        type="password"
                        :placeholder="lang.t('resetPassword.pswRule')"
                        auto-complete="new-password"
                        v-model="formData.newPwd"
                    >
                        <ElIEye slot="suffix"></ElIEye>
                    </el-input>
                </el-form-item>
                <el-form-item class="submit">
                    <el-button class="btn-type-one" @click="formSubmit"><!-- 确定 -->{{ lang.t('resetPassword.confirm') }}</el-button>
                </el-form-item>
            </el-form>
        </el-dialog>
    </div>
</template>

<script>
import { isPC } from 'pub-utils/device.js';
import { isComplexPass } from 'pub-utils/regsUtils.js';
import ElIEye from 'pub-components/elIconfont/elIEye';
import i18n from 'src/lang';

export default {
    components: {
        ElIEye,
    },
    data() {
        return {
            isPC: isPC(),
            show: true,
            formData: {
                oldPwd: '',
                newPwd: '',
            },
            rules: {
                oldPwd: [
                    { required: true, message: i18n.t('resetPassword.oldPswNotEmpty'), trigger: 'blur' }, // 原密码不能为空
                ],
                newPwd: [
                    { required: true, message: i18n.t('resetPassword.newPswNotEmpty'), trigger: 'blur' }, // 新密码不能为空
                ],
            },
            lang: i18n,
            canClose: false, // CFD-10313 更换密码弹窗改成不允许关闭
        };
    },
    computed: {
        // 已废弃，因为在 CFD-10313 默认不允许关闭
        // huaweiUserFlag() {
        //     return store.state.commonHeaderInfo.huaweiUserFlag;
        // },
    },
    watch: {
        show(val) {
            if (!val) {
                this.$localStorage.remove('isPwdLogin');
            }
        },
    },
    methods: {
        formSubmit() {
            const checkNewPwd = () => {
                if (isComplexPass(this.formData.newPwd)) {
                    this.changeLoginCode();
                } else {
                    this.formData.newPwd = '';
                }
            };
            this.$refs.ruleForm.validate(val => {
                val && checkNewPwd();
            });
        },
        // 更改登录密码
        changeLoginCode() {
            this.$http.put('/users/login-pwd', {
                newPwd: this.formData.newPwd,
                oldPwd: this.formData.oldPwd,
            }).then(() => {
                this.$MessageToast({
                    message: this.lang.t('resetPassword.modifySuccess'), // 修改成功
                    type: 'success',
                });
                this.show = false;
            });
        },
    },
};
</script>

<style lang="scss">
    .reset-password{
        .el-dialog{
            width: 436px;
            border-radius: 4px;
            .el-dialog__header {
                padding: 20px 20px 0;
            }
            .el-dialog__title{
                font-weight: normal;
            }
            .el-dialog__body{
                padding: 81px 20px 10px;
            }
            .notice{
                color: $--color-text-primary;
                font-size: 12px;
                line-height: 35px;
                background: $--color-danger-lighter;
                width: 100%;
                position: absolute;
                top: 57px;
                left: 0;
                z-index: 2;
                text-align: center;
                i{
                    color: $--color-danger;
                    font-size: 14px;
                }
            }
            .el-form-item{
                margin-bottom: 20px;
                .el-form-item__content{
                    width: 280px;
                    line-height: 30px;
                }
                .el-form-item__label{
                    line-height: 1;
                    padding: 8px 12px 8px 0;
                }
                .el-input__inner{
                    height: 30px;
                }
                .el-button{
                    padding: 6px 25px;
                }
            }
        }
        .isPhone .el-dialog{
            width: 90%;
        }
    }
</style>
