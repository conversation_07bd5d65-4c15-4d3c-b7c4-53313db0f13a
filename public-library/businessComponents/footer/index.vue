<!-- 业务组件：公共底部 -->
<!-- 引用位置 基本页面均用-->
<template>
    <!-- 日文环境、日语时展示日文footer -->
    <JaFooter v-if="getIsForeignVersion" :langCacheOnly="langCacheOnly"></JaFooter>
    <!-- 二级域名下显示定制footer -->
    <CustomFooter v-else-if="!isEntHost" :langCacheOnly="langCacheOnly" class="login-footer"></CustomFooter>
    <footer v-else class="register-footer">
        <ul class="clear font-size-zero">
            <li v-if="isCanSwitchLanguage" class="lang-switch-btn">
                <LangSwitch :cacheOnly="langCacheOnly"></LangSwitch>
                <i>|</i>
            </li>
            <li class="open-platform">
                <a target="_blank" :href="`https://${openHost}/#/login`">
                    <span>{{ $t('commonFooter.openPlatform') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="about">
                <a target="_blank" @click="handleFooterClick('about')">
                    <span>{{ $t('commonFooter.aboutBestSign') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="contact">
                <a target="_blank" @click="handleFooterClick('contact')">
                    <span>{{ $t('commonFooter.contact') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="recruitment">
                <a target="_blank" @click="handleFooterClick('recruitment')">
                    <span>{{ $t('commonFooter.recruitment') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="help" v-if="lang !== 'en'">
                <a target="_blank" @click="handleFooterClick('help')">
                    <span>{{ $t('commonFooter.help') }}</span>
                </a>
                <i>|</i>
            </li>
            <li class="copyright">
                <span>V4.0.0 {{ $t('commonFooter.copyright') }} © {{ backEndCopyRightRange || defaultCopyRightRange }} {{ $t('commonFooter.company') }}</span>
                <i>|</i>
            </li>
            <li class="on-record">
                <span>{{ $t('commonFooter.record') }}</span>
            </li>
            <li class="on-record" v-if="isHubblePage">
                <i>|</i>
                <span>{{ $t('commonFooter.hubbleRecordId') }}</span>
            </li>
        </ul>
    </footer>
</template>

<script>
import CustomFooter from 'pub-businessComponents/footer/CustomFooter.vue';
import JaFooter from 'pub-businessComponents/footer/JaFooter/';
import LangSwitch from 'pub-businessComponents/langSwitch';
import { mapState, mapGetters } from 'vuex';
import cookie from 'vue-cookie';
export default {
    components: {
        LangSwitch,
        CustomFooter,
        JaFooter,
    },
    props: {
        langCacheOnly: {
            default: false,
            type: Boolean,
        },
        isCanSwitchLanguage: {
            default: true,
            type: Boolean,
        },
    },
    data() {
        return {
            openHost: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'openapi.bestsign.info' : 'openapi.bestsign.cn',
            host: this.GLOBAL.ENV_NAME === 'PRE_ENV' ? 'www.bestsign.info' : 'www.bestsign.cn',
            backEndCopyRightRange: this.$cookie.get('copyRightRange'),
            defaultCopyRightRange: `2014-${new Date().getFullYear()}`,
        };
    },
    computed: {
        ...mapState(['commonHeaderInfo', 'features']),
        ...mapGetters(['getIsForeignVersion']),
        isBrand() {
            return ~~this.$cookie.get('isBrand') === 1;
        },
        lang() {
            return this.$i18n.locale;
        },
        // 判断二级域名是否是ent
        isEntHost() {
            const fullHost = window.location.href;
            const secondHost = fullHost.split('.')[0].split('//')[1];
            return secondHost.includes('ent') || secondHost.includes('ai') || process.env.NODE_ENV.indexOf('development') > -1;
        },
        accessToken() {
            return cookie.get('access_token') === null ? '' : cookie.get('access_token');
        },
        isHubblePage() {
            return this.$route.meta.isHubblePage === true;
        },
    },
    methods: {
        async handleFooterClick(type) {
            const anonymousID  = await this.$sensors.getAnonymousID();
            const url = `?pageFrom=ent&identifyId=${anonymousID}`;
            const map = {
                about: `https://${this.host}/about/about-us${url}`,
                contact: `https://${this.host}/contact-us${url}`,
                recruitment: `https://${this.host}/about/join-us${url}`,
                help: `https://${this.host}/help/FAQ${url}&entId=${this.commonHeaderInfo.currentEntId}&token=${this.accessToken}`,
            };
            window.open(map[type]);
            return false;
        },
    },
};
</script>

<style lang="scss">
	$border-color: $--border-color-light;
    footer.register-footer {
        box-sizing: border-box;
        width: 100%;
        height: 35px;
        // line-height: 35px;
        padding-top: 10px;
        // padding-bottom: 15px;
        border-top: 1px solid $border-color;
        background-color: $--background-color-base;
        ul {
            display: block;
            width: 100%;
            text-align: center;
            white-space: nowrap;
            overflow: hidden;
            li {
                display: inline-block;
                font-size: 12px;
                color: $--color-text-regular;
                cursor: pointer;
                i {
                    display: inline-block;
                    margin: 0 5px;
                    color: $text-color-light;
                }

                span:last-child {
                    color: $--color-text-secondary;
                }

                i:last-child {
                    margin: 0 10px;
                }
                &.lang-switch-btn {
                    span {
                        font-size: 12px;
                        color: $--color-text-secondary
                    }
                    i.el-icon-ssq-diqiu {
                        margin: 0;
                        padding-right: 5px;
                        vertical-align: bottom;
                    }
                }
            }
        }
    }

</style>
