<!-- 定制二级域名展示的特定footer -->
<template>
    <footer class="custom-footer">
        <ul class="clear font-size-zero">
            <li><img src="./img/customFooterLogo.png" width="142" :alt="$t('commonFooter.ssqLogo')"></li>
            <li>
                <span><!-- 电子签约服务由 -->{{ $t('commonFooter.provideTip') }}<strong><!-- 上上签 -->{{ $t('commonFooter.ssq') }}</strong><!-- 提供 -->{{ $t('commonFooter.provide') }}</span>
                <i>|</i>
            </li>
            <li v-if="isCanSwitchLanguage" class="lang-switch-btn">
                <LangSwitch :cacheOnly="langCacheOnly"></LangSwitch>
                <i>|</i>
            </li>
            <li v-if="lang === 'zh'">
                <span><!-- 签约服务热线 -->{{ $t('commonFooter.signHotline') }}：400-993-6665</span>
                <i>|</i>
            </li>
            <li>
                <span>{{ $t('commonFooter.record') }}</span>
            </li>
        </ul>
    </footer>
</template>
<script>
import LangSwitch from 'pub-businessComponents/langSwitch';
import { mapState } from 'vuex';

export default {
    components: {
        LangSwitch,
    },
    props: {
        langCacheOnly: {
            default: false,
            type: Boolean,
        },
    },
    computed: {
        ...mapState(['features']),
        isCanSwitchLanguage() { // 是否可以切换语言
            return true;
            // return this.features.includes('180');
        },
        lang() {
            return this.$i18n.locale;
        },
    },
};
</script>
<style lang="scss">
$border-color: $--border-color-light;
footer.custom-footer {
    box-sizing: border-box;
    width: 100%;
    height: 35px;
    padding-top: 7px;
    border-top: 1px solid $border-color;
    background-color: $--background-color-base;
    ul {
        display: block;
        width: 100%;
        text-align: center;
        white-space: nowrap;
        overflow: hidden;
        li {
            display: inline-block;
            vertical-align: text-bottom;
            font-size: 12px;
            color: $--color-text-regular;
            img{
                margin-right: 15px;
                vertical-align: middle;
            }
            i {
                display: inline-block;
                margin: 0 10px;
                color: $text-color-light;
            }
            strong{
                color: $--color-text-primary;
            }
            &.lang-switch-btn {
                span {
                    font-size: 12px;
                    color: $--color-text-secondary
                }
                i.el-icon-ssq-diqiu {
                    margin: 0;
                    padding-right: 5px;
                    vertical-align: bottom;
                }
            }
        }
    }
}
</style>
