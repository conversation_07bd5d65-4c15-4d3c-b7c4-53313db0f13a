<template>
    <div class="auto-seal-effective">
        <!-- 开启自动盖章 -->
        <el-dialog
            :title="`${$t('autoSeal.openAutoSeal')}-${sealName}`"
            width="560px"
            :visible.sync="visible"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
            class="auto-seal-effective-dialog"
        >
            <div class="margin-box">{{ $t('autoSeal.openAutoTip',{name:sealName}) }}</div>
            <template v-if="effectList && effectList.length>0">
                <div v-for="(item,index) in effectList" :key="index" class="account-item">
                    {{ item.empName }} <span>({{ item.account }})</span>
                </div>
            </template>
            <div class="jump-box margin-box" @click="isShowStep = !isShowStep">{{ $t('autoSeal.howToOpenTip') }}</div>
            <div v-if="isShowStep" class="step3-box">
                <div class="tip">{{ $t('autoSeal.step1Tip',{name:sealName}) }}</div>
                <img v-if="isShowStepPic" src="~pub-images/sealExample/auto_seal_step1.png" alt="" width="100%">
                <div class="tip">{{ $t('autoSeal.step2Tip') }}“<span class="jump-box bottom-border">{{ $t('autoSeal.step2Tip1') }}</span>”，{{ $t('autoSeal.step2Tip2') }}</div>
                <img v-if="isShowStepPic" src="~pub-images/sealExample/auto_seal_step2.png" alt="" width="100%">
            </div>

            <div slot="footer">
                <el-button
                    type="primary"
                    @click="handleClose"
                >{{ $t('autoSeal.iKnow') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>

export default {
    components: {
    },
    props: {

        // 个人自动签章的id
        sigId: {
            default: '',
            type: String,
        },
        sealName: {
            default: '',
            type: String,
        },
        visible: {
            default: false,
            type: Boolean,
        },
        sealId: {
            default: '',
            type: String,
        },
        entId: {
            default: '',
            type: String,
        },
        isFromConsole: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            isShowStep: false,
            effectList: [
            ],
        };
    },
    computed: {
        isShowStepPic() {
            return this.$i18n.locale === 'zh';
        },
    },
    methods: {
        handleGoUserCenter() {
            this.$router.push('/usercenter/seal');
        },
        handlePreviousStepClick() {
            this.$emit('previousStepClick');
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
    created() {
        // 企业是否在乐高城配置过
        this.$http.get(`/ents/seal/${this.sealId}/auto-sign/enabled/employees`)
            .then((res) => {
                this.effectList = res.data || [];
            })
            .catch(() => {});
    },
};
</script>
<style lang="scss">
.auto-seal-effective-dialog {
    .el-dialog__title{
        max-width: 80% !important;
    }
    .el-steps{
        margin-bottom: 20px;
        .is-success{
            .el-step__icon.is-text{
                color: #00AA64;
                border-color: #00AA64;
            }
        }
        .is-process{
            border-color: initial !important;
            background-color: initial !important;
            border-width: initial !important;
            border-style: initial !important;
            .el-step__icon-inner::after{
                display: none;
            }
        }
        .is-wait{
            background-color: initial !important;
        }
        .is-finish{
            background-color: initial !important;
        }
    }
    .step3-box{
        color: $--color-text-primary;
        font-size: 14px;
        .tip{
            margin: 5px 0;
        }
    }
    .margin-box{
        margin: 15px 0;
    }
    .jump-box{
        color: $--color-primary;
        cursor: pointer;
        font-size: 500;
    }
    .bottom-border{
        border-bottom: 1px solid $--color-primary;
    }
    .account-item{
        padding: 5px 15px;
        color: $--color-text-regular;
        font-size: 13px;
    }
    .el-dialog__header {
        height: 50px !important;
    }
    .el-dialog__body {
        padding-bottom: 0px;
    }
    .el-dialog__footer {
        text-align: center;
        padding-top: 10px !important;
    }
}
.en-page .auto-seal-effective-dialog{
    .el-dialog--tiny {
        width: 506px;
    }
}
</style>
