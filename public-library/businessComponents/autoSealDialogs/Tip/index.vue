<template>
    <!--开启自动盖章成功后提示弹窗-->
    <el-dialog class="auto-seal-dialog-tip" :visible.sync="visible" :center="true" :show-close="false" :title="$t('autoSeal.tip')" width="450px">
        <p>{{ $t('autoSeal.autoSealDialogTip.1') }}</p>
        <p>{{ $t('autoSeal.autoSealDialogTip.2') }}</p>
        <p>* {{ $t('autoSeal.autoSealDialogTip.3') }}</p>
        <p>* {{ $t('autoSeal.autoSealDialogTip.4') }}</p>
        <el-radio-group v-model="isSync">
            <el-radio :label="true">{{ $t('autoSeal.yes') }}</el-radio>
            <el-radio :label="false">{{ $t('autoSeal.no') }}</el-radio>
        </el-radio-group>
        <span slot="footer" class="dialog-footer">
            <el-button type="primary" @click="handleConfirm"><p>{{ $t('autoSeal.iKnow') }}</p></el-button>
        </span>
    </el-dialog>
</template>
<style scoped>
</style>
<script>
export default {
    props: {
        visible: {
            default: false,
            type: Boolean,
        },
        pmsAuthSign: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isSync: false,
        };
    },
    methods: {
        handleConfirm() {
            if (this.isSync) {
                this.$http.post('/contract-api/contracts/fetch-contracts-and-sign');
            }
            this.handleClose();
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
    },
};
</script>

<style lang="scss">
.auto-seal-dialog-tip{
    p{
        margin: 6px 0;
    }
}
</style>