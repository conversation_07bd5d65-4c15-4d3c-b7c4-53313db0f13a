<template>
    <div class="scene-config-module">
        <h4 class="scene-title">{{ titleMap }}</h4>
        <div class="scene-item">
            <el-checkbox class="scene-item__checkbox"
                v-model="conditions.enable"
            >
                {{ branchTextObj(conditions).label }}</el-checkbox>
            <div class="add-item-class" v-if="conditionType!='receiverEntId'">
                <ul v-if="conditions.enable && conditions.data.length" class="scene-item__branches">
                    <li v-for="(branch, bIndex) in conditions.data" :key="branch.data" class="scene-item__branch">

                        <span>{{ branch }}</span>
                        <i class="el-icon-ssq--bs-guanbi" @click="deleteBranch(bIndex)"></i>
                    </li>
                </ul>
                <div class="scene-item__branch-add" v-if="conditions.enable">
                    <el-input
                        :placeholder="branchTextObj(conditions).placeholder"
                        v-model="conditions.newBranchValue"
                        clearable
                        @input="handleValueChange"
                        @blur="validateValue"
                    ></el-input>
                    <span class="add-btn" @click="addBranch(conditions)">
                        <i class="el-icon-ssq--bs-jia"></i>{{ $t('autoSeal.add') }}</span>
                    <p class="error-tip" v-if="conditions.showErrorTip">
                        {{ branchTextObj(conditions).errorTip }}
                    </p>
                </div>
                <p class="scene-item__note-tip" v-if="!conditions.enable && branchTextObj(conditions).tips">{{
                    branchTextObj(conditions).tips }}</p>
            </div>
            <div v-else-if="conditions.enable" class="choose-item-class">
                <el-checkbox-group
                    v-model="conditions.data"
                >
                    <p v-for="entInfo in conditions.sources" :key="entInfo.entId">
                        <el-checkbox :label="entInfo.entId">{{ entInfo.entName }}</el-checkbox>
                    </p>
                </el-checkbox-group>
            </div>

        </div>
    </div>
</template>

<script>
import resRules from 'pub-utils/regs.js';
export default {
    name: 'SceneModule',
    props: {
        conditions: {
            type: Object,
            default: () => ({}),
        },
        conditionType: {
            default: '',
            type: String,
        },
        canOpt: {
            default: true,
            type: Boolean,
        },

    },
    data() {
        return {
            LINK_TYPES: ['contractTemplateId'],
        };
    },
    computed: {
        branchTextObj() {
            return () => {
                const LABEL_MAP = {
                    'DEFAULT': {
                        label: this.$t('autoSeal.senderEnable'),
                    },
                    'senderEntName': {
                        label: this.$t('autoSeal.senderEnt'),
                        placeholder: this.$t('autoSeal.inputEntNamePlaceholder'),
                        errorTip: this.$t('commonHeader.plzEnterRightEnt'),
                    },
                    'receiverEntId': {
                        label: this.$t('autoSeal.receiveEnt'),
                        placeholder: this.$t('autoSeal.inputEntNamePlaceholder'),
                        errorTip: this.$t('commonHeader.plzEnterRightEnt'),
                    },
                    'senderAccount': {
                        label: this.$t('autoSeal.senderAccountEnable'),
                        placeholder: this.$t('autoSeal.inputAccountPlaceholder'),
                        errorTip: this.$t('autoSeal.phoneOrEamilTip'),
                        tips: this.$t('autoSeal.unpointAccountTips'),
                    },
                    'contractTemplateId': {
                        label: this.$t('autoSeal.templateIdEnable'),
                        placeholder: this.$t('autoSeal.inputTemplateIdPlaceholder'),
                        errorTip: this.$t('autoSeal.inputTemplateIdTips'),
                        tips: this.$t('autoSeal.unpointContractContentTips'),
                    },
                };
                return LABEL_MAP[this.conditionType];
            };
        },
        titleMap() {
            const TITLE_MAP = {
                'default': this.$t('autoSeal.defaultText'),
                'senderEntName': this.$t('autoSeal.enterprise'),
                'senderAccount': this.$t('autoSeal.account'),
                'contractTemplateId': this.$t('autoSeal.contractContent'),
            };
            return TITLE_MAP[this.conditionType];
        },
        canAddBranch() {
            return () => {
                return this.conditions.enable;
            };
        },
    },
    methods: {
        handleValueChange() {
            this.$set(this.conditions, 'showErrorTip', false);
        },
        validateValue() {
            const item = this.conditions;
            if (!item.newBranchValue) {
                return this.$set(item, 'showErrorTip', false);
            }
            let validateResult = false;
            switch (this.conditionType) {
                case 'senderEntName':
                    validateResult = !resRules.companyName.test(item.newBranchValue);
                    break;
                case 'senderAccount':
                    validateResult = !resRules.userEmail.test(item.newBranchValue) && !resRules.userPhone.test(item.newBranchValue);
                    break;
                case 'SPECIFIED_CONTRACT_ID':
                case 'contractTemplateId':
                    validateResult = !resRules.contractId.test(item.newBranchValue);
                    break;
            }

            this.$set(item, 'showErrorTip', validateResult);
        },
        deleteBranch(branchIndex) {
            this.conditions.data.splice(branchIndex, 1);
        },
        async addBranch(item) {
            if (!item.newBranchValue) {
                return;
            }
            if (item.showErrorTip) {
                return;
            }
            if (this.conditions.data.length > 9) {
                return this.$MessageToast.info(this.$t('autoSeal.maxAddLimit'));
            }
            if (this.LINK_TYPES.includes(this.conditionType)) {
                await this.getIdIsAvaliable(item);
            }
            this.conditions.data.push(item.newBranchValue);
            item.newBranchValue = '';
        },
        getIdIsAvaliable({ newBranchValue }) {
            const queryStr = `if-exist-template?templateId=${newBranchValue}`;
            const url = `/ents/seal/auto-sign/${queryStr}`;
            return this.$http.get(url);
        },
        jumpToDetail(contractId) {
            window.open(`/sign-flow/doc-manage/detail/${contractId}`);
        },
    },
    created() {
    },
};
</script>

<style lang="scss">
.scene-config-module {
    padding-top: 10px;
    .scene-title {
        color: $--color-text-secondary;
        line-height: 20px;
    }
    .scene-item {
        display: block;
        line-height: 30px;
        padding-left: 10px;
        &__checkbox .el-checkbox__label {
            font-size: 12px;
            color: $--color-text-primary;
        }
        &__branches {
            list-style: disc;
            padding-left: 37px;
            .scene-item__branch {
                font-size: 14px;
                span {
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: bottom;
                    &.active:hover {
                        color: $--color-primary;
                        cursor: pointer;
                    }
                }
                i {
                    display: none;
                    margin-left: 20px;
                    cursor: pointer;
                    color: $--color-primary;
                }
                &:hover i {
                    display: inline-block;
                }
            }
        }
        &__branch-add {
            padding-left: 20px;
            .el-input {
                width: 270px;
                &.error-input input {
                    border-color: $--color-danger;
                }
            }
            .add-btn {
                cursor: pointer;
                margin-left: 10px;
                color: $--color-primary;
            }
            .error-tip {
                color: $--color-danger;
                line-height: 16px;
            }
        }
        &__note-tip {
            line-height: 14px;
            color: $--color-text-secondary;
        }
    }
    .choose-item-class{
        margin-left: 25px;
         .el-checkbox{
             .el-checkbox__label{
                max-width: 350px;
                overflow: hidden;
                white-space: nowrap;
                text-overflow: ellipsis;
                vertical-align: middle;
                padding-bottom: 2px
             }
         }

    }
}
</style>
