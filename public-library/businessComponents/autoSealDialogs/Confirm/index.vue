<template>
    <div class="auto-seal-confirm">
        <!-- 开启自动盖章 -->
        <el-dialog
            :title="$t('autoSeal.tip')"
            width="560px"
            :visible.sync="visible"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
            class="auto-seal-confirm-dialog"
        >
            <el-steps v-if="isFromConsole" :active="active" align-center>
                <el-step :title="$t('autoSeal.manageConfig')"></el-step>
                <el-step :title="$t('autoSeal.codeVerify')"></el-step>
                <el-step :title="$t('autoSeal.signerConfig')"></el-step>
            </el-steps>
            <div v-if="active === 1">
                <h2 class="highlight-tip">
                    {{ $t('autoSeal.specialConfirmTip') }}
                </h2>
                <h4 class="tip-content">
                    <p>{{ $t('autoSeal.warningTip.title') }}</p>
                    <p>{{ $t('autoSeal.warningTip.tip1') }}</p>
                    <p>{{ $t('autoSeal.warningTip.tip2') }}</p>
                </h4>
                <h4 class="tip-content">
                    <p>{{ $t('autoSeal.suggestionsTip.title') }}</p>
                    <p>{{ $t('autoSeal.suggestionsTip.tip1') }}</p>
                    <p>{{ $t('autoSeal.suggestionsTip.tip2') }}</p>
                </h4>
                <h4 class="tip-content">
                    <el-checkbox v-model="hasReadAndAgree"> {{ $t('autoSeal.hasReadAndAccept') }}</el-checkbox>
                </h4>
                <el-form :model="openAutoSealData" :rules="openAutoSealData.rule" label-position="right" label-width="75px">
                    <template v-if="SIGN_SECOND_CHECK === true">
                        <el-form-item :label="$t('autoSeal.signPsw')" prop="signPass">
                            <el-input v-model="openAutoSealData.signPass"
                                :placeholder="$t('autoSeal.inputPwd6')"
                                :maxlength="6"
                                size="small"
                                type="password"
                                auto-complete="new-password"
                                name="signPass"
                            ></el-input>
                        </el-form-item>
                        <p class="tr">
                            <span class="highlight" @click="handleForgetSignPass">{{ $t('autoSeal.forgetPsw') }}</span>
                        </p>
                    </template>
                    <el-form-item class="countdown-item"
                        :label="sendType.type == 'E' ? $t('autoSeal.mail') :
                            $t('autoSeal.phone')"
                    >
                        <span class="phone">{{ notice }}</span>
                    </el-form-item>
                    <el-form-item :label="$t('autoSeal.verificationCode')">
                        <div class="verify-flex">
                            <el-input
                                class="verify-input"
                                v-model="verifyCode"
                                :maxlength="6"
                            >
                            </el-input>
                            <CountDown class="countDown"
                                :clickedFn="handleClickSendCode"
                                :disabled="countDownDisabled"
                                ref="btn"
                                :second="60"
                            ></CountDown>
                        </div>
                    </el-form-item>
                    <div class="switch">
                        <span v-if="phoneNotice" class="voiceCodeLabel">{{ $t('autoSeal.msgTip') }}</span>
                        <span v-if="phoneNotice" class="highlight" @click="handleClickVoice">{{ $t('autoSeal.voiceVerCode') }}</span>
                        <span v-if="mailNotice && phoneNotice">
                            {{ $t('autoSeal.or') }}
                            <span v-show="phoneNotice && sendType.type == 'E'" class="highlight" @click="handleClickMailAndPhone">{{ $t('autoSeal.SMSVerCode') }}</span>
                            <span v-show="mailNotice && (sendType.type == 'S' || sendType.type == 'V') " class="highlight" @click="handleClickMailAndPhone">{{ $t('autoSeal.emailVerCode') }}</span>
                        </span>
                    </div>
                    <div class="enable-tips">{{ $t('autoSeal.enableTipText') }}</div>
                </el-form>
            </div>
            <div v-else-if="active === 2" class="step3-box">
                <div class="tip">{{ $t('autoSeal.step1Tip',{name:sealName}) }}</div>
                <img v-if="isShowStepPic" src="~pub-images/sealExample/auto_seal_step1.png" alt="" width="100%">
                <div class="tip">{{ $t('autoSeal.step2Tip') }}“<span class="jump-box">{{ $t('autoSeal.step2Tip1') }}</span>”，{{ $t('autoSeal.step2Tip2') }}</div>
                <img v-if="isShowStepPic" src="~pub-images/sealExample/auto_seal_step2.png" alt="" width="100%">
            </div>
            <div slot="footer">
                <el-button
                    v-if="isFromConsole && active === 1"
                    @click="handlePreviousStepClick"
                >{{ $t('autoSeal.preStep') }}</el-button>
                <el-button
                    v-if="active === 1"
                    type="primary"
                    @click="handleOpenAutoSeal"
                    :disabled="submitDisabled"
                >{{ isFromConsole ? $t('autoSeal.nextStep') : $t('autoSeal.confirm') }}</el-button>
                <el-button
                    v-if="active === 2"
                    type="primary"
                    @click="$emit('openAutoSealSuccess')"
                >{{ $t('autoSeal.done') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import CountDown from 'pub-components/countDown';

export default {
    components: {
        CountDown,
    },
    props: {
        // autoSeal的类型：默认为'', 'personAutoSeal'为个人设置自动签章
        autoSealType: {
            default: '',
            type: String,
        },
        // 个人自动签章的id
        sigId: {
            default: '',
            type: String,
        },
        sealName: {
            default: '',
            type: String,
        },
        visible: {
            default: false,
            type: Boolean,
        },
        sealId: {
            default: '',
            type: String,
        },
        entId: {
            default: '',
            type: String,
        },
        isMineSeal: {
            default: false,
            type: Boolean,
        },
        isFromConsole: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            hasReadAndAgree: false,
            openAutoSealData: {
                show: true,
                signPass: '',
                reason: '',
                rule: {
                    signPass: [
                        { message: this.$t('autoSeal.inputPwd'), trigger: 'blur' },
                        { min: 6, max: 6, message: this.$t('autoSeal.inputNumber6'), trigger: 'blur' },
                    ],
                },
                refuseMaxLength: 30,
            },
            verifyCode: '',
            sendType: {
                type: 'S',
            }, // S：手机短信；E：邮件；V：语音
            notice: '',
            phoneNotice: '',
            mailNotice: '',
            countDownDisabled: false,
            verifyKey: '',
            active: 1,
        };
    },
    computed: {
        ...mapGetters([
            'getUserAccount',
            'SIGN_SECOND_CHECK',
        ]),
        /* 验证码 和 签约密码（若需要）没有完整提供的话，为true */
        submitDisabled() {
            return this.verifyCode.replace(/\s+/g, '').length !== 6 || (this.SIGN_SECOND_CHECK === true && this.openAutoSealData.signPass.replace(/\s+/g, '').length !== 6);
        },
        // 是否是个人自动签章
        isPersonAutoSeal() {
            return this.autoSealType && this.autoSealType === 'personAutoSeal';
        },
        isShowStepPic() {
            return this.$i18n.locale === 'zh';
        },
    },
    methods: {
        handleGoUserCenter() {
            this.$router.push('/usercenter/seal');
        },
        handlePreviousStepClick() {
            this.$emit('previousStepClick');
        },
        handleClose() {
            this.$emit('update:visible', false);
            if (this.isFromConsole && this.active === 2) {
                this.$emit('openAutoSealSuccess');
            }
        },
        handleOpenAutoSeal() {
            if (!this.verifyKey) {
                return this.$MessageToast.error(this.$t('autoSeal.verCodeInputErr')); // 请先获取验证码
            }
            if (this.isPersonAutoSeal) {
                this.handleOpenPersonAutoSeal();
                return;
            }
            const url = this.isMineSeal ? `/ents/seal/${this.sealId}/auto-sign/enable-auto-sign`
                : `/ents/seal/${this.sealId}/auto-sign/enable-auto-switch`;
            this.$http.post(url, {
                // signPass: this.openAutoSealData.signPass,
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
                entId: this.entId,
                notification: this.notice,
            }).then((res) => {
                if (res.data && res.data.verifyResult) {
                    if (this.isFromConsole) {
                        this.active = 2;
                    } else {
                        this.$emit('openAutoSealSuccess');
                    }
                } else {
                    this.$MessageToast.error(this.$t('autoSeal.verCodeFail'));
                }
            });
        },
        // 开启个人自动签章
        handleOpenPersonAutoSeal() {
            const url = `/users/signatures/${this.sigId}/enable-auto-sign`;
            this.$http.post(url, {
                verifyCode: this.verifyCode,
                verifyKey: this.verifyKey,
                notification: this.notice,
            }).then(() => {
                this.$emit('openAutoSealSuccess');
            });
        },
        send() {
            this.countDownDisabled = true;
            setTimeout(this.sended, 0);
            // sendVerCode
            this.$http.sendVerCode({
                code: 'B011',
                sendType: this.sendType.type,
                target: this.notice,
                // bizTargetKey: this.$route.query.contractId // contractId
            })
                .then((res) => {
                    this.verifyKey = res.data.value;
                    this.$MessageToast.success(this.$t('autoSeal.SentSuccessfully'));
                })
                .catch(() => {
                    this.$refs.btn.reset();
                });
        },
        sended() {
            this.$refs.btn.run();
            this.countDownDisabled = false;
        },
        handleClickSendCode() {
            if (!this.hasReadAndAgree) {
                return this.$MessageToast.error(this.$t('autoSeal.acceptTipFirst'));
            }
            this.send();
        },
        handleClickVoice() {
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('autoSeal.sendInternalErr'));
                return;
            }
            this.sendType = { type: 'V' };
            this.notice = this.phoneNotice.code;
        },
        handleClickMailAndPhone() {
            if (this.$refs.btn.time > 0) {
                this.$MessageToast.error(this.$t('autoSeal.sendInternalErr'));
                return;
            }
            if (this.sendType.type === 'E') {
                this.sendType = { type: 'S' };
                this.notice = this.phoneNotice.code;
            } else {
                this.sendType = { type: 'E' };
                this.notice = this.mailNotice.code;
            }
        },
        // 忘记签约密码
        handleForgetSignPass() {
            window.localStorage && window.localStorage.setItem('ForgetPassReturnSignHref', '/' +  this.$router.options.base + this.$route.fullPath);
            this.$router.push(`${this.GLOBAL.rootPathName}/resetSignPassword?userAccount=${this.getUserAccount}`);
        },
    },
    beforeMount() {
        this.$http.get('/users/notifications')
            .then(res => {
                this.mailNotice = res.data.filter(item => {
                    return +item.type === 1;
                })[0] || '';
                this.phoneNotice = res.data.filter(item => {
                    return +item.type === 2;
                })[0] || '';
                this.notice = this.phoneNotice ? this.phoneNotice.code : this.mailNotice.code;
                this.sendType = this.phoneNotice ? { type: 'S' } : { type: 'E' };
            })
            .then(() => {
                this.$watch('sendType', function() {
                    if (this.$refs.btn.time === 0) {
                        this.send();
                    }
                });
            });
    },
};
</script>
<style lang="scss">
.auto-seal-confirm-dialog {
    .el-steps{
        margin-bottom: 20px;
        .is-success{
            .el-step__icon.is-text{
                color: #00AA64;
                border-color: #00AA64;
            }
        }
        .is-process{
            border-color: initial !important;
            background-color: initial !important;
            border-width: initial !important;
            border-style: initial !important;
            .el-step__icon-inner::after{
                display: none;
            }
        }
        .is-wait{
            background-color: initial !important;
        }
        .is-finish{
            background-color: initial !important;
        }
    }
    .step3-box{
        color: $--color-text-primary;
        font-size: 14px;
        .tip{
            margin: 5px 0;
        }
        .jump-box{
            color: $--color-primary;
            cursor: pointer;
            font-size: 500;
            border-bottom: 1px solid $--color-primary;
        }
    }

    .el-dialog__header {
        height: 50px !important;
    }
    .el-dialog__body {
        padding-bottom: 0px;
        .highlight-tip {
            font-weight: 500;
            font-size: 12px;
            margin-bottom: 15px;
        }
        .tip-content {
            font-size: 12px;
            line-height: 20px;
            margin-bottom: 15px;
            .el-checkbox__label {
                font-size: 12px;
                padding-left: 5px;
            }
        }
    }
    .el-dialog__footer {
        text-align: center;
        padding-top: 10px !important;
    }
    .el-form {
        .el-form-item{
            width: 350px;
            margin: 0 auto;
            [dir=rtl] & {
                width: 420px;
                &.countdown-item .el-form-item__label{
                    width: 120px !important;
                }
                .el-form-item__content {
                    margin-left: 0 !important;
                    margin-right: 120px !important;
                }
            }
        }

        .phone{
            color: $--color-text-regular;
            font-size: 14px;
        }
        .switch {
            margin-top: -5px;
            margin-bottom: 10px;
            margin-right: 70px;
            font-size: 12px;
            color:$--color-text-secondary;
        }
        .enable-tips {
            text-align: center;
            font-size: 12px;
        }
        .highlight {
            font-size: 12px;
            color:  $--color-primary;
            cursor: pointer;
        }
        .openAutoSeale-description {
            margin-bottom: 10px;
            font-size: 12px;
            line-height: 18px;
            color: $--color-text-regular;
        }
        .verify-flex {
            position: relative;
            display: block;
            line-height: 28px;
            font-size: 0;
            .verify-input {
                line-height: 28px;
                font-size: 0;
                input {
                    height: 28px;
                    font-size: 12px;
                }
            }
            .countDown {
                position: absolute;
                top: 0;
                right: 0;
                font-size: 12px;
                width: 92px;
                height: 28px;
                line-height: 28px;
                border: 1px solid $--color-text-placeholder;
                border-radius: 1px;
                color: $--color-text-primary;
                [dir=rtl] & {
                    width: 130px;
                    right: auto;
                    left: 0;
                }
            }
        }
    }
}
.en-page .auto-seal-confirm-dialog{
    .el-dialog--tiny {
        width: 506px;
    }
    .el-form {
        .el-form-item{
            margin-bottom: 0;
            .el-form-item__label{
                width: 113px !important;
            }
            .el-form-item__content{
                margin-left: 116px !important;
            }
        }
        .phone{
            color: $--color-text-regular;
            font-size: 14px;
        }
    }
}
</style>
