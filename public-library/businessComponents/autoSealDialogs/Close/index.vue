<template>
    <div class="closeAutoSealDialog-con">
        <!-- 关闭自动盖章 -->
        <el-dialog
            :title="$t('autoSeal.closeAutoSeal')"
            width="450px"
            :visible.sync="visible"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
            class="el-dialog-bg closeAutoSealDialog"
        >
            <div>{{ $t('autoSeal.closeAutoSealTip') }}</div>
            <div slot="footer">
                <el-button
                    type="primary"
                    @click="handlecloseAutoSeal"
                >{{ $t('autoSeal.confirm') }}</el-button>
                <el-button
                    @click="handleClose"
                >{{ $t('autoSeal.cancel') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>

export default {
    props: {
        // autoSeal的类型：默认为'', 'personAutoSeal'为个人设置自动签章
        autoSealType: {
            default: '',
            type: String,
        },
        // 个人自动签章的id
        sigId: {
            default: '',
            type: String,
        },
        visible: {
            default: false,
            type: Boolean,
        },
        sealId: {
            default: '',
            type: String,
        },
        entId: {
            default: '',
            type: String,
        },
    },
    data() {
        return {

        };
    },
    computed: {
        // 是否是个人自动签章
        isPersonAutoSeal() {
            return this.autoSealType && this.autoSealType === 'personAutoSeal';
        },
    },
    methods: {
        handleClose() {
            this.$emit('update:visible', false);
        },
        handlecloseAutoSeal() {
            if (this.isPersonAutoSeal) {
                // 个人自动签章关闭
                this.$http.put(`/users/signatures/${this.sigId}/disable-auto-sign`).then(() => {
                    this.$emit('closeAutoSealSuccess');
                });
            } else {
                this.$http.post(`/ents/autoSign/${this.sealId}`, {
                    autoSign: false,
                    entId: this.entId,
                }).then(() => {
                    this.$emit('closeAutoSealSuccess');
                });
            }
        },
    },
};
</script>
