<template>
    <div class="scene-config-module">
        <h4 class="scene-title">{{ titleMap }}</h4>
        <div class="scene-item"
            v-for="(item, index) in conditions"
            :key="index"
        >
            <template v-if="canOpt">
                <el-checkbox class="scene-item__checkbox"
                    v-model="item.enable"
                    :disabled="item.disabled"
                    @change="handleConditionCheckboxChanged(index, $event)"
                >
                    {{ branchTextObj(item.type).label }}
                </el-checkbox>
                <div v-if="isGroup" class="group-config">
                    <el-checkbox class="scene-item__checkbox"
                        v-if="item.type === 'SENDER_IN_THE_ENTERPRISE'"
                        v-model="item.content[0].includeGroupMember"
                        :disabled="item.disabled"
                        @change="handleIncludeSubCompanyCheckboxChanged(index, $event)"
                    >
                        {{ $t('autoSeal.includeGroupMemeber') }}
                    </el-checkbox>
                </div>
            </template>
            <el-checkbox class="scene-item__checkbox"
                v-else
                :value="item.enable"
                disabled
            >
                {{ branchTextObj(item.type).label }}</el-checkbox>
            <ul v-if="item.enable && item.content.length && item.type !== 'SENDER_IN_THE_ENTERPRISE'" class="scene-item__branches">
                <li v-for="(branch, bIndex) in item.content" :key="branch.data" class="scene-item__branch">
                    <span
                        v-if="item.type === 'SPECIFIED_CONTRACT_ID' && branch.effective"
                        class="active"
                        @click="jumpToDetail(branch.data)"
                    >
                        {{ branch.data }}</span>
                    <span v-else>{{ branch.data }}</span>
                    <span v-if="!branch.effective">（已失效）</span>
                    <i v-if="canOpt" class="el-icon-ssq--bs-guanbi" @click="deleteBranch(bIndex, index)"></i>
                </li>
            </ul>
            <div v-if="!canOpt && isGroup" class="group-config">
                <el-checkbox class="scene-item__checkbox"
                    v-if="item.type === 'SENDER_IN_THE_ENTERPRISE' && item.content.length"
                    v-model="item.content[0].includeGroupMember"
                    disabled
                >
                    {{ $t('autoSeal.includeGroupMemeber') }}
                </el-checkbox>
            </div>
            <div class="scene-item__branch-add" v-if="canAddBranch(item)">
                <el-input :class="item.showErrorTip ? 'error-input' : ''"
                    :placeholder="branchTextObj(item.type).placeholder"
                    v-model.trim="item.newBranchValue"
                    clearable
                    @input="handleValueChange(index)"
                    @blur="validateValue(index)"
                ></el-input>
                <span class="add-btn" @click="addBranch(index, item)">
                    <i class="el-icon-ssq--bs-jia"></i>{{ $t('autoSeal.add') }}{{ item.showTip }}</span>
                <p class="error-tip" v-if="item.showErrorTip">
                    {{ branchTextObj(item.type).errorTip }}
                </p>
            </div>
            <p class="scene-item__note-tip" v-if="canOpt && !item.enable && branchTextObj(item.type).tips">{{
                branchTextObj(item.type).tips }}</p>
        </div>
    </div>
</template>

<script>
import resRules from 'pub-utils/regs.js';
export default {
    name: 'SceneModule',
    props: {
        conditions: {
            type: Array,
            default: () => ([]),
        },
        conditionType: {
            default: '',
            type: String,
        },
        canOpt: {
            default: true,
            type: Boolean,
        },
    },
    data() {
        return {
            newValue: '',
            LINK_TYPES: ['SPECIFIED_CONTRACT_ID', 'SPECIFIED_CONTRACT_TEMPLATE_ID'],
            isInitData: false,
        };
    },
    computed: {
        isGroup() {
            return this.$store.state.commonHeaderInfo.groupVersion;
        },
        branchTextObj() {
            return (type) => {
                const LABEL_MAP = {
                    'DEFAULT': {
                        label: this.$t('autoSeal.senderEnable'),
                    },
                    'SENDER_IN_THE_ENTERPRISE': {
                        label: this.$t('autoSeal.selfEntEnable'),
                    },
                    'SPECIFIED_SENDER_ENTERPRISE': {
                        label: this.$t('autoSeal.senderEntEnable'),
                        placeholder: this.$t('autoSeal.inputEntNamePlaceholder'),
                        errorTip: this.$t('commonHeader.plzEnterRightEnt'),
                    },
                    'SPECIFIED_SENDER_ACCOUNT': {
                        label: this.$t('autoSeal.senderAccountEnable'),
                        placeholder: this.$t('autoSeal.inputAccountPlaceholder'),
                        errorTip: this.$t('autoSeal.phoneOrEamilTip'),
                        tips: this.$t('autoSeal.unpointAccountTips'),
                    },
                    'SPECIFIED_CONTRACT_ID': {
                        label: this.$t('autoSeal.contractIdEnable'),
                        placeholder: this.$t('autoSeal.inputContractIdPlaceholder'),
                        errorTip: this.$t('autoSeal.inputContractIdTips'),
                    },
                    'SPECIFIED_CONTRACT_TEMPLATE_ID': {
                        label: this.$t('autoSeal.templateIdEnable'),
                        placeholder: this.$t('autoSeal.inputTemplateIdPlaceholder'),
                        errorTip: this.$t('autoSeal.inputTemplateIdTips'),
                        tips: this.$t('autoSeal.unpointContractContentTips'),
                    },
                };
                return LABEL_MAP[type];
            };
        },
        titleMap() {
            const TITLE_MAP = {
                'default': this.$t('autoSeal.defaultText'),
                'accountConditions': this.$t('autoSeal.account'),
                'contractContentConditions': this.$t('autoSeal.contractContent'),
                'enterpriseConditions': this.$t('autoSeal.enterprise'),
            };
            return TITLE_MAP[this.conditionType];
        },
        canAddBranch() {
            return (item) => {
                return this.canOpt && item.enable && item.type.includes('SPECIFIED');
            };
        },
    },
    watch: {
        conditions: {
            handler(val) {
                if (val && this.isGroup && !this.isInitData) {
                    this.isInitData = true;
                    const sender = val.find(e => e.type === 'SENDER_IN_THE_ENTERPRISE');
                    if (sender && !sender.content.length) {
                        sender.content = [{ includeGroupMember: false }];
                    }
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        handleConditionCheckboxChanged(index, val) {
            const item = this.conditions[index];
            if (item && item.type === 'SENDER_IN_THE_ENTERPRISE' && this.isGroup && !val) {
                this.conditions[index].content[0].includeGroupMember = false;
            }
            this.$set(this.conditions[index], 'enable', val);
        },
        handleIncludeSubCompanyCheckboxChanged(index, val) {
            this.conditions.forEach(e => {
                if (e.type === 'SENDER_IN_THE_ENTERPRISE' && this.isGroup) {
                    if (val) {
                        e.enable = true;
                    }
                }
            });
        },
        handleValueChange(index) {
            this.$set(this.conditions[index], 'showErrorTip', false);
        },
        validateValue(index) {
            const item = this.conditions[index];
            if (!item.newBranchValue) {
                return this.$set(item, 'showErrorTip', false);
            }
            let validateResult = false;
            switch (item.type) {
                case 'SPECIFIED_SENDER_ENTERPRISE':
                    validateResult = !resRules.companyName.test(item.newBranchValue);
                    break;
                case 'SPECIFIED_SENDER_ACCOUNT':
                    validateResult = !resRules.userEmail.test(item.newBranchValue) && !resRules.userPhone.test(item.newBranchValue);
                    break;
                case 'SPECIFIED_CONTRACT_ID':
                case 'SPECIFIED_CONTRACT_TEMPLATE_ID':
                    validateResult = !resRules.contractId.test(item.newBranchValue);
                    break;
            }

            this.$set(item, 'showErrorTip', validateResult);
        },
        deleteBranch(branchIndex, conditionIndex) {
            this.conditions[conditionIndex].content.splice(branchIndex, 1);
        },
        async addBranch(index, item) {
            if (!item.newBranchValue) {
                return;
            }
            if (item.showErrorTip) {
                return;
            }
            if (this.conditions[index].content.length > 9) {
                return this.$MessageToast.info(this.$t('autoSeal.maxAddLimit'));
            }
            let effective = true;
            if (this.LINK_TYPES.includes(item.type)) {
                const resp = await this.getIdIsAvaliable(item);
                effective = item.type === 'SPECIFIED_CONTRACT_ID' ? resp.data.verfiyResult : true;
            }
            this.conditions[index].content.push({
                data: item.newBranchValue,
                effective,
            });
            item.newBranchValue = '';
        },
        getIdIsAvaliable({ type, newBranchValue }) {
            const queryStr = type === 'SPECIFIED_CONTRACT_ID'
                ? `specified-contract-verify?contractId=${newBranchValue}`
                : `if-exist-template?templateId=${newBranchValue}`;
            const url = `/ents/seal/auto-sign/${queryStr}`;
            return this.$http.get(url);
        },
        jumpToDetail(contractId) {
            window.open(`/sign-flow/doc-manage/detail/${contractId}`);
        },
    },
};
</script>

<style lang="scss">
.scene-config-module {
    padding-top: 10px;
    .scene-title {
        color: $--color-text-secondary;
        line-height: 20px;
    }
    .scene-item {
        display: block;
        line-height: 30px;
        padding-left: 10px;
        [dir=rtl] & {
            padding-right: 10px;
            padding-left: 0;
        }
        &__checkbox .el-checkbox__label {
            font-size: 12px;
            color: $--color-text-primary;
        }
        &__branches {
            list-style: disc;
            padding-left: 37px;
            [dir=rtl] & {
                padding-right: 37px;
                padding-left: 0;
            }
            .scene-item__branch {
                font-size: 14px;
                span {
                    display: inline-block;
                    max-width: 300px;
                    overflow: hidden;
                    white-space: nowrap;
                    text-overflow: ellipsis;
                    vertical-align: bottom;
                    &.active:hover {
                        color: $--color-primary;
                        cursor: pointer;
                    }
                }
                i {
                    display: none;
                    margin-left: 20px;
                    cursor: pointer;
                    color: $--color-primary;
                    [dir=rtl] & {
                        margin-right: 20px;
                        margin-left: 0;
                    }
                }
                &:hover i {
                    display: inline-block;
                }
            }
        }
        &__branch-add {
            padding-left: 20px;
            [dir=rtl] & {
                padding-right: 20px;
                padding-left: 0;
            }
            .el-input {
                width: 270px;
                &.error-input input {
                    border-color: $--color-danger;
                }
            }
            .add-btn {
                cursor: pointer;
                margin-left: 10px;
                color: $--color-primary;
                [dir=rtl] & {
                    margin-right: 10px;
                    margin-left: 0;
                }
            }
            .error-tip {
                color: $--color-danger;
                line-height: 16px;
            }
        }
        &__note-tip {
            line-height: 14px;
            color: $--color-text-secondary;
        }
        .group-config{
            margin-left: 20px;
            [dir=rtl] & {
                margin-right: 20px;
                margin-left: 0;
            }
        }
    }
}
</style>
