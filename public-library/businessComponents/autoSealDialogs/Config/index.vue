<template>
    <div class="auto-seal-config">
        <!-- 开启自动盖章 -->
        <el-dialog
            :title="dialogTitle"
            width="470px"
            :visible.sync="visible"
            :modal-append-to-body="true"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            :before-close="handleClose"
            class="auto-seal-config-dialog"
        >
            <el-steps v-if="isFromConsole" :active="active" finish-status="success" align-center>
                <el-step :title="$t('autoSeal.manageConfig')"></el-step>
                <el-step :title="$t('autoSeal.codeVerify')"></el-step>
                <el-step :title="$t('autoSeal.signerConfig')"></el-step>
            </el-steps>
            <div>
                <h3>{{ isPersonAutoSeal ? $t('autoSeal.autoPersonSignTipTitle'): $t('autoSeal.autoSignTipTitle') }}</h3>
                <!--  默认选中  -->
                <SceneModule
                    :can-opt="canOpt"
                    conditionType="default"
                    :conditions="DEFAULT_SCENE.conditions"
                    :autoSealType="autoSealType"
                ></SceneModule>
                <div v-if="isPersonAutoSeal">
                    <PersonSceneModule
                        v-for="key in Object.keys(configData)"
                        :key="key"
                        :conditionType="key"
                        :conditions="configData[key]"
                        :can-opt="canOpt"
                        :autoSealType="autoSealType"
                    >
                    </PersonSceneModule>
                </div>

                <div v-else>
                    <SceneModule
                        v-for="(val, key) in configData"
                        :key="key"
                        :conditionType="key"
                        :conditions="val"
                        :can-opt="canOpt"
                        :autoSealType="autoSealType"
                    >
                    </SceneModule>
                </div>
            </div>

            <div slot="footer">
                <template v-if="canOpt">
                    <el-button
                        @click="handleClose"
                    >{{ $t('autoSeal.cancel') }}</el-button>
                    <el-button
                        type="primary"
                        @click="handleSaveConfig"
                    >{{ $t('autoSeal.nextStep') }}</el-button>
                </template>
                <el-button
                    v-else
                    type="primary"
                    @click="handleToNext"
                >{{ $t('autoSeal.confirm') }}</el-button>
            </div>
        </el-dialog>
    </div>
</template>
<script>
import { mapGetters } from 'vuex';
import SceneModule from '../SceneModule';
import PersonSceneModule from '../PersonSceneModule';

export default {
    components: {
        SceneModule,
        PersonSceneModule,
    },
    props: {
        // autoSeal的类型：默认为'', 'personAutoSeal'为个人设置自动签章
        autoSealType: {
            default: '',
            type: String,
        },
        // 个人自动签章的id
        sigId: {
            default: '',
            type: String,
        },
        visible: {
            default: false,
            type: Boolean,
        },
        sealId: {
            default: '',
            type: String,
        },
        entId: {
            default: '',
            type: String,
        },
        sealName: {
            default: '',
            type: String,
        },
        canOpt: {
            type: Boolean,
            default: true,
        },
        configData: {
            default: () => ({}),
            type: Object,
        },
        isFromConsole: {
            default: false,
            type: Boolean,
        },
    },
    data() {
        return {
            DEFAULT_SCENE: {
                conditions: [{
                    type: 'DEFAULT', // 仅前端用于标记类型
                    enable: true,
                    disabled: true,
                    content: [],
                }],
            },
            hasConfigBoss: false,
            active: 0,
        };
    },
    computed: {
        ...mapGetters([
            'getUserAccount',
            'SIGN_SECOND_CHECK',
            'getCurrentEntInfo',
        ]),
        dialogTitle() {
            return this.isPersonAutoSeal ? this.$t('autoSeal.openAutoSeal') : `${this.$t('autoSeal.openAutoSeal')}-${this.sealName}`;
        },
        // 是否是个人自动签章
        isPersonAutoSeal() {
            return this.autoSealType && this.autoSealType === 'personAutoSeal';
        },
    },
    methods: {
        handleToNext() {
            this.$emit('configSuccess');
            this.$emit('update:visible', false);
        },
        handleClose() {
            this.$emit('update:visible', false);
        },
        handleSaveConfig() {
            const flatConfigData = Object.values(this.configData || {}).flat();
            const hasInputNotAdd = flatConfigData.some(item => item.enable && item.newBranchValue);
            if (hasInputNotAdd) {
                return this.$MessageToast.error(this.$t('autoSeal.notClickAddIconTip'));
            }
            if (this.isPersonAutoSeal) {
                this.handlePersonSaveConfig();
                return;
            }
            const necessaryValidate = this.configData.enterpriseConditions.some(condition => {
                // 需要指定本企业发送合同或指定具体发件方企业
                return condition.enable &&
                    (condition.type === 'SENDER_IN_THE_ENTERPRISE' || condition.content.length > 0);
            });
            const hasSelected = this.configData.enterpriseConditions.some(condition => {
                // 已经勾选了“本企业发送合同”
                return condition.enable &&
                    (condition.type === 'SENDER_IN_THE_ENTERPRISE');
            });
            if (!necessaryValidate) {
                return this.$MessageToast.error(this.$t('autoSeal.pointSenderEnt'));
            }
            // 未开启高版本无法勾选的提示
            if (!this.hasConfigBoss && hasSelected) {
                return this.$MessageToast.error(this.$t('autoSeal.versionTip'));
            }

            // “指定发件方企业”的index
            const specifiedEntIndex = this.configData.enterpriseConditions.findIndex(condition => {
                return (condition.type === 'SPECIFIED_SENDER_ENTERPRISE');
            });
            // 勾选发件方企业后需指定具体企业名称
            if (this.configData.enterpriseConditions[specifiedEntIndex].enable &&
                !this.configData.enterpriseConditions[specifiedEntIndex].content.length) {
                return this.$MessageToast.error(this.$t('autoSeal.pointEntName'));
            }

            // 勾选但未指定具体账号
            let selectButEmpty = this.configData.accountConditions[0].enable &&
                !this.configData.accountConditions[0].content.length;

            if (selectButEmpty) {
                return this.$MessageToast.error(this.$t('autoSeal.ponitSenderAccount'));
            }
            if (!selectButEmpty) {
                // 勾选但未指定模板ID
                selectButEmpty = this.configData.contractContentConditions.some(condition => {
                    return condition.enable && !condition.content.length;
                });
            }

            if (selectButEmpty) {
                return this.$MessageToast.error(this.$t('autoSeal.pointContractContent'));
            }
            // 移除前端使用字段
            Object.values(this.configData).forEach(conditions => {
                conditions.forEach(condition => {
                    delete condition.newBranchValue;
                    delete condition.showErrorTip;
                });
            });
            this.$http.post(`/ents/seal/${this.sealId}/auto-sign/configs`, {
                ...this.configData,
            }).then(() => {
                this.$emit('configSuccess');
            });
        },
        // 保存个人自动签章配置
        handlePersonSaveConfig() {
            // 指定发件方企业
            const { enable, data } = this.configData.senderEntName;
            if (!enable) {
                return this.$MessageToast.error(this.$t('autoSeal.pointSenderEnt'));
            }

            // 勾选发件方企业后需指定具体企业名称
            if (!data.length) {
                return this.$MessageToast.error(this.$t('autoSeal.pointEntName'));
            }

            // 接收方企业
            const receiverEntEnable = this.configData.receiverEntId.enable;
            const receiverEntData = this.configData.receiverEntId.data;
            if (!receiverEntEnable) {
                return this.$MessageToast.error(this.$t('autoSeal.pointReceiveEnt'));
            }

            // 勾选接收方企业后需指定具体企业名称
            if (!receiverEntData.length) {
                return this.$MessageToast.error(this.$t('autoSeal.pointReceiveEntName'));
            }

            // 勾选但未指定具体账号
            const senderAccountEnable = this.configData.senderAccount.enable;
            const senderAccountData = this.configData.senderAccount.data;
            const selectAccountButEmpty = senderAccountEnable && !senderAccountData.length;

            if (selectAccountButEmpty) {
                this.$set(this.configData.senderAccount, 'showErrorTip', true);
                return this.$MessageToast.error(this.$t('autoSeal.ponitSenderAccount'));
            }

            // 勾选但未指定合同模版id
            const contractTemplateIdEnable = this.configData.contractTemplateId.enable;
            const contractTemplateIdData = this.configData.contractTemplateId.data;
            const selectTemplateButEmpty = contractTemplateIdEnable && !contractTemplateIdData.length;

            if (selectTemplateButEmpty) {
                this.$set(this.configData.contractTemplateId, 'showErrorTip', true);
                return this.$MessageToast.error(this.$t('autoSeal.pointContractContent'));
            }

            const params = {
                enableSenderEntNames: this.configData.senderEntName.enable, // 指定发件方企业开关
                senderEntNames: this.configData.senderEntName.data.join(','), // 指定发件方企业名称，多个企业名称以逗号分隔，最多10个
                enableReceiverEntIds: this.configData.receiverEntId.enable, // 指定收件方企业开关
                receiverEntIds: this.configData.receiverEntId.data.join(','), // 指定收件方企业ID，多个企业名称以逗号分隔，最多10个
                enableSenderAccounts: this.configData.senderAccount.enable, // 指定发件方账号开关
                senderAccounts: this.configData.senderAccount.data.join(','), // 指定发件方账号，多个账号以逗号分隔，最多10个
                enableContractTemplateIds: this.configData.contractTemplateId.enable, // 指定合同模板ID开关
                contractTemplateIds: this.configData.contractTemplateId.data.join(','), // 指定合同模板ID，多个合同模板ID以逗号分隔，最多10个
            };
            this.$http.post(`/users/signatures/${this.sigId}/auto-sign-condition`, params).then(() => {
                this.$emit('configSuccess');
            });
        },
    },
    created() {
        // 企业是否在乐高城配置过
        this.$http.get(`/ents/is-pms-configured/${this.getCurrentEntInfo.entId}`)
            .then((res) => {
                const { data: { value: hasConfig } } = res;
                this.hasConfigBoss = hasConfig || false;
            })
            .catch(() => {});
    },
};
</script>
<style lang="scss">
.auto-seal-config-dialog .el-dialog {
    .el-dialog__header {
        height: 50px !important;
    }
    .el-dialog__body {
        font-size: 12px;
    }
    .el-steps{
        margin-bottom: 20px;
    }
    .el-dialog__title{
        max-width: 80% !important;
    }
    .el-steps{
        .is-process{
            border-color: initial !important;
            background-color: initial !important;
            border-width: initial !important;
            border-style: initial !important;
            .el-step__icon-inner::after{
                display: none;
            }
        }
        .is-wait{
            background-color: initial !important;
        }
        [dir=rtl] & {
            .el-step__line {
                left: -50%;
                right: 50%;
            }
        }
    }
}
</style>
