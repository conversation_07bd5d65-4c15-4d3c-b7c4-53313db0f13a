<template>
    <div class="ssq-slider-item">
        <ul>
            <li
                v-for="(item, index) in itemList"
                :key="index"
                :class="{ active: item.active}"
                @click="handleCheckJump(item)"
            >
                <span>{{ item.title }}</span>
            </li>
        </ul>
    </div>
</template>

<script>
export default {
    components: {
    },
    props: {
        itemList: {
            type: Array,
            default: () => ([]),
        },
    },
    data() {
        return {

        };
    },
    methods: {
        handleCheckJump(item) {
            this.$emit('sideItemClick', item);
        },
    },
    mounted() {

    },
};
</script>

<style lang="scss">
.ssq-slider-item {
    li {
        display: inline-block;
        width: 219px;
        height: 44px;
        line-height: 44px;
        color: #333333;
        border-right: 3px solid #F6F6F6;
        box-sizing: border-box;
        font-size: 12px;
        transition: background-color 0.3s, color 0.3s, border-color 0.3s;
        cursor: pointer;
        span {
            margin-left: 50px;
            [dir=rtl] & {
                margin-left: 0;
                margin-right: 50px;
                border-left: 3px solid #F6F6F6;
                border-right: none;
            }
        }
        &:hover {
            color: #127FD2;
            border-color: #127FD2;
            background-color: #FFFFFF;
        }
    }
    .active {
        color: #127FD2;
        border-color: #127FD2;
        background-color: #FFFFFF;
    }
}
</style>

