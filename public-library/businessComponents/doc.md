# footer组件

#### 通用底部

**使用说明** ：直接引入即可

# Header组件

#### 通用头部

**使用说明** ：直接引入即可

# certificationRealnameIcon组件

#### 实名认证—认证图标

##### props说明：

|  参数  |  类型   |            说明             | 默认值 | 是否必填 |
| :----: | :-----: | :-------------------------: | :----: | :------: |
|  type  | String  |          图标类型           |        |          |
| hasPic | Boolean |     是否非免身份证认证      |  true  |          |
| status | Number  | // 0 未认证 1 已认证 2 审核 |        |          |

# certificationAllIconsStatic组件

#### 实名认证--所有认证图标

##### props说明：

|          参数          |  类型   |        说明        |                            默认值                            | 是否必填 |
| :--------------------: | :-----: | :----------------: | :----------------------------------------------------------: | :------: |
|         title          | String  |        标题        |                                                              |          |
|         hasPic         | Boolean | 是否非免身份证认证 |                             true                             |          |
| configAcceptAuthConfig | Object  |   支持的认证方式   | {'bankAuth': true,'faceAuth': true,'handAuth': true,'phoneAuth': true,'abroadAuth': true,} |          |
|  bankcardIconsStatus   | Object  | 手持身份证认证状态 |              {authinfoStatus: 0,auditInfo: ''}               |          |
|    phoneIconsStatus    |  同上   |   手机号认证状态   |                             同上                             |          |
|    faceIconsStatus     |  同上   |    刷脸认证状态    |                             同上                             |          |
|   sesameIconsStatus    |  同上   |    芝麻认证状态    |                             同上                             |          |
|   inhandIconsStatus    |  同上   |  手持证件认证状态  |                             同上                             |          |
| notmainlandIconsStatus |  同上   | 非大陆人士认证状态 |                             同上                             |          |
|       iconConfig       | Object  |    是否展示图标    |                                                              |          |

# certificationOutOfCourt组件

#### 实名认证—驳回展示

##### props说明：

|    参数    |  类型   |          说明          | 默认值 | 是否必填 |
| :--------: | :-----: | :--------------------: | :----: | :------: |
|    isPC    | Boolean |         是否pc         |  true  |          |
|    type    | String  |  类型// 0 企业 1 个人  |  "1"   |          |
|    name    | String  |          名称          |        |          |
|  idNumber  | String  |        证件号码        |        |          |
| rejectInfo |  Array  |        驳回原因        |        |          |
|  showTip   | Boolean | 是否展示驳回的认证方式 |  true  |          |

# certificationPermissionNoticeStatic组件

#### 实名认证—将获得权限图标

**使用说明** ：直接引入即可

# companyFull组件

#### 选择企业成员弹框

**props说明：**

|        参数        |  类型   |          说明           |  默认值  | 是否必填 |
| :----------------: | :-----: | :---------------------: | :------: | :------: |
|       state        | Boolean |      是否显示弹框       |          |          |
|     selectType     | String  |   成员单选，多选类型    | checkBox |          |
|     onlyActive     | Boolean |  是否只是所有可用用户   |   True   |          |
|       title        | String  |        弹框标题         |          |          |
|   exsitedUserIds   |  Array  | 已存在成员的userid list |          |          |
| exsitedMemberTitle | String  |    已选择的成员标题     |          |          |
|    submitEmpty     | Boolean |    是否可以不选成员     |  False   |          |

# resetPassword组件

#### 密码复杂度过低提示弹框，要求重置密码

```vue
 Vue.$resetPassword();
```

# selectBizLine组件

#### 选择业务线弹窗

##### props说明：

|          参数        |   类型   |          说明          | 默认值 | 是否必填 |
| :------------------: | :-----: | :--------------------: | :----: | :------: |
|       visible       | Boolean |         是否显示         |  true  |          |
|      bizLineList    | Array   |        业务线数据        |  []    |          |
|   selectedLineEntId | String  |       选中业务线id       |  ''    |          |
|       callBack      | Function  |        回调           |        |          |


# giftContract

#### {注册/认证}成功合同奖励

##### props说明：

|          参数        |   类型   |               说明             |   默认值    | 是否必填 |
| :------------------: | :-----: | :----------------------------: | :--------: | :------: |
|       giftType       |  String |    礼物类型register/personAuth  |  'register'  |          |
