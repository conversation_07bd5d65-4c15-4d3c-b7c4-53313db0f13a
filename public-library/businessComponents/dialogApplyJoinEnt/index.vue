<template>
    <div>
        <el-dialog
            class="applyJoinEnt-dialog"
            :class="{
                'isPhone': !isPC
            }"
            :visible.sync="dialogVisible"
            :show-close="showClose"
            :close-on-click-modal="showClose"
            :close-on-press-escape="showClose"
            @open="handleOpen"
            @close="handleClose"
        >
            <!-- 提示去申诉/联系主管理员 -->
            <div class="applyJoinEnt-infoCon" v-if="!showJoinEnt">
                <div class="applyOrJoin-content">
                    <p class="icon-line"><i class="el-icon-ssq-tishi1"></i></p>
                    <template v-if="inAuthPage">
                        <div class="subject-info">
                            <p>{{ subjectName }}</p>
                            <p>{{ $t('dialogApplyJoinEnt.beenAuthenticated') }}</p>
                        </div>
                    </template>
                    <template v-else>
                        <div class="subject-info">
                            <p>{{ $t('dialogApplyJoinEnt.assignedIdentity') }}</p>
                            <p>{{ subjectName }}</p>
                        </div>
                    </template>
                    <div v-if="entAdminName || entAdminAccount " class="admin-info">
                        <p v-if="!inAuthPage">{{ $t('dialogApplyJoinEnt.entBeenAuthenticated') }}</p>
                        <p v-if="entAdminName">{{ $t('dialogApplyJoinEnt.entAdminName') }}{{ entAdminName || '' }}</p>
                        <p v-if="entAdminAccount">{{ $t('dialogApplyJoinEnt.entAdminAccount') }}{{ entAdminAccount || '' }}</p>
                    </div>
                    <p class="joinOrApply-tip">{{ joinOrApplyTip }}</p>
                </div>
                <div class="applyJoinEnt-btns">
                    <!-- 在生态版时，不显示胜诉按钮 -->
                    <el-button class="applyJoinEnt-btn-appeal btn-type-one" @click="handleGoAppeal">
                        <p>{{ $t('dialogApplyJoinEnt.applyToBeAdmin') }}</p>
                    </el-button>
                    <el-button class="btn-type-one" @click="handleContactAdmin">
                        <p>{{ $t('dialogApplyJoinEnt.contactToJoin') }}</p>
                    </el-button>
                </div>
            </div>
            <!-- 申请权限表单 -->
            <div class="applyJoinEnt-form" v-else>
                <div class="applyJoinEnt-formItem">
                    <span class="applyJoinEnt-formItem__label applyJoinEnt-formItem__label-padding">
                        <span class="applyJoinEnt-formItem__labelRequiredIcon">*</span>
                        {{ $t('dialogApplyJoinEnt.applicant') }}
                    </span>
                    <el-input :placeholder="$t('dialogApplyJoinEnt.inputYourName')" v-model="formData.name"></el-input>
                </div>
                <div class="applyJoinEnt-formItem">
                    <span class="applyJoinEnt-formItem__label applyJoinEnt-formItem__label-padding">{{ $t('dialogApplyJoinEnt.account') }}</span>
                    <span>{{ account }}</span>
                </div>
                <div class="applyJoinEnt-form__btns">
                    <el-button
                        class="btn-type-one applyJoinEnt-form__submitBtn"
                        :disabled="formData.name.length === 0"
                        @click="handleSubmit"
                    >
                        {{ $t('dialogApplyJoinEnt.send') }}
                    </el-button>
                </div>
            </div>
            <slot name="more"></slot>
        </el-dialog>

        <!-- 选择业务线 -->
        <SelectBizLine
            :visible.sync="selectLineVisbile"
            :selectedLineEntId.sync="selectedLineEntId"
            :bizLineList="bizLineList"
            :callBack="handleSubmit"
        ></SelectBizLine>
    </div>
</template>

<script>
import { isPC } from 'pub-utils/device.js';
import { bizLineMixin } from 'pub-mixins/checkBizLine.js';
import SelectBizLine from 'pub-businessComponents/selectBizLine';

export default {
    components: {
        SelectBizLine,
    },
    mixins: [bizLineMixin],
    props: {
        // 签约主体名称
        subjectName: {
            type: String,
            default: '',
        },
        // 管理员名称
        entAdminName: {
            type: String,
            default: '',
        },
        // 管理员账号
        entAdminAccount: {
            type: String,
            default: '',
        },
        appliedEntId: {
            type: String,
            default: '',
        },
        applyUserId: {
            type: String,
            default: '',
        },
        account: {
            type: String,
            default: '',
        },
        type: {
            type: String,
            default: 'sign', // sign , auth
        },
        showClose: {
            type: Boolean,
            default: true,
        },
        pageFrom: {
            type: String,
            default: 'sign', // sign , auth
        },
        corpName: {
            type: String,
            default: '',
        },
    },
    data() {
        return {
            isPC: isPC(),
            dialogVisible: true,
            formData: {
                name: '',
                notes: '',
            },
            showJoinEnt: false,
            selectLineVisbile: false,
        };
    },
    computed: {
        inAuthPage() {
            return this.type === 'auth';
        },
        joinOrApplyTip() {
            return this.inAuthPage ? this.$t('dialogApplyJoinEnt.sendWishToJoin') : this.$t('dialogApplyJoinEnt.applyToJoin');
        },
    },
    watch: {
        dialogVisible(val) {
            if (val) {
                this.$sensors && this.$sensors.track({
                    eventName: 'Ent_CompanyAuthWindow_PopUp',
                    eventProperty: {
                        auth_method: '',
                        page_name: '提交基本信息',
                        identitycard_type: '',
                        auth_type: '',
                        window_name: '企业已被其他人实名',
                        previous_page_name: this.pageFrom,
                    },
                });
            }
        },
    },
    methods: {
        // 前置步骤已选择业务线时，在这里提供赋值 selectedLineEntId 的方法跳过选择
        handleSetLineEntId(entId) {
            entId && (this.selectedLineEntId = entId);
        },
        handleOpen() {
            this.$emit('open');
        },
        handleClose() {
            this.$emit('close');
        },
        handleContactAdmin() {
            this.showJoinEnt = true;
            this.handleTrackBtnClick('联系管理员加入企业');
        },
        // 去申诉
        handleGoAppeal() {
            this.handleTrackBtnClick('我要申诉成为主管理员');
            this.$router.push(`/account/appeal?subjectName=${this.corpName ? this.corpName : this.subjectName}${this.inAuthPage ? '' : '&from=sign'}`);
        },
        handleTrackBtnClick(btnName) {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_CompanyAuthWindow_BtnClick',
                eventProperty: {
                    auth_method: '',
                    page_name: '提交基本信息',
                    identitycard_type: '',
                    auth_type: '',
                    window_name: '企业已被其他人实名',
                    previous_page_name: this.pageFrom,
                    icon_name: btnName,
                },
            });
        },
        // 提交申请表单
        async handleSubmit() {
            // 判断加入多业务线企业
            const hasBizLine = await this.checkHasBizLine(this.subjectName);
            if (hasBizLine && !this.selectedLineEntId) {
                if (this.bizLineList.length > 1) {
                    this.selectLineVisbile = true;
                    return false;
                } else {
                    this.selectedLineEntId = this.bizLineList[0].entId;
                }
            }

            this.$http.post('/ents/apply-join-ent/add', {
                appliedEntId: this.selectedLineEntId,
                appliedEntName: this.subjectName,
                account: this.account,
                applyUserId: this.applyUserId,
                applyName: this.formData.name,
                remark: this.formData.notes,
            }).then(() => {
                this.handleClose();
                this.$MessageToast.success(this.$t('dialogApplyJoinEnt.sentSuccessful'));
            });
        },
    },
};
</script>

<style lang="scss">
.applyJoinEnt-dialog {
    .el-dialog__header{
        height: 0!important;
        padding: 0!important;
        .el-dialog__headerbtn{
            margin-right: 20px;
            margin-top: 20px;
        }
    }

    .el-dialog {
        width: 384px;
    }

    &.isPhone .el-dialog {
        width: 100%;
    }

    .el-dialog__body {
        padding: 0!important;
        line-height: 20px;
    }

    .applyJoinEnt-infoCon {
        margin-bottom: 17px;
        color: #333;
        font-size: 12px;
    }

    .applyOrJoin-content{
        padding: 25px 32px 14px;
        background: #F4F9FC;

        .icon-line{
            color: #127FD2;
            font-size: 20px;
            text-align: center;
        }
    }

    // 签约主体名称
    .subject-info {
        padding-top: 5px;
        padding-bottom: 12px;
        text-align: center;
        font-size: 14px;
        color: #333;

        p{
            word-break: break-all;
        }
    }

    .joinOrApply-tip{
        margin-top: 12px;
        border-top: 1px solid #eee;
        padding-top: 10px;
        color: #666;
        text-align: left;
        line-height: 14px;
    }

    // 按钮
    .applyJoinEnt-btns {
        padding-top: 17px;
        text-align: center;

        .el-button {
            width: 150px;
            height: 34px;
            line-height: 17px;
            padding: 0;
            font-size: 12px;
        }
    }

    // 申诉按钮
    .applyJoinEnt-btn-appeal {
        margin-right: 18px;
    }

    // 表单样式
    .applyJoinEnt-form {
        padding: 33px;
        font-size: 14px;

        .applyJoinEnt-formItem div.el-input {
            width: 220px;

            .el-input__inner {
                width: 220px;
                height: 30px;
            }
        }
    }

    .applyJoinEnt-formItem__label {
        position: relative;
        display: inline-block;
        width: 52px;
        color: #666;
    }

    .applyJoinEnt-formItem__labelRequiredIcon {
        position: absolute;
        left: -8px;
        top: 2px;
        color: #FF0000;
    }

    .applyJoinEnt-formItem__label-padding {
        padding-bottom: 20px;
    }

    .applyJoinEnt-form__btns {
        padding-top: 15px;
        padding-right: 35px;
        text-align: right;
    }

    .applyJoinEnt-form__submitBtn {
        width: 70px;
        height: 33px;
        padding: 0;

        &:disabled{
            color: #ccc;
            background: #eee;
        }
    }
}
</style>
