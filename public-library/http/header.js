/* eslint-disable eqeqeq */
import axios from 'axios';
import store from 'src/store';
import { addBizPoint } from 'pub-utils/business/addBizPoint.js';

// 获取 head-info 后进行一些赋值操作
axios.headerInfoConfig = headerInfo => {
    // head-info存在store的commonHeaderInfo中
    store.commit('pushInitCommonHeaderInfo', headerInfo);
    Vue.$hybrid.setVersion(headerInfo.deltaHybridVersion);

    // 区分用户是公有云还是混合云
    // 如果是混合云用户，开始轮询检测内网环境
    if (headerInfo.hybridServer) {
        store.commit('changeHybridUserType', 'hybrid');
        // 不等轮询结果，先resolve
        Vue.$hybrid.checkInLAN(0);
        return Promise.resolve();
    } else {
        Vue.$hybrid.clearAll();
        store.commit('changeLANStatus', null);
        store.commit('changeHybridUserType', 'public');
        store.commit('replaceHybridAccessToken', {
            hybridServer: '',
            hybridAccessToken: '',
        });
        return Promise.resolve();
    }
};

// 退出登录
axios.logOut = async function() {
    let ssoLogoutLink = '';
    if (~~Vue.$cookie.get('isBrand') === 1) {
        // saml协议的sso用户，登出时需要传samlNameId
        const { data } = await axios.get(`users/ignore/ent-brand?samlNameId=${Vue.$cookie.get('samlNameId') || ''}`);
        ssoLogoutLink = data.ssoLogoutLink || '';
    }
    return new Promise((resolve) => {
        localStorage.removeItem('dozenList');
        axios.post(`/auth-center/user/logout`, {
            refreshToken: Vue.$cookie.get('refresh_token'),
        }).finally(() => {
            Vue.$token.delete();
            Vue.$hybrid.clearAll();
            store.commit('logout');
            if (ssoLogoutLink) {
                location.href = ssoLogoutLink;
            } else {
                resolve();
            }
        });
    });
};

axios.getDevInfo = function(clientId) {
    return new Promise((resolve) => {
        axios
            .get('/ents/ignore/developer/info', {
                params: { clientId },
            })
            .then(res => {
                resolve(res);
            });
    });
};

// 切换身份
axios.switchEntId = function(entId) {
    return new Promise((resolve, reject) => {
        axios.post('/authenticated/switch-ent', {
            entId,
            refreshToken: Vue.$cookie.get('refresh_token'),
        }).then(({ data }) => {
            Vue.$token.save(data.access_token, data.refresh_token);
            Vue.$http.get('/users/head-info', { noToast: 1 }).then(res => {
                if (res && res.data) {
                    Vue.$http.headerInfoConfig(res.data);
                    resolve();
                }
            }).catch(e => reject(e));
        }).catch(e => reject(e));
    });
};
axios.addBizPoint = (scene = '', contractId = '', isStart = false) => {
    return addBizPoint(scene, contractId, isStart);
};
