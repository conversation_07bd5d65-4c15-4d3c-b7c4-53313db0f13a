// HTTP 请求状态码为 409 的情况下后端返回的 code 字段
import Vue from 'vue';
import axios from 'axios';
import store from 'src/store';
import router from 'src/router';
import i18n from 'src/lang/';

let responeseData = {};

const codeMethodMap = {
    // 登录过期
    '040003'() {
        if (!router.history.current.meta.noLogin) {
            // 删除登录信息
            Vue.$token.delete();

            Vue.$hybrid.clearAll();
            store.commit('logout');
            redirectAfterPromise(Vue.$MessageToast.error(i18n.t('pubTips.loginOverdue')), '/account-center/login');
        }
    },
    // 访问令牌被挪用
    '040017'() {
        // 删除登录信息
        Vue.$token.delete();
        redirectAfterPromise(Vue.$MessageToast.error(responeseData.message), '/account-center/login');
    },
    // 错误的权限
    '160001'() {
        redirectAfterPromise(Vue.$MessageToast.error(responeseData.message), '/account-center/home');
    },
    // 模板授权被取消后，没有权限使用
    '160007'() {
        templateErrorHandler(i18n.t('codeHandlerMap.tip1')); // 您对该模板的使用权限被模板创建者收回
    },
    // 模板被创建人删除后，被授权人无法使用
    '160008'() {
        templateErrorHandler(i18n.t('codeHandlerMap.tip2')); // 此模板已被创建者删除
    },
    // 模板被停用后，被授权人无法使用
    '170036'() {
        templateErrorHandler(i18n.t('codeHandlerMap.tip3')); // 此模板已被创建者停用，无法继续使用
    },
    // 当前员工被停用，需要自动登出
    '090018'() {
        Vue.$token.delete();
        Vue.$MessageToast.error(responeseData.message);
        setTimeout(() => {
            redirectAfterPromise(axios.logOut(), '/account-center/login');
        }, 1000);
    },
    // 个人用户无法进入模板页面
    '170001'() {
        Vue.$MessageToast.error(responeseData.message);
        router.push('/account-center/home');
    },
    // 资源数量限制
    '120103'() {
        messageBox(responeseData.message || i18n.t('pubTips.errorTip'), responeseData.data, i18n.t('pubTips.know'), '120103');
    },
    '110146'() {
        messageBox('温馨提示', '注意到您的企业和发件方企业都曾接受过来自工行赠送的合同份数；按照约定，您需要从工行指定入口才能签署此合同', '知道了', '110146');
    },
};

// 通用异常提示code白名单，具体异常在业务层处理
const NOTOAST_CODE_LIST = [
    'U-180012', // 混合云上传的文件已有签名，暂不支持此类文件
    '180012', // 上传的文件已有签名，暂不支持此类文件，具体的异常提示通过弹窗展示
    '020510', // 认领鉴权报错不报错，直接跳转新页面
    '130002', // 合同比对没有查看下载权限
    '130005', // 混合云比对不能查看
    '130006', // 合同比对没有查看下载权限
];

// 模板错误通用方法
function templateErrorHandler(content) {
    redirectAfterPromise(
        messageBox(i18n.t('codeHandlerMap.tip4'), content, i18n.t('codeHandlerMap.tip5')),
        '/template/list',
    );
}

function redirectAfterPromise(promise, url) {
    return promise.then(() => {
        router.push(url);
    });
}

function messageBox(title, message, confirmBtnText, errCode) {
    // eslint-disable-next-line new-cap
    return Vue.MessageBox({
        title,
        message,
        confirmBtnText,
        errCode,
    });
}

/**
 *
 * @param {*} code HTTP 状态码
 * @param {*} resData 接口返回的数据
 * @param {*} defaultMessage 通用的消息提示
 * @param {*} error 异常消息体
 * @param {*} status 状态码
 */
export function codeHandler(code, resData, defaultMessage, error, status) {
    let message = defaultMessage;
    responeseData = resData || {};

    if ((code === 'U-900' || [405, 404].includes(Number(status))) && store.state.commonHeaderInfo.hybridServer) {
        // 混合云jar包，版本太低 不支持新功能，统一提示
        message = i18n.t('codeHandlerMap.getNewJar'); // 请联系上上签升级jar包
    } else if (status >= 500) {
        message = message || i18n.t('pubTips.serverError');
    } else if (error.message === 'Network Error') {
        message = i18n.t('codeHandlerMap.networkNotConnect'); // 当前网络不可用，请检查你的网络设置
    }

    if (Number(status) === 409 && Object.keys(codeMethodMap).indexOf(code) >= 0) {
        codeMethodMap[code]();
    } else if (code === '300001') { // 高级功能不可用，请清除配置，或者续费后，方可继续使用
        if (store.getters.getIsForeignVersion) {
            return Vue.$MessageToast.error(i18n.t('codeHandlerMap.advancedFeatureError'));
        }
        const dataInfo = responeseData && responeseData.data;
        Vue.$featureSupport({ endData: dataInfo || {}, type: 'trialEnd' });
    } else if (!NOTOAST_CODE_LIST.includes(code)) { // 过滤不需要异常处理的code list
        message && Vue.$MessageToast.error(message);
    }
}

/**
 * 保存接口数据
 * @param {*} config
 * @param {*} data 接口返回的数据
 */
export function saveResponseHandler({ config, data }) {
    store.commit('setReportHttpData', { url: config.url, data });
}

