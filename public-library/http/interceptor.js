/* eslint-disable eqeqeq */
// 当前版本 64bdc3640daaa38c61157ee11a038b23ea287be9 Tue Mar 17 17:37:24 2020 +0800
// 提测之前需比对下版本
import axios from 'axios';
import cookie from 'vue-cookie';
import token from '../plugins/token';
import GLOBAL from '../consts/global';
import { code<PERSON>and<PERSON>, saveResponseHandler } from './codeHandler.js';
import i18n from 'src/lang/';
import router from 'src/router';
import loginByPassword from '../mock/login';
import { isPC, isWechat, isQyWechat, isMiniprogram } from 'pub-utils/device.js';
const getTerminal = function() {
    let terminal;
    if (isPC()) {
        terminal = 'PC';
    } else if (isQyWechat && isMiniprogram) {
        terminal = 'QWX_APPLET';
    } else if (isQyWechat && !isMiniprogram) {
        terminal = 'QWX';
    } else if (isWechat && isMiniprogram) {
        terminal = 'WX_APPLET';
    } else if (isWechat && !isMiniprogram) {
        terminal = 'WX';
    } else if (localStorage.getItem('isBestSignApp')) {
        terminal = 'APP';
    } else {
        terminal = 'H5';
    }
    return terminal;
};

// 匹配到以下路径headers里不加terminal字段
const ignoreAddTerminal = ['www/api'];
axios.defaults.baseURL = `${window.location.protocol}//${window.location.host}`;
// axios request 拦截器
/**
 * config 动态配置参数
 * noToast: 1, 接口返回非200去除默认toast
 * noUseToken: true, 接口请求中header不使用Authorization,使用场景：混合云3
 */
axios.interceptors.request.use(config => {
    let access_token = cookie.get('access_token');
    if (config.useStageToken) {
        access_token = cookie.get('stage_access_token');
    }
    // 避免混3上传config中传headers.Authorization
    if (config.noUseToken && config.headers && config.headers.Authorization) {
        delete config.headers.Authorization;
    }
    if (access_token && !config.noUseToken) {
        config.headers.Authorization = `bearer ${access_token}`;
    }
    // 给 get 请求加上时间戳防缓存
    if (config.method === 'get') {
        config.url += `${config.url.indexOf('?') > -1 ? '&' : '?'}_t=${new Date().getTime()}`;
    }
    /**
      * terminal字段
        PC
        H5
        APP
        WX
        WX_APPLET
        QWX
        QWX_APPLET
    */
    if (!ignoreAddTerminal.find(str => config.url.indexOf(str) !== -1)) {
        config.headers['terminal'] = getTerminal();
    }

    // 在config中记录请求发起的时间
    config.requestTime = new Date().getTime();

    // config.headers['x-authenticated-userid'] = '{"userId":2272327918275267593,"developerId":1,"chosenEntId":2246102699454002180,"empId":2428193981079795720}';
    // config.headers['x-authenticated-userid'] = '{"userId":2250654608781260800,"developerId":1,"chosenEntId":2251179070752586753,"empId":2251179070769363972}';
    // config.headers['x-authenticated-userid'] = '{"userId":2022891049763995655,"chosenEntId":2022891049856270341,"empId":2022891049873047559,"developerId":1}';
    return config;
});
axios.interceptors.response.use(response => {
    saveResponseHandler(response);
    const { config: { url }, data: { code, message, msg } } = response;
    response.responseTime = new Date().getTime();
    if (url.includes('/contract-center') && code !== '0') {
        // contract-center、contract-center-bearing接口单独处理
        Vue.$MessageToast.error(message || msg || i18n.t('codeHandlerMap.errorTip')); // 服务器开了点小差，请稍后再试
        return Promise.reject(response);
    }
    if (url.includes('/users/head-info')) {
        localStorage.setItem('currentTabEntId', response.data?.currentEntId);
    }
    return response;
}, error => {
    const responseTime = new Date().getTime();

    if (error.code === 'ECONNABORTED') {
        return Promise.reject(packingCustomGeneralError(error, responseTime));
    }

    if (error.response) {
        // 请求成功发出且服务器也响应了状态码
        error.response.responseTime = responseTime;
        if (error.response.status === 401) {
            return handle401Response(error);
        }
        handleResponseError(error);
        return Promise.reject(packingCustomGeneralError(error, responseTime));
    } else {
        // 如果存在error.request，代表请求已经成功发起，但没有收到响应，浏览器网络设置offline可以复现场景
        // console.log(error.request);
        // 如果不存在error.request，则代表发送请求时出了点问题，可查看error.config
        return Promise.reject(packingCustomGeneralError(error, responseTime));
    }
});
function handle401Response(error) {
    const { config } = error;
    const refreshToken = cookie.get('refresh_token');
    token.delete();
    // 如果是本地开发，并且本地是localhost、127.0.0.1打开，通过dialog登陆
    if (process.env.NODE_ENV.includes('development') && (location.host.indexOf('localhost') > -1 || location.host.indexOf('127.0.0.1') > -1 || location.host.indexOf('192.168.') > -1) && location.port !== '6004') {
        loginByPassword();
        return Promise.reject(error);
    }
    // 如果access_token过期，则请求刷新token接口，然后重新调用原接口
    if (!config._retry) {
        config._retry = true;
        if (refreshToken) {
            // 如果存在refreshToken, 用refreshToken去刷新成新的token，然后接着请求这个401的接口
            return postRefreshToken(config, refreshToken);
        }
        // 如果不存在refreshToken, 前往登陆页面
        if (router.history.current.meta.noLogin) {
            Vue.$hybrid.clearAll();
            return Promise.reject(error);
        }
        if (GLOBAL.rootPathName == '/ssoinner') {
            router.push(`${GLOBAL.rootPathName}/account-center/sso/notFound`);
        } else {
            /* 将当前需要登录才能访问的页面通过redirect返回 */
            // repalce场景下，未设置noToast的场景不弹toast提示
            config.noToast === undefined ? config.noToast = 1 : '';
            router.replace({
                path: `${location.origin}/account-center/login`,
                query: {
                    redirect: location.pathname + location.search,
                },
            });
        }
    } else {
        return Promise.reject(packingCustomGeneralError(error, error.response.responseTime));
    }
}
// 请求成功发出且服务器也响应了状态码，但状态代码超出了 2xx 的范围
function handleResponseError(error) {
    const {
        config,
        response: {
            status,
            data,
        },
    } = error;
    const code = data && data.code;
    const message = !config.noToast && data && data.message;

    codeHandler(code, data, message, error, status);

    // 除了noToast不进行展示的error，其他error进行上报
    if (!config.noToast) {
        sensorsRequestError({
            url: config.url,
            reason: data?.message || error.message,
            errorCode: code,
            httpCode: status,
        });
    }
}

// 子项目初始化sensors的,进行接口报错埋点
function sensorsRequestError(obj = {}) {
    Vue.$sensors && Vue.$sensors.track({
        eventName: 'Ent_CommonError_Log',
        eventProperty: {
            module_name: process.env.VUE_APP_PROJECT_NAME,
            request_url: obj.url,
            fail_reason: obj.reason,
            fail_error_code: obj.errorCode,
            fail_http_code: obj.httpCode,
        },
    });
}

// 服务器未响应时，封装一个供业务使用的Object对象
// 原来会返回一个Error Type对象，不友好
function packingCustomGeneralError(error, responseTime) {
    // 可以在这里处理不同类型的error
    // 原始throw出来的error对象，包含name、message、stack属性
    // console.log(error instanceof Error);
    // console.log(error instanceof TypeError);

    let result = {
        responseTime: responseTime,
        response: error.response,
        status: error.name,
        statusText: error.message,
        config: error.config,
        request: error.request, // `error.request` 在浏览器中是 XMLHttpRequest 的实例
        name: error.name, // error对象的默认属性
        message: error.message, // error对象的默认属性
        stack: error.stack, // error对象的默认属性
    };

    // 一般40x的请求会有response，50x的没有
    if (error.response) {
        result = {
            ...result,
            status: error.response.status, // 请求的http code
            statusText: error.response.data.message || error.message, // 后端有response错误提示则用后端的错误提示
        };
    }

    return result;
}

function postRefreshToken(config, refreshToken) {
    return axios.post('auth-center/user/refresh-token', { refreshToken })
        .then(async({
            data: {
                access_token,
                refresh_token,
                token_type,
            },
        }) => {
            token.save(access_token, refresh_token);
            const temp = `${token_type} ${access_token}`;
            axios.defaults.headers.common['Authorization'] = temp;
            config.headers['Authorization'] = temp;
            return axios(config);
        }).catch(() => {
            if (!router.history.current.meta.noLogin) {
                router.replace({
                    path: `${location.origin}/account-center/login`,
                    query: {
                        redirect: location.pathname + location.search,
                    },
                });
                Vue.$hybrid.clearAll();
            }
        });
}

axios.install = function(Vue) {
    Vue.$http = axios;
    Vue.prototype.$http = axios;
};

export default axios;
