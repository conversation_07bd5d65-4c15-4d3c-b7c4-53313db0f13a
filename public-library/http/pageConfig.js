import axios from 'axios';
import store from 'src/store';
// 登录注册时获取页面配置
axios.getNoLoginPageConfig = function(clientId) {
    return Vue.$http
        .get(`/users/ignore/style/${clientId}`)
        .then(res => {
            if (res && res.data) {
                store.commit('pushSsoConfig', res.data);
            }
        })
        .catch(() => {});
};

// 获取页面配置
axios.getPageConfig = async function(headerInfo) {
    if (!Object.keys(store.getters.getSsoConfig).length &&
        headerInfo.extendFields.isLoginFromDeveloper
    ) {
        // 乐高成-客户管理/页面配置, 单点登陆页面配置信息
        const { data } = await Vue.$http.get('/users/style');
        store.commit('pushSsoConfig', data);
    }
    // 乐高成-客户管理/客户配置，即商品化不同版本的功能点
    const { data } =  await Vue.$http.get('/users/features');
    store.commit('pushFeatures', data);
    // 高级功能信息
    const res =  await Vue.$http.get('/ents/advanced-features-detail');
    store.commit('pushAdvancedFeatures', res.data);
    return Promise.resolve();
};

// 合同管理第一期，查询是否开通过合同管理
// 第二期去掉了这部分逻辑。。。
axios.getConfigsInfo = () => {
    let url = 'ents/configs/default/NEW_CONTRACT_MANAGE/value';
    if (store.getters.getUserType === 'Person') {
        url = '/users/configs/default/NEW_CONTRACT_MANAGE';
    }
    return Vue.$http
        .get(url)
        .then(res => {
            if (res && res.data) {
                store.commit('setNewContract', res.data.value === 'true');
            }
        })
        .catch(() => {});
};
