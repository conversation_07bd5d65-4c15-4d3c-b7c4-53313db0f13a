/* eslint-disable eqeqeq */
// TODO:  只用在了登陆注册模块、以及涉及到发送短信验证码的部分。其中只有crypto-js在/users/ignore/captcha/notice这个接口中用到，需要做接耦
import axios from 'axios';
import store from '../../src/store';
import i18n from 'src/lang';
axios.getImageVerCode = function(imageKey) {
    return new Promise((resolve) => {
        axios
            .get(`/users/ignore/captcha/base64-image`, {
                params: { imageKey },
            })
            .then(res => {
                resolve(res);
            });
    });
};

/**
 * code:
 * 'B001': 注册
 * 'B002': 忘记密码
 * 'B003': 重置签约密码
 * 'B004': 修改通知方式
 * 'B005': 修改账号
 * 'B006': 更换管理员
 * 'B008': 发送通知到账号
 */

const sendVerCommon = function(opts, type) {
    const captchaVerifyParam = store.state.captchaVerifyParam;
    const {
        code,
        imageCode = '',
        imageKey = '',
        sendType,
        target = '',
        bizTargetKey = '',
        noToast = 0,
        token = '',
    } = opts;
    let url;
    switch (type) {
        case 1:
            url = '/users/captcha/notice';
            break;
        case 2:
            url = '/users/ignore/v2/captcha/notice';
            break;
        case 3:
            url = '/users/ignore/captcha/notice-token';
            break;
    }

    const params = {
        url: url,
        method: 'post',
        data: {
            code,
            sendType,
            target,
            bizTargetKey,
        },
        noToast: noToast || 0,
    };

    if (type == 2 || type == 1) {
        const AES = require('crypto-js/aes');
        const Utf8 = require('crypto-js/enc-utf8');
        const ECB = require('crypto-js/mode-ecb');
        const Pkcs7 = require('crypto-js/pad-pkcs7');
        // 使用AES对'{code}:{target}'数据进行加密，目的增加中间人修改手机号频繁调接口的难度
        // mod,和padding和后端约定好
        const key = Utf8.parse(Vue.GLOBAL.AES_ENCRYPT_KEY);
        const timestamp = Date.parse(new Date());
        const encryptStr = `${code}:${target}:${bizTargetKey ?? ''}:${timestamp}`;
        const encryptToken = AES.encrypt(encryptStr, key, {
            mode: ECB,
            padding: Pkcs7,
        }).toString();
        // 可能含有特殊字符，encode下
        const encodeToken = encodeURIComponent(encryptToken);
        params.url = `${url}?encryptToken=${encodeToken}`;
    }

    if (type == 3) {
        Object.assign(params.data, {
            token: token,
        });
    }

    let headersObj;
    if (imageCode !== '' && imageKey !== '') {
        headersObj = {
            'Content-Type': 'application/json; charset=utf-8',
            additionalImgVerCode: JSON.stringify({
                imageCode: imageCode,
                imageKey: imageKey,
            }),
        };
        Object.assign(params, { headers: headersObj });
    }
    if (captchaVerifyParam) {
        headersObj = {
            'Content-Type': 'application/json; charset=utf-8',
            additionalAfs: captchaVerifyParam,
        };
        Object.assign(params, { headers: headersObj });
    }
    return axios(params);
};

// 发送接口调用成功后逻辑
const sendVerSuccess = function(res) {
    const { msg } = res.data;
    if (msg) {
        // eslint-disable-next-line new-cap
        Vue.MessageBox({
            headerTitle: i18n.t('commonHeader.tip'),
            message: msg,
            confirmBtnText: i18n.t('autoSeal.iSee'),
            showHeader: true,
        });
    }
};

// 需要登录
axios.sendVerCode = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 1)
            .then(res => {
                sendVerSuccess(res);
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
// 不需要登录
axios.sendVerCodeNoLogin = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 2)
            .then(res => {
                sendVerSuccess(res);
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
// 不需要登录需要token
axios.sendVerCodeNoLoginNeedToken = function(opts) {
    return new Promise((resolve, reject) => {
        sendVerCommon(opts, 3)
            .then(res => {
                sendVerSuccess(res);
                resolve(res);
            })
            .catch(err => {
                reject(err);
            });
    });
};
