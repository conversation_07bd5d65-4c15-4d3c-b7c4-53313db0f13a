import axios from 'axios';
import store from 'src/store';
axios.getNoticeCount = () => {
    return Vue.$http
        .get('/ents/message/count')
        .then(res => {
            if (res && res.data) {
                store.commit(
                    'pushNoticeCount',
                    res.data || {
                        noticeUnreadMessageCount: 0,
                        contractUnreadMessageCount: 0,
                        unreadMessageCount: 0,
                        approvalUnreadMessageCount: 0,
                    },
                );
            }
        })
        .catch(() => {});
};
