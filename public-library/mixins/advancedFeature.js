import { mapGetters, mapState } from 'vuex';
import i18n from 'src/lang';
import store from 'src/store';
// 高级功能试用 mixin
export const advancedFeatureMixin = {
    data() {
        return {
        };
    },
    computed: {
        ...mapGetters([
            'checkAdvancedFeatureData',
            'getUserPermissons',
        ]),
        ...mapState({
            currentEntId: state => state.commonHeaderInfo.currentEntId,
        }),
        // 高级功能试用信息
        trialFeatureData() {
            return {
                179: this.checkAdvancedFeatureData('179'), // 模板授权
                81: this.checkAdvancedFeatureData('81'), // 归档管理
                34: this.checkAdvancedFeatureData('34'), // 添加合同附属资料
                79: this.checkAdvancedFeatureData('79'), // 必须手写并开启笔迹识别
                68: this.checkAdvancedFeatureData('68'), // 水印  骑缝章
                185: this.checkAdvancedFeatureData('185'), // 发送前审批试用信息
            };
        },
    },
    methods: {
        /**
         * * 获取试用功能状态信息
         * @param {String} featureId 高级功能的featureId
         * @param {Map} trialFeatureData  页面中涉及高级功能的数据
         * @param {Number} status 查询展示状态（1：是否显示tooltip信息 2：是否显示new信息 3：tootip文案显示）
         *
         */
        checkTrialInfoStatus(featureId, status) {
            if (Object.keys(this.trialFeatureData).includes(featureId)) {
                const trialData = this.trialFeatureData[featureId];
                if (status === 1) { // 是否显示tooltip信息
                    return trialData.onTrial === 1 || trialData.onTrial === 2;
                } else if (status === 2) { // 是否显示new信息
                    return !trialData.isOpen && trialData.onTrial === 0;
                } else if (status === 3) { // tootip文案显示
                    return trialData.onTrial === 1 ? this.$t('functionSupportDialog.trialRemainDayTip', { day: trialData.daysRemaining }) : this.$t('functionSupportDialog.trialEndTip');
                }
            }
            return false;
        },

        /**
         * * 检测试用功能是否可使用(true：可使用，false:不可使用)
         * @param {String} featureId 高级功能的featureId
         *@param {Boolean} alertTrailDialog 是否弹出试用弹窗,默认弹
         */
        checkFeatureCanUseInfo(featureId, alertTrailDialog = true) {
            if (Object.keys(this.trialFeatureData).includes(featureId)) {
                const trialData = this.trialFeatureData[featureId];
                if (!trialData.isOpen) { // 未开启
                    if (trialData.onTrial === 0 || trialData.onTrial === 2) { // (0:未试用，1：试用中，2：试用到期)
                        if (alertTrailDialog) {
                            if (store.getters.getIsForeignVersion) {
                                this.$MessageToast.error(this.$t('codeHandlerMap.advancedFeatureError'));
                                return false;
                            }
                            this.$featureSupport({ featureId: trialData.featureId, type: 'trial' });
                        }
                        return false;
                    }
                }
            }
            return true;
        },
        /**
         * * 试用tooltip点击
         * @param {String} featureId 高级功能的featureId
         * @param {Map} trialFeatureData  页面中涉及高级功能的数据
         *
         */
        handleTrialClick(featureId) {
            if (Object.keys(this.trialFeatureData).includes(featureId)) {
                const trialData = this.trialFeatureData[featureId];
                this.$featureSupport({ featureId: trialData.featureId, type: 'trial' });
            }
        },
        /**
         * * 去购买
         * @param {String} featureId 高级功能的featureId
         * @param {String} switchEntId  需要切换的主体entId
         *
         */
        async handleBuyTrialWithEntId(featureId, switchEntId) {
            // 要切换的主体和当前主体不一致，先判断能否切换
            if (this.currentEntId !== switchEntId)  {
                const res = await this.$http.get(`/ents/check-switch-ent?entId=${switchEntId}`);
                const { entId, account, empName } = res.data;
                if (!entId) {
                    return this.$MessageToast(i18n.t('functionSupportDialog.contactAdminTip', { tip: ` ${empName}(${account}) ` }));
                }
                await this.$http.switchEntId(entId);
            }
            if (!this.getUserPermissons.recharge_m) { // 无充值权限
                const { data } = await this.$http.get(`/ents/employees/mask-admin-info`);
                return this.$MessageToast(i18n.t('functionSupportDialog.contactAdminTip', { tip: ` ${data.empName}(${data.account}) ` }));
            }
            this.$featureSupport({ featureId: featureId, type: 'buySwitchEntId' });
        },
    },
};
