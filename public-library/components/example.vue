<template>
    <div>
        <CountDown
            :clickedFn="stepOneSend"
            :disabled="countDownObj.disabled"
            :second="60"
            :runFlag="countDownObj.run"
        >
        </CountDown>
        <el-input
            v-model="inputvalue"
        >
            <ElIDelete slot="suffix"></ElIDelete>
        </el-input>
        <el-input
            v-model="inputvalue"
        >
            <ElIEye slot="suffix"></ElIEye>
        </el-input>
        <SlidePop :outsideShow.sync="slidepop.outsideShow" :needReset="slidepop.needReset">
            <div slot="slide-pop-content"><!-- 侧边展示 -->{{ $t('example.sideDisplay') }}</div>
        </SlidePop>
        <button @click="showPreview = true"><!-- 点击预览 -->{{ $t('example.clickPreview') }}</button>
        <Preview v-model="showPreview" :title="$t('example.previewTitle')">
            <div slot="previewContent">222333</div>
        </Preview>
    </div>
</template>
<script>
import CountDown from 'pub-components/countDown/index.vue';
import ElIDelete from 'pub-components/elIconfont/elIDelete/index.vue';
import ElIEye from 'pub-components/elIconfont/elIEye/index.vue';
import SlidePop from 'pub-components/slidePop/index.vue';
import Preview from 'pub-components/preview';

export default {
    components: {
        CountDown,
        ElIDelete,
        ElIEye,
        SlidePop,
        Preview,
    },
    data() {
        return {
            countDownObj: {
                disabled: false,
                run: false,
            },
            inputvalue: '',
            slidepop: {
                outsideShow: true,
                needReset: 0,
            },
            showPreview: false,
        };
    },
    methods: {
        stepOneSend() {
            this.countDownObj.disabled = true;
        },
    },
    mounted() {
        this.$MessageToast({
            message: this.$t('resetPassword.modifySuccess'), // 修改成功
            type: 'success',
            iconClass: '',  // icon class
            duration: 2500,
            callBack: () => {
                console.log(1);
            }, // 关闭后回调方法
        }).then(() => {
            console.log(2);
        });
    },
};
</script>
