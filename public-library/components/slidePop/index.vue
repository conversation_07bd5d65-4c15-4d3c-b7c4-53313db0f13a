<!-- 通用组件：侧边弹出框组件 -->
<!-- 引用位置 用户中心页面Account,Contracts,Preferences,Signatrue-->
<template>
    <div class="slide-pop">
        <template v-if="reset">
            <div class="slide-pop-box" v-if="outsideShow">
                <div class="bg-click" @click="changeShowStatus(1)"></div>
                <div class="slide-pop-model slide-pop-animated" :class="animatedClass">
                    <i class="slide-pop-close el-icon-ssq-delete" @click="changeShowStatus(0)"></i>
                    <slot name="slide-pop-content"></slot>
                </div>
            </div>
        </template>
        <template v-else>
            <div class="slide-pop-box" v-show="outsideShow">
                <div class="bg-click" @click="changeShowStatus(1)"></div>
                <div class="slide-pop-model slide-pop-animated" :class="animatedClass">
                    <i class="slide-pop-close el-icon-ssq-delete" @click="changeShowStatus(0)"></i>
                    <slot name="slide-pop-content"></slot>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
export default {
    props: {
        outsideShow: {
            type: Boolean,
        },
        needReset: { // :needReset="1",强制每次show的时候重置，:needReset="0"，根据关闭方式来判断是否需要重置
            type: Number,
            default: 0,
        },
    },
    data() {
        return {
            reset: true,
            animatedClass: 'fadeInRight',
        };
    },
    watch: {
        outsideShow: function() { // v-show
            if (this.outsideShow) {
                this.animatedClass = 'fadeInRight';
            } else {
                this.animatedClass = 'fadeOutRight';
            }
        },
        needReset: function(val) {
            // eslint-disable-next-line eqeqeq
            if (val == 0) {
                this.reset = false;
            } else {
                this.reset = true;
            }
        },
    },
    methods: {
        changeShowStatus(f) {
            this.animatedClass = 'fadeOutRight';

            // eslint-disable-next-line eqeqeq
            if (this.needReset == 0) {
                if (f === 1) {
                    this.reset = false;
                } else {
                    this.reset = true;
                }
            } else {
                this.reset = true;
            }

            setTimeout(() => {
                this.$emit('update:outsideShow', !this.outsideShow);
            }, 200);
        },
    },
};
</script>
<style lang="scss">
.slide-pop{
    .slide-pop-box {
		position: fixed;
		top: 0;
		left: 0;
		width: 100%;
		height: 100%;
		z-index: 1111;
		.bg-click {
			width: 100%;
			height: 100%;
		}
	}
	.slide-pop-animated {
		animation-duration: .3s;
		animation-fill-mode: both;
	}

	.slide-pop-model {
		position: fixed;
		top: $header-height;
		bottom: 35px;
		right: 0;
		width: 300px;
		background-color: $--color-white;
		border-left: 1px solid $--border-color-light;
		box-shadow: -5px 0 5px -3px #e8e8e8;
		animation-duration: .3s;
		z-index: 1;
		overflow-y: auto;
		box-shadow: none\9;

		i.slide-pop-close {
			position: absolute;
			top: 19px;
			right: $left-padding;
			font-size: 17px;
			color: $--color-text-placeholder;
			cursor: pointer;
			z-index: 1;
			&:hover {
				color: $--color-text-regular;
			}
		}
		.slide-pop-head {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			padding: 0 $left-padding;
			height: 55px;
			line-height: 55px;
			border-bottom: 1px solid $border-color;
			font-size: 14px;

			h1{
				font-size: 14px;
				font-weight: bold;
			}
		}
		.slide-pop-body {
            position: absolute;
			top: 55px;
			bottom: 120px;
			width: 100%;
			overflow-y: scroll;
			padding-bottom: 0px;
			font-size: 14px;
			color: $--color-black;

			.slide-pop-body-section{
				margin-bottom: 18px;
				.slide-pop-body-section-title{
					margin-bottom: 10px;
					font-size: 12px;
				}
			}
		}
		.slide-pop-foot {
			position: absolute;
			bottom: 0;
			right: 0;
			width: 100%;
			height: 80px;
			text-align: center;
			background-color: $--background-color-regular;
			line-height: 80px;
		}
        [dir="rtl"] & {
            right: auto;
            left: -300px;
            border-right: 1px solid $--border-color-light;
            border-left: none;
            box-shadow: 5px 0 5px -3px #e8e8e8;
            .slide-pop-close {
                right: auto;
                left: $left-padding;
            }
        }
    }

    @-webkit-keyframes fadeInRight {
        0% {
            opacity: 0;
            -webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0);
        }

        to {
            opacity: 1;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    }

    @keyframes fadeInLeft {
        0% {
            opacity: 0;
            -webkit-transform: translateZ(0);
            transform: translateZ(0)
        }

        to {
            opacity: 1;
            -webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0)
        }
    }
    @keyframes fadeInRight {
        0% {
            opacity: 0;
            -webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0);
        }

        to {
            opacity: 1;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }
    }
    .fadeInRight {
        -webkit-animation-name: fadeInRight;
        animation-name: fadeInRight;
        [dir=rtl] & {
            -webkit-animation-name: fadeInLeft;
            animation-name: fadeInLeft;
        }
    }

    @-webkit-keyframes fadeOutRight {
        0% {
            opacity: 1
        }

        to {
            opacity: 0;
            -webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0)
        }
    }

    @keyframes fadeOutRight {
        0% {
            opacity: 1
        }

        to {
            opacity: 0;
            -webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0)
        }
    }
    @keyframes fadeOutLeft {
        0% {
            opacity: 1;
            -webkit-transform: translate3d(100%,0,0);
            transform: translate3d(100%,0,0)
        }

        to {
            opacity: 0;
        }
    }

    .fadeOutRight {
        -webkit-animation-name: fadeOutRight;
        animation-name: fadeOutRight;
        [dir=rtl] & {
            -webkit-animation-name: fadeOutLeft;
            animation-name: fadeOutLeft;
        }
    }
}

</style>
