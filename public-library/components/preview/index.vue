<template>
    <div
        class="preview-component"
        :class="{'isPhone-con': !isPC}"
        v-if="value"
    >
        <div class="preview-component__head">
            <span class="preview-component__close el-icon-ssq--bs-guanbi cur-pointer" @click="close"></span>
        </div>
        <div class="preview-component__wapper">
            <h4 class="preview-component__title">
                {{ title }}
            </h4>
            <div class="preview-component__content">
                <slot name="previewContent"></slot>
            </div>
        </div>
    </div>
</template>

<script>
import { isPC } from 'pub-utils/device.js';
export default {
    props: {
        title: {
            type: String,
            default: '',
        },
        value: {
            type: Boolean,
            default: false,
        },
    },
    data() {
        return {
            isPC: isPC(),
        };
    },
    methods: {
        close() {
            this.$emit('input', false);
        },
    },
};
</script>
<style lang="scss">
    .preview-component{
        position: fixed;
        left: 0;
        top: 0;
        width: 100%;
        height: 100%;
        overflow-y: scroll;
        background: rgba(0, 0, 0, 0.5);
        z-index: 9999;
        &__head{
            position: absolute;
            height: 60px;
            width: 100%;
            min-width: 1000px;
            background: $--color-black;
        }
        &__close{
            position: absolute;
            top: 20px;
            right: 30px;
            color: $--color-white;
        }
        &__wapper{
            width: 1000px;
            margin: 60px auto 0;
            background: $background-color-dark;
            border-radius: 4px;
            text-align: center;
        }
        &__title{
            height: 86px;
            line-height: 86px;
            font-size: 22px;
            color: $--color-text-regular;
            border-bottom: 1px solid $--border-color-light;
        }
        &__content{
            padding: 50px 80px;
        }
        &.isPhone-con {
            .preview-component__head{
                min-width: 300px;
            }
            .preview-component__wapper {
                width: 90%;
                .preview-component__content {
                    padding: 20px 25px;
                    img {
                        width: 100%;
                    }
                }
            }
        }
    }
</style>
