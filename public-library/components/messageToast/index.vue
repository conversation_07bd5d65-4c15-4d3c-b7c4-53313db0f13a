<!-- 通用组件：toast提示 -->
<!-- 引用位置 基本都有用-->
<template>
    <transition
        enter-active-class="message-toast-animated fadeIn"
        leave-active-class="message-toast-animated fadeOut"
    >
        <div
            v-if="visible"
            class="message-toast"
        >
            <i
                v-if="iconVisible"
                :class="computedClass"
            >
            </i>
            <p>{{ message }}</p>
        </div>
    </transition>
</template>

<script type="text/babel">
export default {
    data() {
        return {
            message: '',
            iconClass: '',
            type: null,
            visible: true,
            duration: 2500,
            closed: false,
            timer: null,
            dispatch: null,
            callBack: () => {},
        };
    },

    computed: {
        iconVisible() {
            return (this.iconClass !== '' || this.type !== null);
        },
        computedClass() {
            let iconClassName = '';

            if (this.type !== null) {
                switch (this.type) {
                    case 'success':
                        iconClassName = 'el-icon-ssq-qianyuewancheng';
                        break;
                    case 'info':
                        iconClassName = 'el-icon-ssq-tishi2';
                        break;
                    case 'warning':
                    case 'error':
                        iconClassName = 'el-icon-ssq-jingshi';
                        break;
                }
            }

            return (this.iconClass !== '' ? this.iconClass : iconClassName);
        },
    },

    watch: {
        closed(newVal) {
            if (newVal) {
                this.visible = false;
                this.$el.addEventListener('transitionend', this.destroyElement);
                this.dispatch('resolve');
            }
        },
    },

    methods: {
        destroyElement() {
            this.$el.removeEventListener('transitionend', this.destroyElement);
            this.$destroy(true);
            this.$el.parentNode.removeChild(this.$el);
        },

        clearTimer() {
            clearTimeout(this.timer);
        },

        startTimer() {
            if (this.duration > 0) {
                this.timer = setTimeout(() => {
                    if (!this.closed) {
                        this.closed = true;
                        if (this.callBack && typeof this.callBack === 'function') {
                            this.callBack();
                        }
                    }
                }, this.duration);
            }
        },
    },

    mounted() {
        this.startTimer();
    },
};
</script>
<style lang="scss">
  .message-toast-animated {
    animation-duration: .3s;
    animation-fill-mode: both;
  }
  .message-toast {
    box-sizing: border-box;
    z-index: 20000;
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translateX(-50%) translateY(-65%);
    background: rgba(0,0,0,.7);
    min-width: 156px;
    padding: 20px 36px;
    border-radius: 4px;
    color: $--color-white;
    text-align: center;
    i {
      margin-bottom: 15px;
      font-size: 33px;
    }
    p {
      font-size: 14px;
    }
  }
</style>
