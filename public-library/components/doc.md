# countDown组件

#### 倒计时组件

##### props说明：

|   参数    |   类型   |      说明      | 默认值 | 是否必填 |
| :-------: | :------: | :------------: | :----: | :------: |
|   isWap   | Boolean  |   是否手机端   | false  |          |
|  second   |  Number  |    相隔时间    |   60   |          |
| disabled  | Boolean  |   是否可点击   | false  |          |
| clickedFn | Function |  按钮点击回调  |        |   true   |
|  runFlag  | Boolean  | 是否开始倒计时 | false  |          |

# elIDelete组件

#### input内删除按钮

##### 使用说明

```vue
<el-input v-model="inputvalue">
    <ElIDelete slot="suffix"></ElIDelete>
</el-input>
```

# elIEye组件

#### input内密码显示隐藏按钮

##### 使用说明

```vue
<el-input type="password" v-model="input">
    <ElIEye slot="suffix"></ElIEye>
</el-input>
```

## messageToast组件

#### toast提示

##### 使用说明

```vue
 this.$MessageToast('');
 this.$MessageToast.success('');
 this.$MessageToast.warning('');
 this.$MessageToast.info('');
 this.$MessageToast.error('');
 this.$MessageToast({
    message: '修改成功',
    type: 'success',
    iconClass: '',  // icon class
    duration: 2500,
    callBack: () => {} // 关闭后回调方法
 }).then(()=>{

 })
```

# slidePop组件

#### 侧边弹出框组件

##### props说明：

|   参数    |   类型   |      说明      | 默认值 | 是否必填 |
| :-------: | :------: | :------------: | :----: | :------: |
|   isWap   | Boolean  |   是否手机端   | false  |          |
|  second   |  Number  |    相隔时间    |   60   |          |
| disabled  | Boolean  |   是否可点击   | false  |          |
| clickedFn | Function |  按钮点击回调  |        |   true   |
|  runFlag  | Boolean  | 是否开始倒计时 | false  |          |


