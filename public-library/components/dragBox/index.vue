<template>
    <div class="drag-box" ref="a">
        <div class="drag-item" :style="{ width: aWidth + 'px' }">
            <slot name="item1"></slot>
        </div>
        <div class="drag-line" @mousedown="startDragging('left', $event)" ref="line"></div>
        <div class="drag-item" :style="{ width: bWidth + 'px' }">
            <slot name="item2"></slot>
        </div>
        <div class="drag-line" @mousedown="startDragging('right', $event)" ref="line"></div>
        <div class="drag-item" :style="{ width: cWidth + 'px' }">
            <slot name="item3"></slot>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        width1: {
            type: Number,
            default: 300,
        },
        width2: {
            type: Number,
            default: 300,
        },
        width3: {
            type: Number,
            default: 300,
        },
        minWidth: {
            type: Number,
            default: 100,
        },
    },
    data() {
        return {
            isDragging: false,
            startLeftX: 0,
            startRightX: 0,
            aWidth: this.width1,
            bWidth: this.width2,
            cWidth: this.width3,
            draggingType: 'left',
        };
    },
    methods: {
        startDragging(type, e) {
            this.isDragging = true;
            this.draggingType = type;
            if (type === 'left') {
                this.startLeftX = e.clientX;
            } else {
                this.startRightX = e.clientX;
            }
            document.addEventListener('mousemove', this.handleMouseMove);
            document.addEventListener('mouseup', this.stopDragging);
        },
        handleMouseMove(e) {
            if (this.isDragging) {
                if (this.draggingType === 'left') {
                    const distance = e.clientX - this.startLeftX;
                    if (distance > 0) {
                        this.aWidth += distance;
                        if (this.bWidth > this.minWidth) {
                            this.bWidth -= distance;
                        } else if (this.cWidth > this.minWidth) {
                            this.cWidth -= distance;
                        } else {
                            this.aWidth -= distance;
                        }
                    } else {
                        if (this.aWidth > this.minWidth) {
                            this.aWidth += distance;
                            this.bWidth -= distance;
                        }
                    }
                    this.startLeftX = e.clientX;
                } else {
                    const distance = e.clientX - this.startRightX;
                    if (distance < 0) {
                        this.cWidth -= distance;
                        if (this.bWidth > this.minWidth) {
                            this.bWidth += distance;
                        } else if (this.aWidth > this.minWidth) {
                            this.aWidth += distance;
                        } else {
                            this.cWidth += distance;
                        }
                    } else {
                        if (this.cWidth > this.minWidth) {
                            this.cWidth -= distance;
                            this.bWidth += distance;
                        }
                    }
                    this.startRightX = e.clientX;
                }
            }
        },
        stopDragging() {
            this.isDragging = false;
            const { aWidth, bWidth, cWidth } = this;
            this.$emit('change', { aWidth, bWidth, cWidth });
            document.removeEventListener('mousemove', this.handleMouseMove);
            document.removeEventListener('mouseup', this.stopDragging);
        },
    },
};
</script>

<style lang="scss">
.drag-box {
    width: 100%;
    display: flex;
    flex-direction: row;
    overflow: hidden;
}

.drag-item{
    overflow: hidden;
    * {
        user-select: none;
    }
}

.drag-line {
    width: 0px;
    border: 2px solid #eee;
    cursor: col-resize;
}
</style>
