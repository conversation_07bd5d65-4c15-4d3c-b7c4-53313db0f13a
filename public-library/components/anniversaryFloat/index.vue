<template>
    <div class="activity-icon" v-show="showActivityIcon">
        <div class="body">
            <img src="~pub-images/activityIcon.png" alt="上上签10周年电子合同套餐限时折扣" @click="toPage">
            <div class="close" @click="closeIcon">
                <i class="el-icon-ssq--bs-guanbi"></i>
            </div>
        </div>
    </div>
</template>

<script>
export default {
    data() {
        return {
            hasPermission: false,
            hasCheckPage: false,
            hasLogin: false,
        };
    },
    computed: {
        showActivityIcon() {
            return !this.isRootPath && this.checkValid && !this.hasCheckPage && (!this.hasLogin || this.hasPermission);
        },
        checkValid() {
            const endTime = new Date('2025-01-20 18:00:00').getTime();
            const startTime = new Date('2024-12-12 00:00:00').getTime();
            const currentTime = new Date().getTime();
            const ent = location.href.includes('.info') || location.href.includes('.tech');
            if (ent) {
                return false;
            } else if (currentTime >= startTime && currentTime <= endTime) {
                return true;
            } else {
                return false;
            }
        },
        isRootPath() {
            return this.$route.path === '/';
        },
    },
    watch: {
        '$route': {
            handler(val) {
                this.$set(this, 'hasLogin', val.path !== '/' && !val.meta?.noLogin);
                // 在这里处理路由变化逻辑
                if (this.hasLogin) {
                    this.getChecked();
                    this.getPermissions();
                } else {
                    this.$set(this, 'hasPermission', false);
                    this.$set(this, 'hasCheckPage', false);
                }
            },
            deep: true,
            immediate: true,
        },
    },
    methods: {
        toPage() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    activity_id: '10th',
                    activity_name: '十周年活动',
                    first_category: '悬浮框',
                    icon_name: '活动入口',
                },
            });
            if (this.hasLogin) {
                if (this.$store.getters.getUserType === 'Enterprise') {
                    this.$router.push('/console/enterprise/recharge?utm_campaign=10th&utm_medium=float_button&_channel_track_key=ihsVA0Mi&utm_source=ent');
                } else {
                    this.$router.push('/usercenter/recharge?utm_campaign=10th&utm_medium=float_button&_channel_track_key=gjS6NOll&utm_source=ent');
                }
            } else {
                location.href = 'https://www.bestsign.cn/product-chongzhi';
            }
        },
        getPermissions() {
            this.$http.get('/ents/query-anniversary-ent').then(({ data: { isChannel, pmsVersionName, isGroupMember, isICBC, hasChargeM } }) => {
                const version = pmsVersionName.includes('标准版');
                this.hasPermission = !isChannel && version && !isGroupMember && !isICBC && hasChargeM;
            });
        },
        getChecked() {
            this.$http.get('/users/configs/YEAR_END_GUIDE').then(res => {
                if (res.data) {
                    this.hasCheckPage = res.data.value === 'true';
                } else {
                    this.hasCheckPage = false;
                }
            });
        },
        closeIcon() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    activity_id: '10th',
                    activity_name: '十周年活动',
                    first_category: '悬浮框',
                    icon_name: '关闭',
                },
            });
            if (this.hasLogin) {
                this.$http.post('/users/configs/YEAR_END_GUIDE', {
                    name: 'YEAR_END_GUIDE',
                    value: 'true',
                }).then(() => {
                    this.hasCheckPage = true;
                });
            } else {
                this.hasCheckPage = true;
            }
        },
    },
};
</script>
<style lang="scss" scoped>
.activity-icon {
    position: fixed;
    right: 5px;
    top: 200px;
    width: 120px;
    z-index: 99;
    .body {
        position: relative;
        height: 100%;
        width: 100%;
        img {
          width: 100%;
          height: 100%;
          cursor: pointer;
          margin-bottom: 6px;
        }
        .close {
            border: 1px solid #000;
            border-radius: 20px;
            width: 20px;
            height: 20px;
            margin: auto;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            .el-icon-ssq--bs-guanbi {
              color: #000;
              font-size: 8px;
              margin-left: 1px;
            }
        }
    }
}
</style>
