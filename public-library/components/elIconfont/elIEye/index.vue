<!-- 通用组件：input内密码显示隐藏按钮 -->
<!-- 引用位置 用户中心组件：AccountPopChangeAccount,AccountPopChangeInform,AccountPopChangePassword,AccountPopResetName,AccountPopResetPassword,AccountPopsetPassword,AccountPopsetSignnpass-->
<template>
    <i class="iconfont el-input__icon i-eye el-icon-ssq-eye-filling" :class="isShow?'active':''" @click="handleIconClick"></i>
</template>

<script>
// 和element深度耦合，慎用
export default {
    data() {
        return {
            isShow: 0,
        };
    },
    methods: {
        handleIconClick: function() {
            const type = this.$parent.$refs.input.type;
            this.isShow = !this.isShow;
            this.$parent.$refs.input.type = type === 'password' ? 'text' : 'password';
        },
    },
};
</script>

<style lang="scss">
	.i-eye {
		font-size: 20px !important;
        color: $--color-text-placeholder;
        &.active {
            color: $--color-primary-light-2;
        }
	}
</style>
