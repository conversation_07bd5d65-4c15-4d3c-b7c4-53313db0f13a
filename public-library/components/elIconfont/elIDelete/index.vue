<!-- 通用组件：input框内删除按钮 -->
<!-- 引用位置 用户中心组件：AccountPopChangeAccount,AccountPopChangeInform-->
<template>
    <i class="iconfont el-icon-ssq-delete el-input__icon" v-if="isShow" @click="handleIconClick"></i>
</template>

<script>
// 和element深度耦合，慎用
export default {
    props: {
        selfClick: {
            default: false,
            type: Boolean,
        },
    },
    computed: {
        isShow: function() {
            return this.$parent.value;
        },
    },
    methods: {
        handleIconClick: function() {
            if (this.selfClick) {
                this.$emit('click', this);
            } else {
                this.$parent.$emit('input', '');
            }
        },
    },
};
</script>
