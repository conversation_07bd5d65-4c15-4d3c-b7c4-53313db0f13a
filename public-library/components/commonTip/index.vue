<!-- 通用tooltip，popover组件, 统一使用content，reference插槽 -->
<template>
    <component
        :popper-class="popperClass"
        :is="currentComponent"
        v-bind="allProps"
        @show="$emit('show')"
        @hide="$emit('hide')"
        @after-enter="$emit('after-enter')"
        @after-leave="$emit('after-leave')"
    >
        <template v-if="type === 'tooltip'">
            <!-- 如果有reference的slot内容就使用 -->
            <slot v-if="hasReferenceSlot" name="reference"></slot>
            <!-- 否则使用默认的i标签 -->
            <i
                v-else
                :class="['el-icon-ssq-bangzhu', 'common-tip-icon', iconClass]"
            ></i>
            <!-- 如果有props的content内容，就不使用slot的content -->
            <div v-if="!content" slot="content"><slot name="content"></slot></div>
        </template>
        <template v-else>
            <!-- 如果有reference的slot内容就使用 -->
            <slot v-if="hasReferenceSlot" slot="reference" name="reference"></slot>
            <!-- 否则使用默认的i标签 -->
            <i
                v-else
                slot="reference"
                :class="['el-icon-ssq-wenhao', 'common-tip-icon', 'common-tip-popover', iconClass]"
            ></i>
            <!-- 如果有props的content内容，就不使用slot的content -->
            <slot v-if="!content"><slot name="content"></slot></slot>
        </template>
    </component>
</template>

<script>
export default {
    name: 'CommonTip',
    props: {
        type: {
            default: 'tooltip', // tooltip | popover
            type: String,
        },
        iconClass: {
            default: '',
            type: String,
        },
        content: {
            default: '',
            type: String,
        },
    },
    computed: {
        hasReferenceSlot() {
            return !!this.$slots.reference;
        },
        currentComponent() {
            return `el-${this.type}`;
        },
        allProps() {
            return {
                ...this.$props,
                ...this.$attrs,
            };
        },
        popperClass() {
            const popperClass = this.$attrs.popperClass || this.$attrs['popper-class'] || '';
            if (this.allProps.title) {
                return `common-tip common-tip-has-title ${popperClass}`;
            }
            return `common-tip ${popperClass}`;
        },
    },
};

</script>

<style lang="scss">
.common-tip.el-tooltip {
    max-width: 400px;
}
.common-tip-has-title {
    padding: 0 0 30px;
    .el-popover__title {
        height: 50px;
        padding: 0 30px;
        margin-bottom: 20px;
        border-bottom: 1px solid $text-color-lighter;
        font-size: 16px;
        line-height: 50px;
    }
}
.common-tip-icon {
    font-size: 12px;
    cursor: pointer;
}
.common-tip-popover {
    &:hover {
        color: $theme-color;
    }
}
</style>
