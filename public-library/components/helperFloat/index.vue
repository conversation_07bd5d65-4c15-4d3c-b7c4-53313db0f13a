<!-- 通用组件：帮助图标悬浮窗 -->
<!-- 引用位置：基本全部页面 -->
<template>
    <div>
        <div class="HelperFloat-cpn hidden">
            <div class="helper-hidden" @click="toggleFloat"><!-- 隐藏 -->{{ $t('helperFloat.hide') }}</div>
            <div class="helper" @click="feedback">
                <div class="helper-wrap">
                    <img src="~pub-images/helper/suggestion.png">
                    <div class="helper-title"><!-- 提建议 -->{{ $t('helperFloat.suggest') }}</div>
                </div>
            </div>
            <div class="helper" @click="service">
                <div class="helper-wrap">
                    <img src="~pub-images/helper/server.png">
                    <div class="helper-title"><!-- 在线客服 -->{{ $t('helperFloat.onlineService') }}</div>
                </div>
            </div>
            <div v-if="isSupportLogReport" class="helper" @click="reportLog">
                <div class="helper-wrap">
                    <i class="el-icon-ssq-nimingtiyi"></i>
                    <div class="helper-title">{{ $t('helperFloat.reportLog') }}</div>
                </div>
            </div>
        </div>
        <div class="HelperFloat-cpn-hidden" :class="{'HelperFloat-cpn-hidden_en' : isEn}" @click="toggleFloat">{{ $t('helperFloat.advice') }}</div>
        <div class="iframe" v-if="feedbackVisible" :class="{open: feedbackVisible}">
            <div class="close" @click="closeFeedback">
                <i class="el-icon-ssq-jian"></i>
            </div>
            <iframe :src="feedbackSrc" frameborder="0"></iframe>
        </div>
        <div class="iframe service" v-if="serviceVisible" :class="{open: serviceVisible, 'is-enlarge': isEnlarge}">
            <div class="close" @click="closeFeedback">
                <span><!-- 最小化 -->{{ $t('helperFloat.minimize') }}</span>
                <i class="el-icon-ssq-jian"></i>
            </div>
            <div class="enlarge" @click="handleEnlarge">
                <span><!-- 点击放大 -->{{ $t(`helperFloat.${isEnlarge ? 'zoomOut' : 'enlarge'}`) }}</span>
                <i class="el-icon-full-screen"></i>
            </div>
            <iframe :src="serviceSrc" frameborder="0"></iframe>
        </div>
    </div>

</template>

<script>
import { mapGetters } from 'vuex';
import sha1 from 'js-sha1';
import Qs from 'qs';
import { customAliErrorReport, isProjectSupportLogReport } from 'pub-utils/aliErrorReport.js';
// import { Random } from 'random-js';
const Random = require('random-js');
export default {
    data() {
        return {
            visible: false,
            feedbackVisible: false,
            serviceVisible: false,
            isEnlarge: false,
        };
    },
    computed: {
        ...mapGetters(['getUserType', 'console_getEntName', 'getUserFullName', 'getUserAccount']),
        feedbackSrc() {
            // const host = window.location.host === 'ent.bestsign.cn' ? 'www.bestsign.cn' : 'www.bestsign.info';
            const host = this.GLOBAL.ENV_NAME === 'PE_ENV' ? 'www.bestsign.cn' : 'www.bestsign.info';
            return  `${window.location.protocol}//${host}/udesk/feedback.html`;
        },
        serviceSrc() {
            var userkey = 'f25b205a263df814b042c0ddaf72b11e';
            var random = new Random();
            var nonce = random.string(16);
            var timestamp = Date.parse(new Date());
            var web_token = this.getUserAccount;
            var sign_str = 'nonce=' + nonce + '&timestamp=' + timestamp + '&web_token=' + web_token + '&' + userkey;
            var signature = sha1(sign_str).toUpperCase();
            var type = this.getUserType === 'Person' ? '个人' : '企业';
            var mainbody = '';
            if (this.getUserType === 'Person') {
                mainbody = this.getUserFullName ? this.getUserFullName : this.getUserAccount;
            } else {
                mainbody = this.console_getEntName ? this.console_getEntName : this.getUserAccount;
            }
            var query = Qs.stringify({
                'nonce': nonce,
                'timestamp': timestamp,
                'web_token': web_token,
                'signature': signature,
                'c_cf_用户登录账号': web_token,
                'c_cf_用户类型': type,
                'c_cf_认证主体名': mainbody,
                'c_cf_用户咨询来源': 'PC',
            });
            return '//bestsign.udesk.cn/im_client/?web_plugin_id=57144?' + query;
        },
        isEn() {
            return this.$i18n.locale === 'en';
        },
        // 是否支持日志上报功能
        isSupportLogReport() {
            return isProjectSupportLogReport();
        },
    },
    methods: {
        // 上报日志
        reportLog() {
            customAliErrorReport({ networkData: this.$store.state.reportHttpData });
            this.toggleFloat();
        },
        getActivatedMenu() {
            return document.querySelector('.common-header-nav__list li.nav__li-active')?.innerText || location.pathname;
        },
        service() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.getActivatedMenu(),
                    first_category: '悬浮框',
                    icon_name: '在线客服',
                },
            });
            this.serviceVisible = true;
        },
        feedback() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.getActivatedMenu(),
                    first_category: '悬浮框',
                    icon_name: '提建议',
                },
            });
            this.feedbackVisible = true;
        },
        closeFeedback() {
            this.feedbackVisible = false;
            this.serviceVisible = false;
            this.isEnlarge = false;
        },
        // 放大/缩小
        handleEnlarge() {
            this.isEnlarge = !this.isEnlarge;
        },
        toggleFloat() {
            this.$sensors && this.$sensors.track({
                eventName: 'Ent_Common_BtnClick',
                eventProperty: {
                    page_name: this.getActivatedMenu(),
                    first_category: '悬浮框',
                    icon_name: '咨询建议',
                },
            });
            const float = document.querySelectorAll('.HelperFloat-cpn');
            const aside = document.querySelectorAll('.HelperFloat-cpn-hidden');
            Array.prototype.forEach.call(float, function(item) {
                item.classList.toggle('hidden');
            });
            Array.prototype.forEach.call(aside, function(item) {
                item.classList.toggle('hidden');
            });
        },
    },
};
</script>
<style lang="scss">

    .HelperFloat-cpn {
        position: fixed;
        bottom: 200px;
        right: 35px;
        z-index: 99999;
        transition: all .5s ease;
        &.hidden {
            transform: translateX(200px);
        }
        * {
            box-sizing: border-box;
        }
        .helper-hidden {
            width: 57px;
            height: 15px;
            line-height: 15px;
            font-size: 10px;
            background-color: $background-color-gray;
            color: $--color-white;
            text-align: center;
            margin-bottom: 3px;
            cursor: pointer;
        }
        .helper {
            width: 57px;
            height: 57px;
            padding: 5px 0;
            cursor: pointer;
            overflow: hidden;
            margin-bottom: 6px;
            background-color: $--color-primary;
            text-align: center;
            .helper-title {
                font-size: 10px;
                line-height: 18px;
                text-align: center;
                color: $--color-white;
                padding-top: 5px;
            }
            img {
                width: 20px;
            }
        }
    }
    .HelperFloat-cpn-hidden {
        word-break: break-all;
		position: fixed;
		right: 0;
		bottom: 250px;
		width: 20px;
		font-size: 10px;
		background-color: $--color-primary;
		color: $--color-white;
		text-align: center;
		padding: 3px 2px;
		cursor: pointer;
		border-radius: 5px 0 0 5px;
		transition: all .5s ease;
		z-index: 99999;
		box-sizing: border-box;
		&.hidden {
			transform: translateX(100px);
		}
	}
	.iframe {
		position: fixed;
		right: 60px;
		bottom: 0;
		width: 360px;
		height: 480px;
		background-color: $--background-color-regular;
		box-shadow: rgba(0, 0, 0, 0.15) 0px 0px 20px 0px;
		transition: all .5s ease;
		transform: translateY(100%);
		z-index: 100000;
		iframe {
			width: 100%;
			height: 100%;
		}
		.close, .enlarge {
			position: absolute;
			right: 12px;
			top: 0;
			font-size: 16px;
			cursor: pointer;
			padding: 10px;
			color: $--color-black;
            span {
                font-size: 14px;
            }
		}
		&.open, &.enlarge {
			transform: translateY(0);
		}
		&.service {
			.close, .enlarge {
				color: $--color-white;
				right: 125px;
				top: 9px;
			}
            .enlarge{
                right: 70px;
                i{
                    vertical-align: text-bottom;
                    margin: 0 0 0 2px;
                    font-weight: bold;
                }
            }
		}
    }
    .is-enlarge{
        width: 500px;
        height: 572px;
    }
    .el-icon-ssq-nimingtiyi{
        color: white;
    }
    .HelperFloat-cpn-hidden.HelperFloat-cpn-hidden_en {
        width: 40px;
        height: 20px;
        line-height: 20px;
        padding: 0;
    }
</style>
