import store from 'src/store';
import Vue from 'vue';
import i18n from 'src/lang';
import VueCookie from 'vue-cookie';
import { isServer } from '../plugins/cookie/index';
import PasswordExpiration from 'pub-businessComponents/dialog/PasswordExpiration/install.js';
// 获取企业品牌状态
export function getEntBrand() {
    const WHITE_HOSTS = ['molyguide'];
    // 获取企业品牌状态
    Vue.$http.get('/users/ignore/ent-brand/check').then(res => {
        /* 宏祥科技使用反代理，如果不在这里进行处理，会把客户的host改掉引起错误 */
        const hostArr = location.host.split('.');
        if (res.data.value + '' === '1' && !WHITE_HOSTS.includes(hostArr[1])) {
            location.href = location.href.replace(hostArr[0], 'ent');
        }
        if (res.data.value + '' !== '3') {
            Vue.$cookie.delete('isBrand');
            Vue.$cookie.delete('sino-russia');
            Vue.$cookie.delete('homeLogoFileId');
        } else {
            Vue.$cookie.set('isBrand', 1);
        }

        if (res.data.time) {
            Vue.$cookie.set('copyRightRange', res.data.time);
        } else {
            Vue.$cookie.delete('copyRightRange');
        }
    });
}

function handleLanguage(lang) {
    // 配置用户语言偏好
    if (typeof lang === 'string' && !store.state.noLoginLanguage) {
        i18n.locale = lang;
        VueCookie.set('language', lang, { secure: isServer() ? true : null }); // 配置cookie内默认的语言偏好，后端要用
    } else {
        // 如果用户在没有登录的时候切换了语言，head-info的VIEW_LANGUAGE为null，但是此时要把cookie的语言类型传给后端
        let targetLang = VueCookie.get('language');
        if (store.state.noLoginLanguage) {
        // 如果用户在未登录页面人为切换了语言，需要更新
            targetLang = store.state.noLoginLanguage;
            store.commit('resetNoLoginLanguage');
        }
        Vue.$http.post('/users/configs/VIEW_LANGUAGE', {
            name: 'VIEW_LANGUAGE',
            value: targetLang,
        }).then(() => {
        // location.reload(); // 部分文案在代码逻辑中确定，直接修改locale逻辑不会重新执行，刷新界面
        });
    }
}

/**
 * next为路由导航守卫beforeEach中的next函数
 * SAAS-31983 登录token失效后，重新刷新页面出现白屏现象
 */
export function getHeadInfoData() {
    // 获取commonheader数据
    return Vue.$http.get('/users/head-info')
        .then(async res => {
            if (res && res.data) {
                if (res.data.weekPasswordFlag && Vue.localStorage.get('isPwdLogin')) {
                    // saas-8438 密码复杂度过低要求先修改密码
                    let exist = false;
                    document.body.childNodes.forEach(node => {
                        node.className === 'reset-password' && (exist = true);
                    });
                    !exist && Vue.$resetPassword();
                }
                handleLanguage(res.data.VIEW_LANGUAGE);
                // saml协议的sso用户，登出时根据samlNameId获取第三方登出链接
                if (res.data.samlNameId) {
                    Vue.$cookie.set('samlNameId', res.data.samlNameId);
                } else {
                    Vue.$cookie.delete('samlNameId');
                }
                store.commit('updateCurrentEntId', res.data.currentEntId); // 更新当前主体entId到store
                const personNormalLogin = res.data.userType === 'Person' && !res.data.extendFields.isLoginFromDeveloper; // 普通登录的个人用户不需要请求配置
                const configData = await Promise.all([
                    Vue.$http.headerInfoConfig(res.data).then(() => res.data),
                    personNormalLogin ? {} : Vue.$http.getPageConfig(res.data), // 获取乐高成、单点登录的配置
                ]);
                if (res.data.remindChangePasswordFlag && location.pathname !== '/usercenter/account' && store.getters.checkFeat.forceReplaceExpiredPassword) {
                    // SAAS-30449 强制更换过期密码
                    PasswordExpiration.install();
                }
                return configData;
            } else {
                return Promise.reject([{}, {}, {}, {}]);
            }
        });
}

export function handlePwdLoginFlag(to, next) {
    const query = to.query;
    query.isPwdLogin === 'true' && (Vue.localStorage.set('isPwdLogin', true));
    delete query.isPwdLogin;
    next({ path: to.path, query });
}
