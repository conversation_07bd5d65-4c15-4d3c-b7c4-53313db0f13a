import Vue from 'vue';
import { loadScript } from '../utils';
import { isEntTestInfoEnv } from '../utils/regsUtils.js';

import {
    getEntBrand,
    getHeadInfoData,
    handlePwdLoginFlag,
} from './businessFunctions';
import { joinPathnameAndQueryObj } from '../utils/getQueryString';
import i18n from 'src/lang';
import store from 'src/store';

// 设置浏览器title
export function setTitle(title, meta) {
    // title可配置为函数，动态设置标题
    if (typeof title === 'function') {
        title = title();
    }
    const isQyWx = window.sessionStorage.getItem('isQyWx');
    if (isQyWx) {
        document.title = title ? `${title}` : i18n.t('commonNomal.ssq'); // 上上签
    } else {
        let commonTitle = isEntTestInfoEnv() ? i18n.t('commonNomal.ssqTestPlatform') : i18n.t('commonNomal.ssqPlatform'); // 上上签电子签约云平台
        if (meta && meta.noLogin && store.getters.getIsForeignVersion) {
            if (store.getters.getIsJa) {
                commonTitle = 'ベストサイン電子契約クラウドプラットフォーム';
            } else if (store.getters.getIsUae) {
                commonTitle = 'منصة بست ساين للتوقيع الإلكتروني السحابي';
            }
        }
        if (i18n.locale === 'en' || store.getters.getIsForeignVersion) { // SAAS-30574要求英文环境下统一，不显示模块名称
            document.title = commonTitle;
        } else {
            document.title = title ? `${title} - ${commonTitle}` : commonTitle;
        }
    }
}
// 生产环境安装和统计[对接百度统计]，其余排除
export const installTj = async() => {
    if (window.location.host.indexOf('cn') > -1) {
        try {
            await loadScript('https://hm.baidu.com/hm.js?1f46ec8ab8bbb8a41ddac8ef894ec63a');
            window._hmt && window._hmt.push(['_trackPageview', location.pathname + location.search]);
        } catch (err) {
            console.log('load js error');
        }
    }
};

// 切换导航栏偶尔报错'Loading chunk *** failed'
export function routerOnErrorFun(error) {
    const pattern = /Loading chunk.+.failed/g;
    const isChunkLoadFailed = error.message.match(pattern);
    if (isChunkLoadFailed) {
        Vue.$MessageToast.info(i18n.t('commonNomal.pageExpiredTip')); // 页面已过期，请刷新重试
    }
}

/**
 * 覆写VueRouter push repalce，支持传入参数location为完整的url地址【使用原生location跳转】
 * 原因：新版签署迁移到新项目，参数跳转方式支持
 * 参考VueRouter push repalce 写法 https://github.com/vuejs/vue-router/blob/dev/src/index.js
 * 有需要的可以自行引入
 */
export function routerPushFun(location, onComplete, onAbort) {
    const isStr = typeof location === 'string';
    const t = isStr ? location : location.path;
    if (/^http/.test(t)) {
        return window.location.href = isStr ? location : joinPathnameAndQueryObj(location.path, location.query);
    }
    this.history.push(location, onComplete, onAbort);
}
export function routerReplaceFun(location, onComplete, onAbort) {
    const isStr = typeof location === 'string';
    const t = isStr ? location : location.path;
    if (/^http/.test(t)) {
        return window.location.replace(isStr ? location : joinPathnameAndQueryObj(location.path, location.query));
    }
    this.history.replace(location, onComplete, onAbort);
}

let pageCount = 0; // 页面route统计
export async function publicBeforeEachHooks(to, from, next) {
    if (!to.matched.length) {
        return next({
            path: '/notFound',
        });
    }

    // 如果参数里有密码登录的状态，存到store里并去掉参数
    if (to.query.isPwdLogin) {
        return handlePwdLoginFlag(to, next);
    }

    ++pageCount;
    if (!process.env.NODE_ENV.includes('development') && process.env.NODE_ENV !== 'development-ccb' && pageCount === 1) {
        installTj();
        getEntBrand();
    }

    // to.meta.title string/function
    setTitle(to.meta.title, to.meta);

    const fromPathHasSsoinner = from.path.indexOf('/ssoinner') > -1;
    const toPathHasSsoinner = to.path.indexOf('/ssoinner') > -1;

    if (fromPathHasSsoinner || toPathHasSsoinner) {
        Vue.GLOBAL.rootPathName = '/ssoinner';
    }
    if (fromPathHasSsoinner && !toPathHasSsoinner) {
        next(`/ssoinner${to.fullPath}`);
    }
    if (to.path.includes('/app/')) {
        // /app/* 相关代码删除
        return location.replace(`/account-center/home`);
    }

    // 不需要登录的页面就继续跳转
    if (to.matched[to.matched.length - 1].meta.noLogin) {
        const needConfigList = ['/register', '/oauth/login']; // 需要获取单点登录页面配置信息
        const needConfig = needConfigList.some(item => {
            return to.fullPath.indexOf(item) > -1;
        });
        if (needConfig && to.query.clientId) {
            return Vue.$http.getNoLoginPageConfig(to.query.clientId).then(() => {
                next();
            });
        } else {
            return next();
        }
    }

    if (to.path.includes('login')) {
        next();
        return;
    }
    // 如果route配置了disableGetConfig: true参数且不是第一次打开页面，不读取接口参数
    if (to.meta && to.meta.disableGetConfig && pageCount > 1) {
        next();
        return;
    }
    await getHeadInfoData();
    next();
}
