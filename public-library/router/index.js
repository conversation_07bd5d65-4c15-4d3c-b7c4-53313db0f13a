export const publicRoutes = [
    {
        path: '/notFound',
        component: () => import(/* webpackChunkName: "error-page" */ 'pub-pages/error/NotFound.vue'),
        meta: {
            noLogin: true,
        },
    },
    {
        path: '/forbidden',
        component: () => import(/* webpackChunkName: "error-page" */ 'pub-pages/error/Forbidden.vue'),
        meta: {
            noLogin: true,
        },
    },
    // {
    //     path: '*',
    //     component: () => import(/* webpackChunkName: "error-page" */ 'pub-pages/error/NotFound.vue'),
    // },
];
