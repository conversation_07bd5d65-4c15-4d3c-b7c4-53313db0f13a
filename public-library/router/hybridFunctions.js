/* eslint-disable eqeqeq */
import LocalStorage from 'pub-plugins/localStorage';
import { CloseWebPage } from 'pub-utils/device.js';
import store from 'src/store';
import axios from 'axios';

// 如果是从混合云UI过来,保存token信息
export function handleHybridEnter(to, next) {
    const query = to.query;
    if (query.access_token && query.refresh_token) {
        Vue.$token.save(query.access_token, query.refresh_token);
        // 实名 抹平参数 transitionHref
        if (query.fromPage) {
            LocalStorage.set('transitionHref', query.fromPage);
        }
        delete query.access_token;
        delete query.refresh_token;
        delete query.fromPage;
        next({ path: to.path, query });
    }
}

// 判断当前为混合云主体时需要拦截路由
export function interceptorHybrid(to, next) {
    const newHybridBlackList = [
        '/doc/list',
        '/template',
        '/home',
        '/template/list',
    ];
    if (store.getters.getIsNewHybrid && newHybridBlackList.includes(to.path)) { // 不允许从新混合云ui过来的用户进入的页面
        // const fromMatch = from.matched[from.matched.length - 1].path;
        // const docListTo = ['/sign/signing', '/sign/preview', 'sign/sign-tip/face-sign-qrcode', 'doc/detail/:contractId']; // 签约管理进入的签署，详情页
        if (!store.state.isCurrentDomainLogin || window.sessionStorage.getItem('isCurrentDomainLogin') == 0) { // 从其他域名的签约管理列表跳转进入
            Vue.$token.delete();
            clearTimeout(axios.inLANInterval);
            store.commit('logout');
            // eslint-disable-next-line new-cap
            CloseWebPage(); // 关闭页面
            return false;
        } else {
            next('/account-center/login');
        }
    }
    return true;
}
