import { dateType, formatDateToString } from 'pub-utils/date.js';
import i18n from 'src/lang';

Vue.filter('timeToString', val => {
    if (!val) {
        return;
    }
    const type = dateType(val); // 今天0  昨天1  今年2  今年之前3  YYYY-MM-DD hh:mm:ss
    let str = '';
    switch (type) {
        case 0: str =  formatDateToString({ date: val, format: 'hh:mm' }); break;
        case 1: str = `${i18n.t('commonNomal.yesterday')} ${formatDateToString({ date: val, format: 'hh:mm' })}`; break;
        case 2: str = formatDateToString({ date: val, format: 'M月D日' }); break;
        case 3: str = formatDateToString({ date: val, format: 'YYYY年M月D日' }); break;
    }
    return str;
});
