import { FEATURE_MAP } from 'pub-consts/const.js';
export default {
    name: 'pageConfig',
    namespaced: false,
    state: {
        // 单点登录配置
        ssoConfig: {},
        // 功能点列表, 在使用乐高成配置时，请使用checkFeat
        features: [],
        // 高级功能点列表
        advancedFeatures: [], // isOpen:是否开启，daysRemaining：试用结束时间，onTrial(0:未试用，1：试用中，2：试用到期)
        reportHttpData: [], // 主动上报的接口信息(日志平台查看)
    },
    getters: {
        // 单点登录配置
        getSsoConfig: (state) => {
            return state.ssoConfig;
        },
        // 检测账号是否包含功能点, 乐高城配置
        checkFeat: ({ features }) => {
            const map = {};
            for (const featureId in FEATURE_MAP) {
                const key = FEATURE_MAP[featureId];
                map[key] = features.includes(featureId);
            }
            return map;
        },
        // 单点登录配置
        getAdvancedFeatures: (state) => {
            return state.advancedFeatures;
        },
        // 检测高级功能是否开启
        checkAdvancedFeatureData: (state) => {
            return (featureId) => {
                const mapData = state.advancedFeatures.find(e => e.featureId === featureId);
                return mapData ?? {};
            };
        },
    },
    mutations: {
        // 单点登录配置相应隐藏
        pushSsoConfig(state, data) {
            state.ssoConfig = data;
        },
        // 根据已开启的功能点显示页面元素
        pushFeatures(state, data) {
            state.features = data.featureIds;
        },
        // 高级功能点列表
        pushAdvancedFeatures(state, data) {
            state.advancedFeatures = data;
        },
        // 保存主动上报日志的接口数据
        setReportHttpData(state, data) {
            const arr = state.reportHttpData;
            // 接口去重
            const isRepeatUrl =  (arr || []).some(val => val.url === data.url);
            if (isRepeatUrl) {
                (arr || []).forEach((val) => {
                    if (val.url === data.url) {
                        val.data = data.data;
                    }
                });
                state.reportHttpData = arr;
                return;
            }
            // 保留10个接口
            if (arr.length >= 10) {
                arr.shift(); // 移除数组中第一条数据
            }
            arr.push(data);
            state.reportHttpData = arr;
        },
    },
    actions: {

    },
};
