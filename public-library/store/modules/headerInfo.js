/* eslint-disable eqeqeq */
import dayjs from 'dayjs';
import i18n from 'src/lang';
import { getSensorsCurrentEntInfo, getSensorsCurrentRoleName } from 'pub-utils/sensorsUtils.js';
export default {
    name: 'commonHeaderInfo',
    namespaced: false,
    state: {
        login: false,
        // 1、public 公有云用户 2、hybrid 混合云用户 3、 publicHybrid 公有云用户进入混合云合同 4、 otherHybrid 混合云用户进入其他混合云合同  5、publicAliyun 公有云用户进入公有云合同 6、hybridAliyun 混合云用户进入公有云合同
        hybridUserType: null,
        hybridVersion: null, // 混合云版本，从4.19.3开始
        hybridEchoHistory: [],
        localFieldSupportType: 0, // 混合云3.0是否支持本地存储，0表示存云端，大于1表示存本地
        // 未读消息
        noticeCount: {
            noticeUnreadMessageCount: 0,
            contractUnreadMessageCount: 0,
            unreadMessageCount: 0,
        },
        commonHeaderInfo: { // 公共头部
            SECOND_CHECK: '0',
            status: false,
            currentEntId: '',
            hasManager: '',
            hasMobile: true,
            userType: '',
            enterprises: [],
            extendFields: {},
            platformUser: {},
            roleDetails: [],
            // 模板匹配规则设置
            templateMatchConf: {
                hasSetTemplateMatch: false, // 是否在boss系统开启了模板匹配规则
                hasSetExcelCharMatch: false, // 是否开启了Excel系统字段匹配
                hasActiveMatchRule: false, // 是否已经添加了规则
                hasUseAlone: false, // 模板匹配开始时,是否开启单独使用模板
            },
            // 混合云host
            hybridServer: '',
            LANConnected: null,
            // 签署人添加附件功能
            receiverAttachent: false,
            hasDynamicTemplate: false, // 是否开启动态模板
        },
        newFromExperience: false, // 新官网注册流程的新客户,查看操作视频入口有用到
        personalIdentifyWay: false, // 开发者个人实名配置开关是否开启
        hasGroupConsole: false, // 是否展示集团控制台
        noMorePhoneSetTip: false, // 不在提醒提交通知手机
        noLoginLanguage: sessionStorage.getItem('noLoginLanguage') || '', // 未登录页面 设置的语言，在登录成功之后 携带过去
    },
    getters: {
        // 判断混合云用户处于内网环境中
        getInLAN: state => {
            return state.commonHeaderInfo.LANConnected;
        },
        getHybridUserType: state => {
            return state.hybridUserType;
        },
        // 是否是单点登录进来的
        getIsLoginFromDeveloper: state => {
            return state.commonHeaderInfo.extendFields.loginType === 'SSO';
        },
        // 是否开启签约密码二次校验 '0'/'1'
        SIGN_SECOND_CHECK: state => {
            return state.commonHeaderInfo.SECOND_CHECK == '1';
        },
        getUserFullName: state => {
            return state.commonHeaderInfo.platformUser.fullName;
        },
        getUserAccount: state => {
            return state.commonHeaderInfo.platformUser.account;
        },
        getUserType: state => {
            return state.commonHeaderInfo.userType;
        },
        getUserId: state => {
            return state.commonHeaderInfo.platformUser.userId;
        },
        getPlatformUserAuthStatus: state => {
            return state.commonHeaderInfo.platformUser.authStatus;
        },
        // 外文版（非国内版，目前有：getIsJa || getIsUae）
        getIsForeignVersion(state, getters) {
            return getters.getIsJa || getters.getIsUae;
        },
        // 是否是阿联酋版用户
        getIsUae() {
            const jaPath = ['uae.bestsign.com', 'ent.bestsign.ae', 'ent-ae.bestsign.info'];
            // 本地先默认返回true
            return process.env.NODE_ENV === 'development-uae' || jaPath.includes(location.host);
        },
        // 是否是日文版用户
        getIsJa() {
            const jaPath = ['ent-jp.bestsign.tech', 'ent-jp.bestsign.info', 'ent.bestsign.com'];
            // 本地先默认返回true
            return process.env.NODE_ENV === 'development-ja' || jaPath.includes(location.host);
        },
        getAuthStatus: (state, getters) => {
            let authStatus = '';
            if (state.commonHeaderInfo.userType === 'Person') {
                authStatus = state.commonHeaderInfo.platformUser.authStatus;
            } else {
                authStatus = getters.getCurrentEntInfo ? getters.getCurrentEntInfo.authStatus : '';
            }
            return authStatus;
        },
        getUserPermissons: state => {
            const roles = state.commonHeaderInfo.roleDetails;
            const permissionObj = {
                DOWNLOAD_CONTRACT: false,
                SEND_TEMPLATE_CONTRACT: false,
                SEND_LOCAL_FILE: false,
                // SEND_CONTRACT_OUT: false,
                SHARE_CONTRACT: false,
                SIGN_CONTRACT: false,
                CREATE_CONTRACT_TEMPLATE: false,
                STATS_CONTRACT: false,
                SIGN_M: false,
                ACCOUNT_M: false,
                CHARGE_M: false,
                CREATE_ARCHIVE_BOX: false, // 创建档案柜权限
            };

            // 遍历角色权限
            if (roles) {
                roles.forEach(role => {
                    role.privileges.forEach(privilege => {
                        permissionObj[privilege.name] = true;
                    });
                });
            }
            return {
                download: permissionObj['DOWNLOAD_CONTRACT'],
                sendTemp: permissionObj['SEND_TEMPLATE_CONTRACT'],
                sendLocal: permissionObj['SEND_LOCAL_FILE'],
                // sendOut: permissionObj['SEND_CONTRACT_OUT'],
                share: permissionObj['SHARE_CONTRACT'],
                sign: true, // permissionObj['SIGN_CONTRACT'] 默认都有签署权限,
                createTemp: permissionObj['CREATE_CONTRACT_TEMPLATE'],
                statistics: permissionObj['STATS_CONTRACT'],
                sign_m: permissionObj['SIGN_M'],
                account_m: permissionObj['ACCOUNT_M'],
                recharge_m: permissionObj['CHARGE_M'],
                create_archive_box: permissionObj['CREATE_ARCHIVE_BOX'], // 创建档案柜权限
            };
        },
        // 是否是单点登录进来的内部成员
        getIsInnerMember: state => {
            return state.commonHeaderInfo.extendFields.isInnerMember;
        },
        hasManager: state => {
            let status = false;
            const roles = state.commonHeaderInfo.roleDetails;

            // 遍历角色权限
            if (roles) {
                roles.forEach(role => {
                    if (role.name === '主管理员' || role.name === i18n.t('home.admin')) {
                        status = true;
                    }
                });
            }
            return status;
        },
        isPerson: state => state.commonHeaderInfo.userType === 'Person',
        getCurrentEntInfo: state => {
            const temObj = state.commonHeaderInfo;
            const ents = temObj.enterprises;
            const curEntId = temObj.currentEntId;
            let curEntInfo;
            ents.forEach((val) => {
                if (val.entId === curEntId) {
                    curEntInfo = val;
                }
            });
            return curEntInfo;
        },
        getHybridEchoItems: state => {
            return state.hybridEchoHistory.map(item => {
                const requestTime = Number(item.config.requestTime);
                return {
                    date: dayjs(requestTime).format('YYYY-MM-DD HH:mm:ss'),
                    timeCost: item.responseTime - requestTime,
                    url: item.config.url,
                    status: item.status,
                    statusText: item.statusText,
                };
            });
        },
    },
    mutations: {
        logout(state) {
            state.userInfo = null;
            state.login = false;
            state.noMorePhoneSetTip = false;
            state.companyDetailInfo = {
                adminEmployee: {

                },
                corpAuth: {

                },
                entGroup: {

                },
                enterprise: {

                },
                rolePrivileges: {

                },
                haveData: false,
            };
            /*
             * fix SAAS-5065 未实名的企业在合同管理页面点退出的时候页面卡死
             * 报错 vue.runtime.esm.js:559 DOMException: Failed to execute 'insertBefore' on 'Node': The node before which the new node is to be inserted is not a child of this node.
             * 暂时找不到具体引起报错的模板代码，可能是v-if和v-for使用有问题https://stackoverflow.com/questions/51653178/vue-warn-error-in-nexttick-notfounderror-failed-to-execute-insertbefore
             *
             */
            const commonHeaderInfo = {
                SECOND_CHECK: '0',
                status: false,
                currentEntId: '',
                hasManager: '',
                // userType: '', // 注释。打开， 未实名的企业在合同管理页面点退出 js报错
                enterprises: [],
                platformUser: {},
                extendFields: {},
                hybridServer: '',
                LANConnected: null,
                templateMatchConf: {},
                hasDynamicTemplate: false,
            };
            state.commonHeaderInfo = {
                ...state.commonHeaderInfo,
                ...commonHeaderInfo,
            };
            Vue.$cookie.delete('sensorsUserInfo');
        },
        pushInitCommonHeaderInfo(state, data) {
            // 增加业务线之后，企业的全名为:entName_bizName
            data.enterprises.map(item => {
                const {
                    bizName,
                    entName,
                } = item;
                item.fullEntName = bizName ? `${entName}_${bizName}` : entName;
                return item;
            });
            if (data.ecologyEnterprises) {
                data.ecologyEnterprises.forEach(item => {
                    item.entId = item.entId + '-ecology';
                });
            }
            // 将生态版企业主体和旗舰版正常的企业合并在一起
            data.enterprises.push(...(data.ecologyEnterprises || []));
            for (const key in data) {
                const val = data[key];
                // state.commonHeaderInfo[key] = val === null ? '' : val;
                Vue.set(state.commonHeaderInfo, key, val === null ? '' : val);
            }
            // state.login = true;
            Vue.set(state, 'login', true);
            Vue.set(state.commonHeaderInfo, 'status', true);

            // sensors公共属性不能取对象的属性，只能把属性放在cookie中
            const userType = state.commonHeaderInfo?.userType || '';
            Vue.$cookie.set('sensorsUserInfo', JSON.stringify({
                currentEntId: state.commonHeaderInfo?.currentEntId || '',
                currentAccountType: userType ? (userType.toLowerCase() === 'enterprise' ? '企业账号' : '个人账号') : '',
                currentEntName: getSensorsCurrentEntInfo(state.commonHeaderInfo),
                currentRoleName: getSensorsCurrentRoleName(state.commonHeaderInfo),
            }));
        },
        // 混合云网络状态改变
        changeLANStatus(state, data) {
            state.commonHeaderInfo.LANConnected = data;
        },
        changeCacheLANStatus(state, data) {
            state.commonHeaderInfo.cacheLANConnected = data;
        },
        // 替换 hybridAccessToken 和 hybridServer
        replaceHybridAccessToken(state, {
            hybridServer,
            hybridAccessToken,
        }) {
            state.commonHeaderInfo.hybridServer = hybridServer;
            state.commonHeaderInfo.hybridAccessToken = hybridAccessToken;
        },
        // 修改 hybridUserType
        changeHybridUserType(state, data) {
            state.hybridUserType = data;
        },
        setHybridVersion(state, data) {
            // console.log('hybridVersion', data)
            state.hybridVersion = data;
        },
        pushNoticeCount(state, data) {
            state.noticeCount = data;
        },
        setLocalFieldSupportType(state, supportType) {
            state.localFieldSupportType = supportType;
        },
        pushHybridEchoList(state, echo) {
            if (state.hybridEchoHistory.length === 100) {
                state.hybridEchoHistory.pop();
            }
            state.hybridEchoHistory.unshift(echo);
        },
        setNoLoginLanguage(state, noLoginLanguage) {
            state.noLoginLanguage = noLoginLanguage;
            sessionStorage.setItem('noLoginLanguage', noLoginLanguage);
        },
        resetNoLoginLanguage(state) {
            state.noLoginLanguage = '';
            sessionStorage.setItem('noLoginLanguage', '');
        },
    },
    actions: {
    },
};
