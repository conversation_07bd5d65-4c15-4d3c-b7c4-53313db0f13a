export default {
    name: 'containerEnvironment',
    namespaced: false,
    state: {
        isQyWx: false, // 是否在企业微信小程序
        isFirstQywx: true, // 从企业微信进入
        signPlatform: 'WAP', // 企业微信小程序与手机端区分来源，也可区分微信与企业微信小程序
    },
    getters: {

    },
    mutations: {
        setIsQyWx(state, data) {
            state.isQyWx = data;
        },
        setIsFirstQywx(state, data) {
            state.isFirstQywx = data;
        },
        setSignPlatform(state, data) {
            state.signPlatform = data;
        },
    },
    actions: {

    },
};
