import MessageToast from 'pub-components/messageToast';

var MessageToastPlugin = function(options) {
    options = options || {};

    if (typeof options === 'string') {
        options = {
            message: options,
        };
    }

    const ToastController = Vue.extend(MessageToast);

    const instance = new ToastController({
        data: options,
    });

    instance.vm = instance.$mount(document.createElement('div'));
    instance.dispatch = dispatchCallBack;

    if (typeof options === 'object') {
        document.body.appendChild(instance.vm.$el);
    }

    let bridge = null;

    // return instance.vm;
    return new Promise((resolve, reject) => {
        bridge = { resolve, reject };
    });

    function dispatchCallBack(action) {
        if (action === 'resolve') {
            bridge.resolve('confirm');
        } else {
            bridge.reject('cancel');
        }
    }
};

['success', 'warning', 'info', 'error'].forEach(type => {
    MessageToastPlugin[type] = options => {
        if (typeof options === 'string') {
            options = {
                message: options,
            };
        }
        options.type = type;
        // eslint-disable-next-line new-cap
        return MessageToastPlugin(options);
    };
});

Vue.prototype.$MessageToast = MessageToastPlugin;
Vue.$MessageToast = MessageToastPlugin;

Vue.use(MessageToastPlugin);

export default MessageToastPlugin;
