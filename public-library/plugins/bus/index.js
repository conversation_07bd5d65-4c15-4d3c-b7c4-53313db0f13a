import Vue from 'vue';

const Bus =  {
    install() {
        const bus = new Vue({
            methods: {
                on(event, ...args) {
                    this.$on(event, ...args);
                },
                emit(event, ...args) {
                    this.$emit(event, ...args);
                },
                off(event, ...args) {
                    this.$off(event, ...args);
                },
            },
        });
        Vue.prototype.$bus = bus;
        Vue.bus = bus;
    },
};
Vue.use(Bus);

export default Bus;
