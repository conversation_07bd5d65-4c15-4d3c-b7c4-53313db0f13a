import Vue from 'vue';

const Token = {
    install(Vue) {
        Vue.prototype.$token = this;
        Vue.$token = this;
    },
    save(access_token, refresh_token) {
        this.clearToken();
        Vue.$cookie.set('access_token', access_token);
        Vue.$cookie.set('refresh_token', refresh_token);
    },
    delete(noDeleteSinoRussia = false) {
        this.clearToken();
        // 删除自定义域名的logoFileId
        Vue.$cookie.delete('homeLogoFileId');
        // fix 签署短链接登录签署完成回到sass首页sino-russia丢失。签署短链接登录会调用delete方法，不能删除sino-russia
        if (!noDeleteSinoRussia) {
            // 删除中俄专用参数值
            Vue.$cookie.delete('sino-russia');
        }
    },
    // 清除一、二级域名下的 token cookie
    clearToken() {
        // 删除未更新前二级域名下存的cookie
        Vue.$cookie.delete('access_token');
        Vue.$cookie.delete('refresh_token');
        // 删除一级域名下存的cookie
        Vue.$cookie.delete('access_token', { domain: Vue.GLOBAL['COOKIE_DOMAIN'] });
        Vue.$cookie.delete('refresh_token', { domain: Vue.GLOBAL['COOKIE_DOMAIN'] });
    },
};

Vue.use(Token);

export default Token;
