import ResetPassword from 'pub-businessComponents/dialog/ResetPassword.vue';

const DialogController = Vue.extend(ResetPassword);

ResetPassword.install = function() {
    const instance = new DialogController().$mount();

    document.body.appendChild(instance.$el);

    Vue.nextTick(() => {
        instance.show = true;
    });
};

Vue.prototype.$resetPassword = ResetPassword.install;
Vue.$resetPassword = ResetPassword.install;

export default ResetPassword;
