import AlphaBasic from './basic';
import i18n from 'src/lang';
import store from 'src/store';

/**
 *
 * @desc 混合云 1.0 业务逻辑，继承 1.0 的通用方法，包含混合云老 jar 包的兼容逻辑
 * @export
 * @class HybridAlpha
 * @extends {AlphaBasic}
 */
export default class HybridAlpha extends AlphaBasic {
    // eslint-disable-next-line no-useless-constructor
    constructor() {
        super();
    }

    // 合同管理混合云合同连不上
    offlineTipList(opt) {
        opt = opt || {};
        const confirmBtnText = opt.confirmBtnText || i18n.t('companyFull.confirm'); // 确定
        const operate = opt.operate || i18n.t('catchMap.view'); // 查看
        const showClose = opt.showClose !== false;

        return new Promise((resolve) => {
            this.echo(opt.hybridServer, { timeout: 5000 }).then(() => {
                resolve(true);
            }).catch(() => {
                function showMessageBox(title, message, btnText) {
                    // eslint-disable-next-line new-cap
                    return Vue.MessageBox({
                        icon: 'null',
                        iClass: 'hybrid-network-tip-msgbox el-dialog-mobile',
                        showHeader: true,
                        headerTitle: i18n.t('catchMap.cantOperate', { operate: operate }), // 无法${operate}合同
                        title,
                        message,
                        confirmBtnText: btnText,
                        showClose: showClose,
                    })
                        .then(() => {
                            opt.callback && opt.callback();
                        })
                        .catch(() => {})
                        .finally(() => {
                            resolve(false);
                        });
                }
                const boxTitle =
                i18n.t('catchMap.hybridNetHeader'); // 发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。
                const boxMessage = i18n.t('catchMap.hybridNetMsg'); // 建议您：检查网络是否正常
                showMessageBox(boxTitle, boxMessage, confirmBtnText);
            });
        });
    }
    /**
     * @desc SAAS-2941,多方签署兼容旧的war包，保证混合云平滑升级
     *
     * @param {*} res
     * @returns
     * @memberof HybridAlpha
     */
    tempEXUploadSuccessHandle(res) {
        const newRes = {
            code: '140001',
            message: 'success',
            result: {
                existError: res.haveErrors,
                missingFields: res.missingFields,
                header: {
                    signers: [],
                    inputFields: [],
                },
                rows: [],
            },
        };
        res.rows.forEach((item, index) => {
            if (index === 0) {
                item.fields.map(field => {
                    newRes.result.header.inputFields.push(field.fieldName);
                });
            }
            newRes.result.rows.push({
                lineNumber: item.lineNumber,
                signers: [],
                inputFields: item.fields,
            });
        });
        return newRes;
    }
    /**
     *
     * @desc 签署上传完文件后，将新文件对象 push 到 docList 中，这里抽离新文件对象组装的逻辑
     * @param {*} res
     * @param {*} contractId
     * @returns
     * @memberof HybridAlpha
     */
    signMergeDocToList(res, contractId) {
        const { order, documentId } = res;
        const imgSrc = this.getImgSrcCros(`/contract-api/contracts/${contractId}/documents/${documentId}/view/1`);
        const docData = Object.assign({}, res, {
            uploadStatus: 2,
            imgSrc,
            zoomMaskShow: false,
        });
        return {
            order,
            docData,
        };
    }
    /**
     *
     * @desc 模板上传完文件后，将新文件对象 push 到 docList 中，这里抽离新文件对象组装的逻辑
     * @param {*} res
     * @param {*} contractId
     * @returns
     * @memberof HybridAlpha
     */
    tempMergeDocToList(res, contractId) {
        const { order, documentId } = res;
        const imgSrc = this.getImgSrcCros(`/template-api/templates/${contractId}/documents/${documentId}/view/1`);
        const docData = Object.assign({}, res, {
            uploadStatus: 2,
            imgSrc,
            zoomMaskShow: false,
        });

        return {
            order,
            docData,
        };
    }
    /**
     *
     * @desc 模板上传完附件后，将新文件对象 push 到 docList 中，这里抽离新文件对象组装的逻辑
     * @param {*} res
     * @param {*} contractId
     * @returns
     * @memberof HybridAlpha
     */
    tempAttachmentMergeDocToList(res, contractId) {
        const { order, attachmentId } = res;
        const imgSrc = this.getImgSrcCros(`/template-api/templates/${contractId}/attachments/${attachmentId}/view/1`);
        const docData = Object.assign({}, res, {
            uploadStatus: 2,
            imgSrc,
            zoomMaskShow: false,
        });

        return {
            order,
            docData,
        };
    }
    /**
     *
     * @desc 单点预览模板文件，获取文件的 base64 ，注释了缓存混合云 header 的逻辑，尽早下线 1.0 ⑧
     * @desc 不缓存 header 的话其实直接用 undirectedRequest 就可以
     * @param {*} url
     * @param {*} id
     * @returns
     * @memberof HybridAlpha
     */
    tempPreviewGetImg(url) {
        const { hybridServer } = store.state.commonHeaderInfo;
        // if (Object.keys(this.hybirdAjaxHeader) > 0 && hybridServer) {
        //     return this.$http(`${hybridServer}${url}`, { headers: this.hybirdAjaxHeader });
        // }
        // 混合云
        if (hybridServer) {
            return this.createHybridCloudHeaders({ url: `${hybridServer}${url}`, method: 'GET', requestData: {}, isFormType: 0 })
                .then(({ data }) => {
                    // this.hybirdAjaxHeader = data;
                    return Vue.$http(`${hybridServer}${url}`, { headers: data });
                });
        }
        // 公有云
        return Vue.$http(url);
    }
    /**
     *
     * @desc 单点预览模板文件，老逻辑：为了后端避免存储临时文件，图片格式用base64
     * @desc 这里先请求第一次获取 pageSize，再拿 pagSize 循环获取 base64 数据
     // todo 可以改成 Promise.all
     * @param {*} templateId
     * @param {*} templatePreviewId
     * @returns
     * @memberof HybridAlpha
     */
    tempPreviewGetContractImgList(templateId, templatePreviewId) {
        return new Promise((resolve, reject) => {
            this.tempPreviewGetImg(`/contract-api/contracts/templates/${templateId}/previews?page=1&templatePreviewId=${templatePreviewId}`, templateId)
                .then(async({ data }) => {
                    const pageSize = data.pageSize;
                    const cacheList = new Array(pageSize);
                    cacheList[0] = `data:image/jpg;base64,${data.imageData}`;
                    for (let i = 2; i <= pageSize; i++) {
                        await this.tempPreviewGetImg(`/contract-api/contracts/templates/${templateId}/previews?page=${i}&templatePreviewId=${templatePreviewId}`, templateId)
                            .then((res) => {
                                cacheList[i - 1] = `data:image/jpg;base64,${res.data.imageData}`;
                            });
                    }

                    resolve({
                        pageSize,
                        list: cacheList,
                    });
                }).catch(() => {
                    reject();
                });
        });
    }
}
