import HybridAlpha from  './alpha/index.js';
import HybridGamma from './gamma/index.js';

const alpha = new HybridAlpha();
const gamma = new HybridGamma();

function resolveScope(version) {
    return version === 'alpha' ? alpha : gamma;
}

// 策略模式匹配不同版本
const HYBRID_TACTICS = {
    echo: {
        'alpha': alpha.echo,
        'gamma': gamma.requestEcho,
    },
    echoBeforeOperate: {
        'alpha': alpha.offlineTipList,
        'gamma': gamma.echoBeforeOperate,
    },
    checkInLAN: {
        'alpha': alpha.checkInLAN,
        'gamma': gamma.checkInLAN,
    },
    makeHeader: {
        'alpha': alpha.createHybridCloudHeaders,
        'gamma': gamma.createHeaders,
    },
    makeRequest: {
        'alpha': alpha.undirectedRequest,
        'gamma': gamma.handleCreateRequest,
    },
    contractHost: {
        'alpha': alpha.returnContractHost,
        'gamma': gamma.requestContractHost,
    },
    contractEnv: {
        'alpha': alpha.getHybridInfo,
        'gamma': gamma.decideContractEnv,
    },
    contractImg: {
        'alpha': alpha.getImgSrcCros,
        'gamma': gamma.getImgSrc,
    },
    offlineTip: {
        'alpha': alpha.offlineTip,
        'gamma': gamma.offlineTip,
    },
    signPrepareMergeDoc: {
        'alpha': alpha.signMergeDocToList,
        'gamma': gamma.signOrTempMergeDocToList,
    },
    tempPrepareMergeDoc: {
        'alpha': alpha.tempMergeDocToList,
        'gamma': gamma.signOrTempMergeDocToList,
    },
    tempAttachmentMergeDocToList: {
        'alpha': alpha.tempAttachmentMergeDocToList,
        'gamma': gamma.signOrTempMergeDocToList,
    },
    tempPreviewGetContractImgList: {
        'alpha': alpha.tempPreviewGetContractImgList,
        'gamma': gamma.tempPreviewContractImgList,
    },
    pdfPreviewUrl: {
        'alpha': alpha.getImgSrcCros,
        'gamma': gamma.pdfPreviewUrl,
    },
};

class HybridAdapter {
    constructor() {
        this.version = '';
    }
    install(exam) {
        Vue.$hybrid = exam;
        Vue.prototype.$hybrid = exam;
    }
    setVersion(deltaHybridVersion) {
        this.version = deltaHybridVersion === '3.0' ? 'gamma' : 'alpha';
    }
    isAlpha() {
        return this.version === 'alpha';
    }
    isGamma() {
        return this.version === 'gamma';
    }
    isGammaLocalField(localFieldSupportType) { // 混合云3.0且业务字段不出门
        return this.isGamma() && localFieldSupportType > 0;
    }
    echo(host, config) {
        return HYBRID_TACTICS.echo[this.version].call(resolveScope(this.version), host, config);
    }
    echoBeforeOperate(opt) {
        return HYBRID_TACTICS.echoBeforeOperate[this.version].call(resolveScope(this.version), opt);
    }
    checkInLAN(interval) {
        return HYBRID_TACTICS.checkInLAN[this.version].call(resolveScope(this.version), interval);
    }
    makeHeader({ url, hybridTarget, method, requestData = {}, isFormType = 0, contractId }) {
        return HYBRID_TACTICS.makeHeader[this.version].call(resolveScope(this.version), { url, hybridTarget, method, requestData, isFormType, contractId });
    }
    makeRequest(config, isFormType) {
        const version = config.hybridVersion || this.version; // 优先根据配置中的版本来作区分
        if (version === 'alpha') {
            delete config.hybridTarget;
        }

        return HYBRID_TACTICS.makeRequest[version].call(resolveScope(version), config, isFormType);
    }
    /**
     *
     * @desc 混3部分接口不需要走混合云了，改走公有云，需要兼容混1
     * @param {*} config
     * @param {*} isFormType
     * @returns
     * @memberof HybridAdapter
     */
    makeRequestAdaptToPublic(config, isFormType) {
        // 混合云 1.0
        if (this.version === 'alpha') {
            return HYBRID_TACTICS.makeRequest['alpha'].call(alpha, config, isFormType);
        }

        const {
            url,
            method,
            data,
            headers = {},
            // contractId,
            // hybridServer,
            ...otherConfig
        } = config;

        // 非混合云1.0走公有云逻辑
        return Vue.$http({
            url,
            method,
            data,
            headers,
            ...otherConfig,
        });
    }
    getContractHost(contractId) {
        return HYBRID_TACTICS.contractHost[this.version].call(resolveScope(this.version), contractId);
    }
    decideContractEnv(systemType, deltaHybridVersion, contractId) {
        this.setVersion(deltaHybridVersion);
        return HYBRID_TACTICS.contractEnv[this.version].call(resolveScope(this.version), systemType, contractId);
    }
    getContractImg(path, query) {
        // 原混1接口接收 query，而现在已经没有传 query 的场景了
        return HYBRID_TACTICS.contractImg[this.version].call(resolveScope(this.version), path, query);
    }
    getPdfPreviewUrl({ hybridServer, hybridAccessToken, hybridTarget, url, params }) {
        if (this.version === 'alpha') {
            return HYBRID_TACTICS.pdfPreviewUrl['alpha'].call(alpha, url);
        }
        return HYBRID_TACTICS.pdfPreviewUrl['gamma'].call(resolveScope(gamma), { hybridServer, hybridAccessToken, hybridTarget, url, params });
    }
    offlineTip(opt = {}) {
        return HYBRID_TACTICS.offlineTip[this.version].call(resolveScope(this.version), opt);
    }
    /**
     *
     * @desc 统一清除定时器
     * @memberof HybridAdapter
     */
    clearAll() {
        clearTimeout(alpha.inLANInterval);
        clearTimeout(gamma.inLANInterval);
    }
    signPrepareMergeDoc(opt, contractId) {
        return HYBRID_TACTICS.signPrepareMergeDoc[this.version].call(resolveScope(this.version), opt, contractId);
    }
    tempPrepareMergeDoc(opt, contractId) {
        return HYBRID_TACTICS.tempPrepareMergeDoc[this.version].call(resolveScope(this.version), opt, contractId);
    }
    tempAttachmentMergeDocToList(opt, contractId) {
        return HYBRID_TACTICS.tempAttachmentMergeDocToList[this.version].call(resolveScope(this.version), opt, contractId);
    }
    tempPreviewGetContractImgList(templateId, templatePreviewId) {
        return HYBRID_TACTICS.tempPreviewGetContractImgList[this.version].call(resolveScope(this.version), templateId, templatePreviewId);
    }
    getOneTimeToken({ contractId = '', size = 1 } = {}) {
        return gamma.getOneTimeToken({ contractId, size });
    }
}

const hybrid = new HybridAdapter();
hybrid.install(hybrid);

export default hybrid;
