
import store from 'src/store';
import i18n from 'src/lang';

/**
 *
 * @desc 混合云 3.0 通用方法
 * @class GammaBasic
 */
class GammaBasic {
    constructor() {
        this.inLANInterval = null;
    }
    requestEcho(host, config) {
        return new Promise((resolve, reject) => {
            Vue.$http.get(`${host}/echo`, {
                ...config,
                noToast: true,
                noUseToken: true,
            }).then((res) => {
                store.commit('setLocalFieldSupportType', res.data.localFieldSupportType);
                this.recordingEchoList(res);
                resolve(res.data);
            }).catch((error) => {
                this.recordingEchoList(error);
                reject(error);
            });
        });
    }
    recordingEchoList(request) {
        store.commit('pushHybridEchoList', request);
    }
    requestInLAN() {
        return this.requestEcho(`${store.state.commonHeaderInfo.hybridServer}`, { timeout: 5000 })
            .then(() => {
                store.commit('changeLANStatus', true);
            })
            .catch(() => {
                store.commit('changeLANStatus', false);
            }).finally(() => {
                this.checkInLAN();
            });
    }
    checkInLAN(interval = 5000) {
        clearTimeout(this.inLANInterval);

        if (interval === 0) {
            return this.requestInLAN();
        }

        // 混合云用户进入公有云合同则不轮询以第一次进页面的状态为准
        if (!(
            store.getters.getHybridUserType === 'publicAliyun' ||
            store.getters.getHybridUserType === 'hybridAliyun'
        )) {
            this.inLANInterval = setTimeout(() => {
                this.requestInLAN();
            }, interval);
        }
    }
    /**
     * @desc 生成混合云请求headers
     *
     * @param {*} { hybridTarget, method, requestData }
     * @returns {
     *   bestsign-client-id
     *   bestsign-sign-timestamp
     *   bestsign-signature
     *   bestsign-signature-type
     * }
     * @memberof GammaBasic
     */
    createHeaders({ hybridTarget, requestData, otherHybridParams = {} }) {
        const cacheData = requestData;
        const parameterMap = { target: hybridTarget };

        // 把 file 从 data 中抽出，这里应该没有 file
        // if (cacheData.file) {
        //     parameterMap.file = cacheData.file;
        //     delete cacheData.file;
        // }

        // params 转为 json 字符串
        parameterMap.params = JSON.stringify(cacheData);
        const data = {
            uriWithParam: '/hybrid/file',
            parameterMap,
            ...otherHybridParams,
        };
        const targetNeedContractIdList = ['/contracts/confirm', '/contracts/paper-sign/sign', '/contract/paper-sign-import'];

        // 合同确认签署，如果是混3发给其他企业签署，需要加上参数contractId
        if (cacheData.contractId && targetNeedContractIdList.includes(hybridTarget)) {
            data.contractId = cacheData.contractId;
        } else if (cacheData.contractIds) { // 批量签署时，需要加上合同的contractId
            data.contractId = cacheData.contractIds[0].contractId;
        }

        return Vue.$http({
            method: 'post',
            url: '/ents/developer/hybrid-header',
            data,
        });
    }
    /**
     *
     * 统一 form 请求
     * @param {*} reqOpt
     * @param {*} resolve
     * @param {*} reject
     * @returns
     * @memberof GammaBasic
     */
    sendFormRequest(reqOpt) {
        const cacheOpt = reqOpt;
        const formData = new FormData();

        // 把 file 从 data 中抽出
        if (cacheOpt.data.file) {
            formData.append('file', cacheOpt.data.file);
            delete cacheOpt.data.file;
        }
        // 其他参数需要转成 json 字符串
        formData.append('params', JSON.stringify(cacheOpt.data));
        formData.append('target', cacheOpt.hybridTarget);

        return Vue.$http({
            ...cacheOpt,
            method: 'post',
            url: cacheOpt.hybridServer + '/hybrid/file',
            data: formData,
            noUseToken: true,
        });
    }
    /**
     * get 请求通过 url 访问，还是统一调用服务发现接口，通过 url 传参
     * 主要是请求图片、下载文件
     * @param {*} action
     * @param {*} param
     * @returns
     * @memberof GammaBasic
     */
    async getRequestSource(action, param = {}) {
        const hybridServer = store.state.commonHeaderInfo.hybridServer;
        // const hybridAccessToken = store.state.commonHeaderInfo.hybridAccessToken || '';
        const entry = '/hybrid/file';
        const stringifyParam = param ? encodeURIComponent(JSON.stringify(param)) : '';
        const encodeTarget = encodeURIComponent(action);
        const hybridAccessToken = await this.getOneTimeToken();
        return `${hybridServer}${entry}?target=${encodeTarget}&bestsign-access-token=${hybridAccessToken}&param=${stringifyParam}`;
    }
    // 判断 hybridServer ，为空并且不为空字符串时取 store 中的值
    _dealServer(server) {
        return server === '' ? '' : (server || store.state.commonHeaderInfo.hybridServer);
    }
    // 发送普通 axios 请求
    _dealNormalRequest(config) {
        return Vue.$http(config);
    }
    /**
     *
     * @desc 混3发送请求入口函数
     * @param {*} config
     * @returns Promise
     * @memberof GammaBasic
     */
    handleCreateRequest(config) {
        // hybridServer 赋值
        const { hybridServer } = config;
        config.hybridServer = this._dealServer(hybridServer);
        // 判断共混请求
        if (config.hybridServer) {
            return this.createRequest(config);
        } else {
            return this._dealNormalRequest(config);
        }
    }
    /**
     *
     * @desc 发送混合云请求
     * @param {*} config
     * @returns
     * @memberof GammaBasic
     */
    async createRequest(config) {
        const {
            // url,
            method,
            data,
            headers = {},
            otherHybridParams = {},
            // contractId,
            hybridServer,
            hybridTarget = '',
            ...otherConfig
        } = config;

        const res = await this.createHeaders({ hybridTarget: hybridTarget, method, requestData: data, otherHybridParams });
        const resData = res.data;
        const resHeaders = {
            'bestsign-client-id': resData['bestsign-client-id'] || '',
            'bestsign-sign-timestamp': resData['bestsign-sign-timestamp'] || '',
            'bestsign-signature': resData['bestsign-signature'] || '',
            'bestsign-signature-type': resData['bestsign-signature-type'] || '',
            'bestsign-user': resData['bestsign-user'] || '',
        };

        return this.sendFormRequest({
            hybridServer: hybridServer,
            hybridTarget: hybridTarget,
            data,
            headers: {
                ...headers,
                ...resHeaders,
            },
            ...otherConfig,
            noUseToken: true,
        });
    }
    // 返回合同对应的 hybridServer 和 hybridAccessToken
    requestContractHost(contractId) {
        return new Promise((resolve, reject) => {
            Vue.$http.get('/users/hybrid-conf', {
                params: { contractId },
            })
                .then(res => {
                    resolve({
                        hybridServer: res.data.hybridServer,
                        hybridAccessToken: res.data.hybridAccessToken,
                        deltaHybridVersion: res.data.deltaHybridVersion,
                        ...res,
                    });
                })
                .catch(err => {
                    reject(err);
                });
        });
    }
    // 当收件人进入合同相关页面时，替换hybridAccessToken
    decideContractEnv(systemType, contractId) {
        const _this = this;
        // 如果是混合云合同
        if (systemType === 'HYBRID_CLOUD') {
            return Vue.$http
                .get(`/users/hybrid-conf?contractId=${contractId}`)
                .then(res => {
                    store.commit('replaceHybridAccessToken', {
                        hybridServer: res.data.hybridServer,
                        hybridAccessToken: res.data.hybridAccessToken,
                    });
                    if (store.getters.getHybridUserType === 'public') {
                        // 判断是公有云用户进入混合云合同
                        store.commit('changeHybridUserType', 'publicHybrid');
                    } else if (
                        store.getters.getHybridUserType === 'hybrid' &&
                        store.state.commonHeaderInfo.hybridServer !== res.data.hybridServer
                    ) {
                        // 判断混合云用户进入其他混合云合同
                        store.commit('changeHybridUserType', 'otherHybrid');
                    }

                    return _this.checkInLAN(0);
                });
        } else {
            // 如果是公有云合同，将 hybridServer 和 hybridAccessToken 设为空
            // 混合云用户进入公有云合同，获取图片时从公有云获取
            store.commit(
                'changeHybridUserType',
                store.state.commonHeaderInfo.hybridServer ? 'hybridAliyun' : 'publicAliyun',
            );
            store.commit('replaceHybridAccessToken', {
                hybridServer: '',
                hybridAccessToken: '',
            });
            clearTimeout(this.inLANInterval);
            return Promise.resolve();
        }
    }
    /**
     *
     * @desc 兼容混2、混3，混二带上 bestsign-access-token，混3的参数和 token 都在后端返回的 url 中
     * @param {*} url
     * @returns
     * @memberof GammaBasic
     */
    resolveHybridImgSrc(url, hybridServer) {
        return `${hybridServer}${url}`;
    }
    /**
     *
     * @desc 混3获取 pdf 流
     * @param {*} { hybridServer, hybridTarget, params, hybridAccessToken }
     * @returns
     * @memberof GammaBasic
     */
    async resolvePdfSrc({ hybridServer, hybridTarget, params }) {
        const hybridAccessToken = await this.getOneTimeToken({ contractId: params.contractId });
        return `${hybridServer}/hybrid/file?target=${hybridTarget}&params=${encodeURI(JSON.stringify(params))}&bestsign-access-token=${hybridAccessToken}`;
    }
    getImgSrc(path) {
        const { hybridServer } = store.state.commonHeaderInfo;
        const join = path.includes('?') ? '&' : '?';

        return hybridServer
            ? this.resolveHybridImgSrc(path + join, hybridServer)
            : `${path}${join}access_token=${Vue.$cookie.get('access_token')}`;
    }
    pdfPreviewUrl({ hybridServer = '', hybridAccessToken, hybridTarget, url, params }) {
        const join = url.includes('?') ? '&' : '?';
        return hybridServer
            ? this.resolvePdfSrc({ hybridServer, hybridTarget, params })
            : `${hybridServer}${url}${join}bestsign-access-token=${hybridAccessToken}&access_token=${Vue.$cookie.get('access_token')}`;
    }
    showMessageBox({ operate, title, message, btnText, showClose, callback, resolve }) {
        // eslint-disable-next-line new-cap
        return Vue.MessageBox({
            icon: 'null',
            iClass: 'hybrid-network-tip-msgbox el-dialog-mobile',
            showHeader: true,
            headerTitle: i18n.t('catchMap.cantOperate', { operate: operate }), // 无法${operate}合同
            title,
            message,
            confirmBtnText: btnText,
            showClose: showClose,
        })
            .then(() => {
                callback && callback();
            })
            .catch(() => {})
            .finally(() => {
                resolve(false);
            });
    }
    /**
     * 混合云网络出错提示
     * *
     * @param  opt = {
     *         confirmBtnText,
     *         operate, 操作类型：签署、拒签、撤销、下载，显示在弹窗 headerTitle，
     *         callback,
     *         fieldPage: false, 指定位置页的提示特殊处理
     *         beforeMessage, 在弹窗提示之前resolve
     * }
     * @return promise
     */
    offlineTip(opt) {
        opt = opt || {};
        const confirmBtnText = opt.confirmBtnText || i18n.t('companyFull.confirm'); // 确定
        const operate = opt.operate || i18n.t('catchMap.view'); // 查看
        const showClose = opt.showClose !== false;

        return new Promise((resolve) => {
            // 混合云用户
            if (store.getters.getHybridUserType === 'hybrid') {
                // 判断用户是否在内网中
                if (store.getters.getInLAN) {
                    resolve(true);
                } else if (!opt.fieldPage) {
                    // 判断当前用户处于局域网内且无法连接
                    Vue.$MessageToast.error(i18n.t('catchMap.checkNet')).then(() => {
                        resolve(false);
                    });
                } else if (opt.fieldPage) {
                    const title = i18n.t('catchMap.hybridNotConnect'); // 原因：您的企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。
                    const message = i18n.t('catchMap.hybridSuggest'); // 建议您：(1)检查网络是否正常；(2)检查合同存储服务器是否正常运行
                    this.showMessageBox({ operate, title, message, btnText: i18n.t('catchMap.goHomePage'), showClose, callback: opt.callback, resolve });
                }
            } else if (
                // 公有云用户或混合云用户进入其他混合云合同
                store.getters.getHybridUserType === 'publicHybrid' ||
                store.getters.getHybridUserType === 'otherHybrid'
            ) {
                // 判断收件人是否在内网中
                if (store.getters.getInLAN) {
                    resolve(true);
                } else {
                    const title =
                    i18n.t('catchMap.hybridNetHeader'); // 发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。
                    const message = i18n.t('catchMap.hybridNetMsg'); // 建议您：检查网络是否正常
                    this.showMessageBox({ operate, title, message, confirmBtnText, showClose, callback: opt.callback, resolve });
                }
            } else if (
                store.getters.getHybridUserType === 'public' ||
                store.getters.getHybridUserType === 'publicAliyun' ||
                store.getters.getHybridUserType === 'hybridAliyun'
            ) {
                resolve(true);
            }
        });
    }
    // 操作前先检查网络环境
    echoBeforeOperate(opt) {
        opt = opt || {};
        const confirmBtnText = opt.confirmBtnText || i18n.t('companyFull.confirm'); // 确定
        const operate = opt.operate || i18n.t('catchMap.view'); // 查看
        const showClose = opt.showClose !== false;

        return new Promise((resolve) => {
            this.requestEcho(opt.hybridServer, { timeout: 5000 }).then(() => {
                resolve(true);
            }).catch(() => {
                const title =
                i18n.t('catchMap.hybridNetHeader'); // 发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。
                const message = i18n.t('catchMap.hybridNetMsg'); // 建议您：检查网络是否正常
                this.showMessageBox({ operate, title, message, confirmBtnText, showClose, callback: opt.callback, resolve });
            });
        });
    }
    /**
     * 获取一次性token,用户访问混3资源
     * *
     * @param
     *     contractId, 有合同id的根据合同id获取合同对应混合的token，无合同id获取当前账号混合云对应的token
     *     size:获取的token个数，默认一个
     *
     */
    getOneTimeToken({ contractId = '', size = 1 } = {}) {
        return new Promise((resolve) => {
            const url = contractId ? `/users/hybrid-conf/batch-acquire-hybrid-access-token/${contractId}?size=${size}` : `/users/hybrid-conf/batch-acquire-hybrid-access-token?size=${size}`;
            Vue.$http.get(url).then((res) => {
                resolve(size === 1 ? res.data.hybridAccessTokens[0] : res.data.hybridAccessTokens);
            }).catch(() => {
                resolve([]);
            });
        });
    }
}

export default GammaBasic;
