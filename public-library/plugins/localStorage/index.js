import { checkoutAvailable } from './utils.js';
import { storage } from './storage.js';

const LocalStorage = {

    install(Vue) {
        Vue.prototype.$localStorage = this;
        Vue.localStorage = this;
    },

    set(name, value) {
        return checkoutAvailable(() => {
            storage.setItem(name, value);
        });
    },

    get(name) {
        let item = null;
        checkoutAvailable(() => {
            item = storage.getItem(name);
        });
        return item;
    },

    remove(name) {
        checkoutAvailable(() => {
            return storage.removeItem(name);
        });
    },

    clear() {
        storage.clear();
    },
};

Vue.use(LocalStorage);

export default LocalStorage;
