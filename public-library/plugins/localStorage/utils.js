let canUseLocalStorage = true;
try {
    const randomKey = 'testValue';
    window.localStorage.setItem(randomKey, '1');
    window.localStorage.removeItem(randomKey);
    canUseLocalStorage = true;
} catch (err) {
    canUseLocalStorage = false;
}

export const isStorageAvailable = canUseLocalStorage;

export const checkoutAvailable = (cb) => {
    if (isStorageAvailable) {
        cb();
        return true;
    } else {
        Vue.$MessageToast('请关闭浏览器“无痕模式”或“隐身模式”后重试');
        return false;
    }
};
