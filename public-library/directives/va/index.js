// 不支持checkbox

/**
 * html
 *  <el-input
        v-va:formData.psss
        v-model="formData.psss">
    </el-input>
    <el-button class="next-step" type="primary" @click="submitForm('formData')" :disabled="false">下一步</el-button>

    js
    data: {
        formData: {
            pass: ''
        }
    },
    methods: {
        submitForm: function(formName) {
            let validateRes = this.$validation.validate(formName, this.formData);
            if (validateRes) {
                console.log('校验通过！')
            }
        }
    }

 * 绑定指令的元素必须要有个v-model绑定，v-model格式为formData.pass
 * :formData为input所属的表单
 * .pass为要验证的规则，后期可支持多个规则累加
 * ="custom"，选填，自定义规则，规则格式见validation-config.js，不写就用默认规则
 */
import Vue from 'vue';
import { find, hasClass, parseDom, insertAfter, setStyle, addClass } from 'pub-utils/dom.js';
import { ValidateRules as  defaultOptions } from 'pub-consts/const.js';

function assert(condition, msg) {
    if (condition) {
        throw new Error(`[va-warn]: ${msg}`);
    }
}

function createErrorDom(baseEl, data, errClass = '') {
    removeErrorDom(baseEl);
    let _class = 'validation-error';
    let _style = 'position: absolute; left: 425px;';
    // element 2.* 之后，data-err-type直接挂在input上的
    if (baseEl.children[0].dataset.errType && baseEl.children[0].dataset.errType === 'bottom') {
        _class = 'validation-error-bottom';
        _style = '';
    }
    if (!hasClass(baseEl.nextSibling, _class)) {
        const html = `<div class="${_class} ${errClass}" style="${_style}">
                <i class="iconfont el-icon-ssq-wrong-filling"></i>
                <span>${data}</span>
            </div>`;

        const newEle = parseDom(html);
        setStyle(baseEl.parentNode, 'position', 'relative');
        insertAfter(newEle, baseEl);
    }
}

function removeErrorDom(baseEl) {
    if (hasClass(baseEl.nextSibling, 'validation-error') || hasClass(baseEl.nextSibling, 'validation-error-bottom')) {
        if (baseEl.nextSibling.remove) {
            baseEl.nextSibling.remove();
        } else if (baseEl.nextSibling.removeNode) {
            baseEl.nextSibling.removeNode(true);
        }
    }
}

// 校验单个inuput
function checkInput(value, rule, _options, vm, el) {
    const notEmptyMsg = vm.$t('validationMsg.notEmpty');
    const isPassed = rule.test(value);
    // TODO: errmsg大部分翻译未处理
    const errmsg_translate = _options.errmsg_translate ? vm.$t(_options.errmsg_translate) : _options.errmsg;

    const errmsg = value === '' ? notEmptyMsg : (errmsg_translate || notEmptyMsg);
    if (!isPassed) {
        createErrorDom(el, errmsg, _options.errClass);
        (typeof _options.fail === 'function') && _options.fail(errmsg);
        _options.notice && (vm[_options.notice] = false);
    } else {
        removeErrorDom(el);
        (typeof _options.success === 'function') && _options.success.call(vm, errmsg);
        _options.notice && (vm[_options.notice] = true);
    }
    (typeof _options.always === 'function') && _options.always();
    return isPassed;
}

class Validation {
    constructor() {
        this.$formGroups = {};
        this.$errmsg = {};
    }

    /**
     * 表单提交的校验
     * @param  {[String]} formName   [表单名称，和“v-va:formName”对应]
     * @param  {[Object]} formVModel [v-model绑定的表单对象]
     * @return {[Boolean]}            [校验结果]
     */
    validate(formName, formVModel) {
        const formCollection = this.$formGroups[formName];
        let res = true;
        for (const k in formVModel) {
            if (!formCollection[k]) {
                continue;
            }

            const rule = formCollection[k].rule;
            const value = formVModel[k];

            if (!rule.test(value)) {
                res = false;
                break;
            }
        }
        return res;
    }

    /**
     * 点击表单提交的
     * @param  {[String]} formName   [表单名称，和“v-va:formName”对应]
     * @param  {[Object]} formVModel [v-model绑定的表单对象]
     * @return {[Boolean]}            [校验结果]
     */
    submitValidate(formName, formVModel, vm) {
        const formCollection = this.$formGroups[formName];
        let isPassed = true;
        for (const k in formVModel) {
            if (!formCollection[k]) {
                continue;
            }
            const rule = formCollection[k].rule;
            const options = formCollection[k].options;
            const el = formCollection[k].recipientDom.parentNode;
            const value = formVModel[k];
            if (!checkInput(value, rule, options, vm, el)) {
                isPassed = false;
            }
        }
        return isPassed;
    }
}

const Directive = function() {
    Vue.directive('va', { bind: function(el, binding, vnode) {
        assert(!vnode.data.model, 'must set a argument, like "v-va:form"');

        const vm = vnode.context;                      // 组件实例 div.register
        const formGroups = vm.$validation.$formGroups; // 组群，取值于Validaion中的this.$formGroups
        const formName = binding.arg;                  // 被绑定指令的元素所属的表单名称
        // 设定意义意义在于和dom解耦，代价是每个指令都需要指定它所属的表单
        const form = formGroups[formName] || {};       // 当前分组
        const itemExpression = vnode.data.model.expression.split('.'); // 'accountData.vCode' 或者 vCode
        const itemName = itemExpression[1] || itemExpression[0]; // vCode / accountData
        // let itemVModel = vnode.data.model.value;  // 被绑定指令的元素的v-model
        const rules = Object.keys(binding.modifiers);  // 验证的规则 { pass: true, mail: true }
        const _options = Object.assign({}, defaultOptions[rules[0]], binding.value);
        const when = _options.when || 'blur';          // 校验时机
        const rule = _options.rule || /\S/;            // 当前组件的规则，默认值是匹配非空字符串
        const recipientDom = find(el, _options.nodeName || 'INPUT');

        form[itemName] = {
            rule: rule,
            options: _options,
            recipientDom: recipientDom,
            // value: itemVModel
        };
        vm.$validation.$formGroups[formName] = form;
        const notEmptyMsg = vm.$t('validationMsg.notEmpty');

        function check(e) {
            const value = e.target.value.trim();
            checkInput(value, rule, _options, vm, el);
        }

        if (recipientDom) {
            if (!hasClass(recipientDom, 'bind')) { // 解决 v-if会导致重复绑定 的问题
                recipientDom.addEventListener(when, check);
            }
            addClass(recipientDom, 'bind');
            // 输入的时候remove 提示' 不能为空！'
            recipientDom.addEventListener('input', function() {
                // const value = e.target.value;
                // eslint-disable-next-line eqeqeq
                const errorDom = el.nextSibling;
                if (errorDom && errorDom.innerText && errorDom.innerText.trim() === notEmptyMsg.trim()) {
                    removeErrorDom(el);
                }
            });
        }
    } });
};

const mount = function() {
    const validation = new Validation();
    Vue.$validation = validation;
    Vue.prototype.$validation = validation;
};

Validation.install = function() {
    mount();
    // eslint-disable-next-line new-cap
    Directive();
};

Vue.use(Validation);

export default Validation;
