import Vue from 'vue';
import i18n from 'src/lang';
const trimName = {};
trimName.install = (Vue) => {
    Vue.directive('trim-name', {
        bind(el) {
            const inputNode = el.getElementsByTagName('input')[0];
            inputNode.addEventListener('blur', (e) => {
                let value = e.target.value.trim();
                const includesEn = /[a-zA-z]/.test(value);
                const includesEmpty = value.includes(' ');
                // 如果输入框内容不是英文但是包含空格，提示并去掉空格
                if (!includesEn && includesEmpty) {
                    Vue.$MessageToast(i18n.t('replaceEmpty'));
                    value = value.replace(/\s/g, '');
                }
                e.target.value = value;
                e.target.dispatchEvent(new Event('input'));
            });
        },
    });
};

Vue.use(trimName);

export default trimName;
