const handleTag = (el, binding) => {
    const label = el.querySelector('.business-status-tag');
    if (label) {
        label.remove();
    }
    const status = binding.value;
    const validStatuses = ['吊销', '注销', '停业', '清算', '撤销'];
    if (validStatuses.includes(status)) {
        const tag = document.createElement('span');
        tag.classList.add('business-status-tag');
        tag.style.fontSize = '12px';
        tag.style.lineHeight = 1;
        tag.style.color = 'white';
        tag.style.backgroundColor = '#FF4444';
        tag.style.padding = '3px';
        tag.style.marginRight = '5px';
        tag.style.borderRadius = '4px';
        tag.innerText = status;
        el.insertBefore(tag, el.firstChild);
    }
};
const businessStatus = () => {
    Vue.directive('business-status', {
        bind(el, binding) {
            handleTag(el, binding);
        },
        update(el, binding) {
            handleTag(el, binding);
        },
    });
};

Vue.use(businessStatus);

export default businessStatus;
