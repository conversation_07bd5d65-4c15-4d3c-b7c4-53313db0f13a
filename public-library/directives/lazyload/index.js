import { scrollParentDom, throttle } from './utils.js';

const DELAY_TIME = 800; // 延迟触发请求的时间

function setSrcAttr(el, trueSrc) {
    if (el.nodeName.toLowerCase() === 'img') {
        if (el.getAttribute('src') === trueSrc) {
            return;
        }
        el.setAttribute('src', trueSrc);
    } else if (el.nodeName.toLowerCase() === 'image') {
        if (el.getAttributeNS('http://www.w3.org/1999/xlink', 'href') === trueSrc) {
            return;
        }
        el.setAttributeNS('http://www.w3.org/1999/xlink', 'xlink:href', trueSrc);
    }
}

function Lazyload() {
    Vue.directive('lazy', function(el, binding) {
        Vue.nextTick(() => {
            const index = binding.value.index === undefined ? 9999 : binding.value.index;
            const trueSrc = typeof binding.value === 'string' ? binding.value : binding.value.src;
            const usePreviewUrl = binding.modifiers?.previewUrl;
            const split = typeof binding.value === 'string' ? 0 : binding.value.split;
            const deferTime = binding?.value?.deferTime || 300;
            const total = binding?.value?.total; // 渲染总数
            const $scrollParent = scrollParentDom(el);
            const rect = el.getBoundingClientRect();

            const DEFER_LIMIT = 200; // defer处理判断边界：总数数量超过200
            const defer = (total && total > DEFER_LIMIT) || binding.modifiers.defer;

            let timer = null;

            function scrollHandler(rect) {
                if (
                    rect.top &&
                    rect.top > 0 - rect.height && // 图片从视口上界出现
                    rect.top < window.innerHeight + 200 // 图片从视口下界出现
                ) {
                    setSrcAttr(el, usePreviewUrl ? el.getAttribute('data-previewUrl') : trueSrc);
                }
            }

            scrollHandler(rect);

            if (index >= split) {
                $scrollParent.addEventListener('scroll', throttle(function() {
                    // defer: 滚动结束后再触发请求，防止滑动过程中不断触发请求
                    if (defer) {
                        clearTimeout(timer);
                        timer = setTimeout(() => {
                            const rect = el.getBoundingClientRect();
                            scrollHandler(rect);
                        }, deferTime);
                        // eslint-disable-next-line brace-style
                    }
                    // 普通情况下：延迟一定时间后再触发请求，防止过快滚动时，频繁触发请求
                    // 滚动较慢时，仍然触发请求（应用场景：用户滚动较慢，一张一张在浏览）
                    else {
                        setTimeout(() => {
                            const rect = el.getBoundingClientRect();
                            scrollHandler(rect);
                        }, DELAY_TIME);
                    }
                }, 200)); // 节流防止滚动中频繁触发函数
            } else {
                setSrcAttr(el, trueSrc);
            }
        });
    },
        // {
        // 	bind(el, binding, vnode, lodVnode) {
        // 		Vue.nextTick( () => {
        // 			let index = binding.value.index == undefined ? 9999 : binding.value.index;
        // 			let trueSrc = typeof binding.value == 'string' ? binding.value : binding.value.src;
        // 			let split = typeof binding.value == 'string' ? 0 : binding.value.split;
        // 			let $scrollParent = scrollParentDom(el);
        // 			let rect = el.getBoundingClientRect();

        // 			let defer = binding.modifiers.defer;

        // 			let timer = null;
        // 			const TIMEOUT = 400;

        // 			function scrollHandler(rect) {
        // 				if (
        // 					rect.top &&
        // 					rect.top > 0 - rect.height && // 图片从视口上界出现
        // 					rect.top < window.innerHeight + 200 // 图片从视口下界出现
        // 				) {
        // 					setSrcAttr(el, trueSrc);
        // 				}
        // 			}

        // 			scrollHandler(rect);

        // 			if (index >= split) {
        // 				$scrollParent.addEventListener('scroll', throttle(function() {
        // 					// defer: 滚动结束后再触发请求，防止滑动过程中不断触发请求
        // 					if (defer) {
        // 						clearTimeout(timer);
        // 						timer = setTimeout(() => {
        // 							let rect = el.getBoundingClientRect();
        // 							scrollHandler(rect);
        // 						}, TIMEOUT);
        // 					}
        // 					// 普通情况下：延迟一定时间后再触发请求，防止过快滚动时，频繁触发请求
        // 					// 滚动较慢时，仍然触发请求（应用场景：用户滚动较慢，一张一张在浏览）
        // 					else {
        // 						setTimeout(() => {
        // 							let rect = el.getBoundingClientRect();
        // 							scrollHandler(rect);
        // 						}, DELAY_TIME);
        // 					}
        // 				}, 200)); // 节流防止滚动中频繁触发函数
        // 			} else {
        // 				setSrcAttr(el, trueSrc);
        // 			}

        // 		} );
        // 	}
        // }
    );
}

Vue.use(Lazyload);

export default Lazyload;
