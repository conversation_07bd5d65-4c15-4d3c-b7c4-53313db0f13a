// 根据模块 id 显示/隐藏 DOM 指令（上上签商品化需求）
// 使用：v-module-id.xxx（如 <div v-module-id.docDetail_2_btn></div>）, xxx 为乐高城里配置对应的模块 id
// 也支持 v-module-id.id1.id2，代表含义：id1 和 id2 的 visible 都不是 false 的时候显示该模块
// 也支持 v-module-id="id1" v-module-id="[id1,id2]" 传入字符串会转成数组
// v-module-id.id1="id2" 这种情况下会优先取 id1
// 新增支持 v-module-id:or.id1.id2, v-module-id:or="[id1, id2]"，代表含义：id1 和 id2 的 visible 只要有一个为 true 的时候显示该模块
import store from 'src/store';
const moduleId = {};
moduleId.install = (Vue) => {
    const { state } = store;
    Vue.directive('module-id', {
        inserted(el, binding, vNode) {
            const config = state.ssoConfig;
            // 兼容 value
            const bindingVal = binding.value instanceof Array ? binding.value : (binding.value ? [binding.value] : []);
            // 当前指令的模块
            const ids = Object.keys(binding.modifiers).length ? Object.keys(binding.modifiers) : bindingVal;
            // 来自 store 里面的配置数据
            const moduleIdConfigData = Object.keys(config).reduce((moduleIdValues, menu) => {
                return moduleIdValues.concat(Object.values(config[menu]));
            }, []);

            const func = binding.arg === 'or' ? Array.prototype.some : Array.prototype.every;
            // 获取该模块 id 的可见性
            const elementVisible = func.call(ids, moduleId => {
                return (moduleIdConfigData.find(item => item.moduleId === moduleId) || {}).visible !== false;
            });

            if (!elementVisible) {
                if (vNode.componentInstance) { // 删除Vue实例
                    vNode.componentInstance.$destroy();
                }

                if (el && el.parentNode) {
                    el.parentNode.removeChild(el);
                }
            } else {
                el.setAttribute('data-module-id', ids);
            }

            // console.log(ids);
        },
    });
    // Vue.directive('module-text', {
    //     inserted (el, binding) {
    //         const config = state.ssoConfig;
    //         const bindingVal = binding.value || binding.expression; // 默认的text值

    //         // 来自 store 里面的配置数据
    //         const moduleIdConfigData = Object.keys(config).reduce((moduleIdValues, menu) => {
    //             return moduleIdValues.concat(Object.values(config[menu]));
    //         }, []);
    //         // 获取该模块 text
    //         const elementText = (moduleIdConfigData.find(item => item.moduleId === binding.arg) || {}).text;
    //         el.innerHTML = elementText ? elementText : bindingVal;
    //     }
    // });
};

Vue.use(moduleId);

export default moduleId;
