<!-- not-found页 -->
<template>
    <Error class="not-found-page">
        <img src="~pub-images/404-iconpng.png" height="135" width="156">
        <p class="p1">Oops!</p>
        <p class="p2"><!-- 没有找到您访问的页面（404） -->{{ $t('codeHandlerMap.page404') }}</p>
    </Error>
</template>
<script>
import Error from './common/Error.vue';
export default {
    components: {
        Error,
    },
};
</script>
<style lang="scss">
	.not-found-page {
		p {
			text-align: center;
		}
		.p1 {
			margin-top: 20px;
			margin-bottom: 10px;
			font-size: 24px;
			color: $--color-black;
		}
		.p2 {
			font-size: 14px;
			color: $--color-info;
		}
	}
</style>
