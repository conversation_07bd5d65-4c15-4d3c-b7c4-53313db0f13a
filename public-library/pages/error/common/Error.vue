<template>
    <div class="error-cpn">
        <div class="main">
            <slot class="slot-content"></slot>
            <router-link
                v-if="isPC && ifNeedHomeBtn"
                tag="p"
                to="/account-center/home"
                class="btn-type-one to-home"
            >
                <!-- 返回首页 -->{{ $t('codeHandlerMap.goHome') }}
            </router-link>
            <div v-else-if="!isPC" class="download"><a :href="downloadAppSrc"><!-- 下载上上签APP -->{{ $t('codeHandlerMap.downloadApp') }}</a></div>
        </div>
        <footerSimpler></footerSimpler>
    </div>
</template>

<script>
import { isPC, isIOS } from 'pub-utils/device.js';
import footerSimpler from 'pub-businessComponents/footerSimpler/index.vue';
export default {
    components: { footerSimpler },
    props: {
        ifNeedHomeBtn: {
            type: Boolean,
            default: true,
        },
    },
    data() {
        return {
            isPC: isPC(),
            downloadAppSrc: isIOS
                ? 'https://itunes.apple.com/us/app/shang-shang-qian/id955123098'
                : 'http://a.app.qq.com/o/simple.jsp?pkgname=com.ssqian.bestsign.sign',
        };
    },
};
</script>

<style lang="scss">
	.error-cpn {
		width: 100%;
		height: 100%;
		.main {
			position: relative;
			top: 34%;
			left: 50%;
			transform: translateX(-50%) translateY(-50%);
			width: 160px;
			.to-home {
				margin: 25px auto 25px;
				width: 110px;
				height: 30px;
				line-height: 30px;
				font-size: 14px;
			}
			.download {
				margin: 25px auto;
				width: 110px;
				height: 30px;
				line-height: 30px;
				font-size: 14px;
                text-decoration: underline;
                margin-top: 18px;
            }
            a:link {
                color: $--color-primary-light-2;
            }
		}
	}
</style>
