import regRules from 'pub-utils/regs.js';
/**
 * [description]
 * @param  {[String]}   when     [什么时候触发校验，非必须，默认'blur']
 * @param  {[Regexp]}   rule     [校验规则，必须]
 * @param  {[String]}   errmsg   [输出信息，非必须]
 * @param  {[String]}   errClass [errDom的自定义样式名，非必须]
 * @param  {[String]}   nodeName [校验的DOM名称，非必须，默认'INPUT']
 * @param  {Function} 	success  [校验成功的回调，非必须]
 * @param  {Function} 	fail 	 [校验失败的回调，非必须]
 * @param  {Function} 	always 	 [校验成功或失败的回调，非必须]
 * @param  {String} 	notice 	 [自动显示隐藏inputNotice组件，非必须]
 */
const ValidateRules = {
    // 注册／登录模块
    pass: {
        rule: regRules.pass,
        // errmsg: '请输入6-18位数字、字母'
        errmsg_translate: 'validationMsg.enter8to18n',
    },
    signpass: {
        rule: regRules.signpass,
        // errmsg: '请输入6位数字',
        errmsg_translate: 'validationMsg.signPwdType',
    },
    companyName: {
        rule: regRules.companyName,
        // errmsg: '请填写真实的企业名称',
        errmsg_translate: 'validationMsg.enterActualEntName',
    },
    userName: {
        rule: regRules.userName,
        // errmsg: '请输入正确的姓名',
        errmsg_translate: 'validationMsg.enterCorrectName',

    },
    userAccount: {
        rule: regRules.userAccount,
        // errmsg: '请输入正确的账号！',
        errmsg_translate: 'validationMsg.errEmailOrTel', // 翻译， value指向 lang/zh.js中的key
    },
    userPhone: {
        rule: regRules.userPhone,
        // errmsg: '请输入正确的手机号',
        errmsg_translate: 'validationMsg.enterCorrectPhoneNum',

    },
    jaTelePhoneOrPhone: {
        rule: regRules.jaTelePhoneOrPhone,
        // errmsg: '请输入正确的手机号',
        errmsg_translate: 'validationMsg.enterCorrectPhoneNum',

    },
    userEmail: {
        rule: regRules.userEmail,
        // errmsg: '请输入正确的邮箱',
        errmsg_translate: 'validationMsg.enterCorrectEmail',

    },
    phoneVerifyCode: {
        rule: regRules.phoneVerifyCode,
        errmsg: '验证码错误',
        errmsg_translate: 'validationMsg.verCodeFormatErr',
    },
    imageVerifyCode: {
        rule: regRules.imageVerifyCode,
        // errmsg: '图形验证码错误',
        errmsg_translate: 'validationMsg.imgCodeErr',

    },
    loginCode: {
        rule: regRules.loginCode,
        errmsg: '请输入6-18位数字、大小写字母',
        errmsg_translate: 'validationMsg.enter6to18n',
    },
    signCode: {
        rule: regRules.signCode,
        errmsg: '请输入6位数字',
        errmsg_translate: 'validationMsg.signPwdType',
    },
    IDCardReg: {
        rule: regRules.IDCardReg,
        // errmsg: '请输入正确的证件号码',
        errmsg_translate: 'validationMsg.enterCorrectIdNum',

    },
    weakIdCardReg: {
        rule: regRules.weakIdCardReg,
        // errmsg: '请输入正确的证件号码',
        errmsg_translate: 'validationMsg.enterCorrectIdNum',
    },
    foreignerUserName: {
        rule: regRules.foreignerUserName,
        errmsg_translate: 'validationMsg.enterCorrectName', // 请输入正确的姓名
    },
};

/**
 * @description 移动端路由
 */
const mobileRouteObj = {
    'index': '/mobile/home', // H5首页改版
    'home': '/mobile/doc/list',
    'sign': '/mobile/sign',
    'docDetail': '/mobile/sign/doc-detail', // ?contractId=${contractId}\
    'faceResult': 'mobile/face-result',
};

/**
 * @description 编辑字段弹窗，日期样式
 */
const DATE_FORMATS = [{
    text: 'xxxx年xx月xx日',
    value: 'yyyy年MM月dd日',
}, {
    text: 'xxxx-xx-xx',
    value: 'yyyy-MM-dd',
}, {
    text: 'xxxx/xx/xx',
    value: 'yyyy/MM/dd',
}, {
    text: 'dd MM yyyy',
    value: 'dd MMMM yyyy',
}];

/**
 * @description 编辑字段弹窗，时刻样式
 */
const TIME_FORMATS = [{
    text: 'HH:mm',
    value: 'HH:mm',
    tooltip: '15:30',
}];

const TIME_BASE_DATE = '1970-01-02';

/**
 * @description 登录注册模块路由
 */
const ACCOUNT_CENTER_ROUTERS = [
    ...['/login', '/mpLogin', '/otherDomainLogin', '/oauth/login', '/sso/login', '/sso/binding', '/sso/sign/claim',
        '/reception/login', '/reception/examination'], // 登录模块
    ...['/register', '/reg/company/mail', '/reg/notFound', '/register/done', '/invite/register', '/invite/reg/staff',
        '/invite/reg/staff/done', '/signing/insteadRegister'], // 注册模块
    ...['/sso/transition', '/sso/signTransition', '/sso/customize', '/sso/notFound', '/oauth/certification/sign', '/oauth/certification/auth',
        '/daAuth', '/daAuth/continue', '/signing/transition', '/signing/transition/take', '/jumper/recharge'], // 中转模块
    ...['/forgotPassword', '/setPassword', '/resetSignPassword'], // 密码
    ...['/service-agreement', '/privacy-policy', '/digital-certificate-protocal'], // 协议
    ...['/enterprise/authentication/baseInfoChecks', '/personal/certification/baseCheck'], // 信息核实页
];
// 实名pc 路由，部分路由与登录部分冲突，使用正则; 包含签署实名引导
const AUTH_PC_ROUTERS = [
    /^\/ent\/sign\/guide$/,
    /^\/sign\/guide\/sino-russia$/,
    /^\/sign\/guide\/restart\/sino-russia$/,
    /^\/ent\/sign\/guide\/transition$/,
    /^\/enterprise\/authentication$/,
    /^\/enterprise\/authentication\/(?!baseInfoChecks)/,
    /^\/\w+\/enterprise\/authentication\/face/,
    /^\/personal\/certification\/(?!baseCheck)/,
    /^\/\w+\/personal\/certification\/(?!baseCheck)/,
    /^\/foundation\/authentication/,
    /^\/sign\/guide$/,
    /^\/personauth\/face/,
    /^\/\w+\/personauth\/face/,
    /^\/person\/(normal|guide|archive)/,
    /^\/account\/appeal$/,
];

// 乐高城配置
const featureIdConfig = [
    {
        name: '签署分享',
        featureId: '14',
        key: 'signShareLink',
    }, {
        name: '刷脸签署',
        featureId: '15',
        key: 'faceVerifyForSign',
    }, {
        name: '自动签署',
        featureId: '6',
        key: 'autoSign',
    }, {
        name: '签署页面支持多语言',
        featureId: '1',
        key: 'signLang',
    }, {
        name: '合同中展示查验二维码',
        featureId: '8',
        key: 'contractShowQRCode',
    }, {
        name: '签署人添加附件',
        featureId: '34',
        key: 'singerAddAttach',
    }, {
        name: '合同到期提醒',
        featureId: '18',
        key: 'contractExpirationReminder',
    }, {
        name: '骑缝章',
        featureId: '52',
        key: 'ridingSeal',
    }, {
        name: '合同类型',
        featureId: '20',
        key: 'contractType',
    }, {
        name: '合同装饰',
        featureId: '68',
        key: 'contractDecoration',
    }, {
        name: '企业文件夹',
        featureId: '19',
        key: 'enterpriseFolder',
    }, {
        name: '企业签署时经办人需实名认证',
        featureId: '57',
        key: 'entSignNeedOperatorAuth',
    }, {
        name: '签约数据导出',
        featureId: '21',
        key: 'docExort',
    }, {
        name: '签署者付费',
        featureId: '58',
        key: 'singerPay',
    }, {
        name: '列表配置',
        featureId: '7',
        key: 'listConfig',
    }, {
        name: '加入区块链',
        featureId: '35',
        key: 'joinBlockchain',
    }, {
        name: '前台代收',
        featureId: '61',
        key: 'frontDeskCollection',
    }, {
        name: '合同下载控制',
        featureId: '60',
        key: 'contractDownload',
    }, {
        name: '快捷入口自定义',
        featureId: '64',
        key: 'shortcut',
    }, {
        name: '合同代发',
        featureId: '74',
        key: 'insteadSendState',
    }, {
        name: '模板总开关',
        featureId: '16',
        key: 'template',
    }, {
        name: '合同代签',
        featureId: '73',
        key: 'allograph',
    }, {
        name: '可使用模板数',
        featureId: '17',
        key: 'numberOfTemplatesAvailable',
    }, {
        name: '模板智能匹配',
        featureId: '4',
        key: 'templateIntelligentMatch',
    }, {
        name: 'EXCEL系统字段匹配',
        featureId: '3',
        key: 'systemFieldMatchInExcel',
    }, {
        name: '允许发起方添加附件',
        featureId: '32',
        key: 'allowSenderToAddAttachments',
    }, {
        name: '创建模板顺序签署',
        featureId: '56',
        key: 'orderSign',
    }, {
        name: '动态模板',
        featureId: '59',
        key: 'dynamicTemplate',
    }, {
        name: '模板组合',
        featureId: '72',
        key: 'templateCombination',
    }, {
        name: '模板支持多签署变量',
        featureId: '33',
        key: 'templateMultiSignVar',
    }, {
        name: '统计报表总开关',
        featureId: '22',
        key: 'statistics',
    }, {
        name: '必须使用单点登录才能进入上上签',
        featureId: '13',
        key: 'mustLoginWithSSO',
    }, {
        name: '数据下载',
        featureId: '23',
        key: 'dataDownload',
    }, {
        name: '成员管理',
        featureId: '24',
        key: 'memberManage',
    }, {
        name: '可使用成员数',
        featureId: '25',
        key: 'memberAmount',
    }, {
        name: '角色管理',
        featureId: '26',
        key: 'roleManage',
    }, {
        name: '客户品牌展示',
        featureId: '27',
        key: 'customerBrandDisplay',
    }, {
        name: '可使用印章数',
        featureId: '28',
        key: 'sealAmount',
    }, {
        name: '印章授权企业',
        featureId: '54',
        key: 'sealAuthorizedEnterprise',
    }, {
        name: '企业品牌',
        featureId: '49',
        key: 'enterpriseBrand',
    }, {
        name: '控制台-预置合同',
        featureId: '65',
        key: 'presetContract',
    }, {
        name: '行业包配置',
        featureId: '66',
        key: 'industryPackageConfig',
    },
    {
        name: '指定签约方式',
        featureId: '83',
        key: 'signWay',
    }, {
        name: '通知设置',
        featureId: '84',
        key: 'notificationSetting',
    }, {
        name: '指定实名方式',
        featureId: '85',
        key: 'RealNameAuthentication',
    }, {
        name: '合同浏览校验身份',
        featureId: '80',
        key: 'viewContractPermissions',
    }, {
        name: '按账号数计费',
        featureId: '69',
        key: 'chargeByAccount',
    }, {
        name: '自定义标签',
        featureId: '70',
        key: 'customLabel',
    }, {
        name: '集团集中部署',
        featureId: '75',
        key: 'groupCentralizedDeployment',
    }, {
        name: '多业务线管理',
        featureId: '67',
        key: 'multiLineManage',
    }, {
        name: 'SSO登录',
        featureId: '71',
        key: 'ssoLogin',
    }, {
        name: '审计日志',
        featureId: '31',
        key: 'auditLog',
    }, {
        name: '审批管理总开关',
        featureId: '29',
        key: 'approvalManage',
    }, {
        name: '审批页面支持多语言',
        featureId: '30',
        key: 'approvalLang',
    }, {
        name: '档案柜数量上限',
        featureId: '36',
    }, {
        name: '单个档案柜自定义照片项数量上限',
        featureId: '37',
    }, {
        name: '单个档案柜自定义文字项数量上限',
        featureId: '38',
    }, {
        name: '档案柜容量上限',
        featureId: '39',
    }, {
        name: '单个档案柜自定义单选项数量上限',
        featureId: '47',
    }, {
        name: '存储空间上限',
        featureId: '40',
    }, {
        name: '单个档案柜自定义多选项数量上限',
        featureId: '48',
    }, {
        name: '企业档案柜',
        featureId: '77',
        key: 'entArchiveBox',
    }, {
        name: '累计档案数上限',
        featureId: '41',
    }, {
        name: '累计刷脸次数上限',
        featureId: '42',
    }, {
        name: '身份证二要素累计上限',
        featureId: '43',
    }, {
        name: '手机三要素累计上限',
        featureId: '44',
    }, {
        name: '银行卡三要素累计上限',
        featureId: '45',
    }, {
        name: '身份证照片OCR累计上限',
        featureId: '46',
    }, {
        name: '上上签信用圈',
        featureId: '76',
        key: 'isAddCreditSys',
    }, {
        name: '查验码管理',
        featureId: '87',
        key: 'verifyQR',
    },
    {
        name: '合同关联',
        featureId: '82',
        key: 'contractRelate',
    }, {
        name: '归档管理',
        featureId: '81',
        key: 'canArchive',
    }, {
        name: '手写笔迹识别',
        featureId: '79',
        key: 'handwriting',
    },
    {
        name: '档案+总开关',
        featureId: '86',
        key: 'archivesSwitch',
    },
    {
        name: '大文件限制开关',
        featureId: '93',
        key: 'bigFileSwitch',
    },
    {
        name: '拒签理由设置',
        featureId: '94',
        key: 'refuseReasonSet',
    },
    {
        name: '企业签字',
        featureId: '95',
        key: 'enterpriseSignature',
    },
    {
        name: '场景定制',
        featureId: '101',
        key: 'sceneConfig',
    },
    {
        name: '业务核对章',
        featureId: '100',
        key: 'requestSeal',
    }, {
        name: '批量搜索',
        featureId: '103',
        key: 'batchSearch',
    }, {
        name: '模板专用章',
        featureId: '104',
        key: 'specialSeal',
    }, {
        name: '实名年审',
        featureId: '108',
        key: 'annualVerify',
    }, {
        name: '自定义资料年审',
        featureId: '109',
        key: 'customInfoVerify',
    }, {
        name: '企业多人签字',
        featureId: '110',
        key: 'entMultiSignature',
    }, {
        name: '刷脸和验证码校验',
        featureId: '112',
        key: 'faceAndMsgVerify',
    },
    {
        name: '合同作废',
        featureId: '111',
        key: 'invalidStatementOperation',
    },
    {
        name: 'excel表头定位',
        featureId: '118',
        key: 'excelHeadKeyword',
    },
    {
        name: '报表订阅权限',
        featureId: '123',
        key: 'contractTapPermission',
    },
    {
        name: '是否开启了新动态模板',
        featureId: '124',
        key: 'newDynamicTemplate',
    },
    {
        name: '批量发送不同合同',
        featureId: '126',
        key: 'batchReplaceDocument',
    },
    {
        name: '相对方企业风险数据订阅',
        featureId: '128',
        key: 'riskDataPermission',
    },
    {
        name: '模板补充协议',
        featureId: '136',
        key: 'templateSupplement',
    },
    {
        name: '延长签约有效时期',
        featureId: '137',
        key: 'extendSignTime',
    },
    {
        name: '合同自动归档',
        featureId: '138',
        key: 'autoArchive',
    },
    {
        name: '单据合同',
        featureId: '139',
        key: 'billDocument',
    },
    {
        name: '合同全量操作',
        featureId: '142',
        key: 'operateAllContract',
    },
    {
        name: '企业信息变更',
        featureId: '144',
        key: 'entAuthChange',
    },
    {
        name: '业务字段同步',
        featureId: '150',
        key: 'syncManagerField',
    },
    {
        name: '签署时效',
        featureId: '154',
        key: 'ddl',
    },
    {
        name: '扫码签字',
        featureId: '155',
        key: 'scanSign',
    },
    {
        name: 'ofd模板',
        featureId: '157',
        key: 'ofdTemplate',
    },
    {
        name: '签约须知附件',
        featureId: '158',
        key: 'signNoticeAttachment',
    },
    {
        name: '合同列表导出字段配置',
        featureId: '162',
        key: 'exportListFieldConfig',
    },
    {
        name: '企业风险查询',
        featureId: '161',
        key: 'entRiskCheck',
    },
    {
        name: '超批量发送',
        featureId: '166',
        key: 'superBatchSend',
    },
    {
        name: '印章投影',
        featureId: '165',
        key: 'pagefulPosition',
    },
    {
        name: '发出合同再补全',
        featureId: '167',
        key: 'sendedEdit',
    },
    {
        name: '安全设置',
        featureId: '173',
        key: 'securitySet',
    },
    {
        name: '个人签署必须实名',
        featureId: '171',
        key: 'mustAuthPersonSign',
    },
    {
        name: '合同保密',
        featureId: '172',
        key: 'contractConfidentiality',
    },
    {
        name: '强制更换过期密码',
        featureId: '177',
        key: 'forceReplaceExpiredPassword',
    },
    {
        name: '发送前审批',
        featureId: '185',
        key: 'approveBeforeSend',
    },
    {
        name: '签署前审批',
        featureId: '186',
        key: 'approveBeforeSign',
    },
    {
        name: '模版审批',
        featureId: '187',
        key: 'templateApprove',
    },
    {
        name: '非大陆居民有限认证配置',
        featureId: '189',
        key: 'limitPersonNonMainlandAuth',
    },
    {
        name: '中断合同继续发送',
        featureId: '197',
        key: 'sendContractFromCache',
    },
    {
        name: '跨平台合同',
        featureId: '198',
        key: 'ifCrossPlatform',
    },
    {
        name: 'word单据合同',
        featureId: '199',
        key: 'wordBill',
    },
    {
        name: '子公司管理',
        featureId: '200',
        key: 'subCompanyManage',
    },
    {
        name: '删除子公司',
        featureId: '201',
        key: 'deleteSubCompany',
    },
    {
        name: 'Hubble',
        featureId: '220',
        key: 'hubble',
    },
    {
        name: '修改采集资料',
        featureId: '204',
        key: 'modifyArchiveInfo',
    },
    {
        name: '自增表单',
        featureId: '207',
        key: 'selfAddForm',
    },
    {
        name: '合同比对',
        featureId: '208',
        key: 'hubbleContractCompare',
    },
    {
        name: '合同翻译',
        featureId: '209',
        key: 'hubbleContractTranslate',
    },
    {
        name: '风险评估',
        featureId: '233',
        key: 'contractJudgeRisk',
    },
    {
        name: '合同摘要',
        featureId: '218',
        key: 'hubbleContractExtract',
    },
    {
        name: '切换付费方',
        featureId: '226',
        key: 'changePayerWhenSend',
    },
    {
        name: '跨境签',
        featureId: '230',
        key: 'crossBorderSign',
    },
    {
        name: '模板印章缩放',
        featureId: '234',
        key: 'sealDragScale',
    },
    {
        name: '风险评估',
        featureId: '233',
        key: 'contractJudgeRisk',
    },
    {
        name: '扣费方式',
        featureId: '238',
        key: 'deductMethod',
    },
    {
        name: '允许选择中国法律',
        featureId: '240',
        key: 'useCnLaw',
    },
];
// featureId对应的名字映射 { [featureId]: [key] }
const FEATURE_MAP = (() => {
const res = {};
featureIdConfig.map(({ featureId, key }) => {
    if (key) {
        res[featureId] = key;
    }
});
return res;
})();

export {
    ValidateRules,
    mobileRouteObj,
    ACCOUNT_CENTER_ROUTERS,
    AUTH_PC_ROUTERS,
    FEATURE_MAP,
    DATE_FORMATS,
    TIME_FORMATS,
    TIME_BASE_DATE,
    featureIdConfig,
};
