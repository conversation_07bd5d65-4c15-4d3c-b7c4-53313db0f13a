// 全局变量
import lang from 'pub-lang/';
import { getHOSTENV, getPresentEnv } from 'pub-utils/business/getHOSTENV.js';

const Version = '1.16.0';

// 有域名时设置cookie domain为bestsign.cn, ip地址访问时直接取ip
const secondaryDomain = 'bestsign';
const COOKIE_DOMAIN = new RegExp(secondaryDomain).test(document.domain)
    ? document.domain.split('.').splice(-2).join('.') : document.domain;

const HOST_ENV = getHOSTENV(); // 判断是官网还是建行
const ENV_NAME = getPresentEnv(); // 判断是预发布还是正式环境,使用：this.GLOBAL.ENV_NAME
// console.log('GLOBAL_ENV_NAME::' + ENV_NAME);

// URI根路径，'', '/ssoinner', '/ssoouter'
const rootPathName = '';

const AES_ENCRYPT_KEY = '44eb2e88a84656e756ec397bd2f18a7d';

const isZh = lang().locale === 'zh';
const LOGO = isZh ? require('pub-images/bestsign-logo.png') : require('pub-images/bestsign-logo-en.png');
const WHITE_LOGO = isZh ? require('pub-images/bestsign-logo-white.png') : require('pub-images/bestsign-logo-white-en.png');

export default {
    HOST_ENV,
    ENV_NAME,
    COOKIE_DOMAIN,
    Version,
    rootPathName,
    AES_ENCRYPT_KEY,
    LOGO,
    WHITE_LOGO,
};
