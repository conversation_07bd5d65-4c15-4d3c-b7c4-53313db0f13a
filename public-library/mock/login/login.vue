<template>
    <el-dialog title="登陆" :visible.sync="dialogFormVisible" width="400px">
        <el-tabs v-model="activeName">
            <el-tab-pane label="验证码登陆" name="verify">
                <el-form @keyup.enter.native="login">
                    <el-form-item label="账号" :label-width="formLabelWidth" class="formStyle">
                        <el-input v-model="userAccount" autocomplete="off" placeholder="请输入手机号码或者邮箱"></el-input>
                    </el-form-item>
                    <el-form-item label="图形验证" :label-width="formLabelWidth" v-if="showImgVerify" class="formStyle">
                        <el-input v-model="imageVerifyCode" placeholder="请填写图片中内容"></el-input>
                        <img class="imgVerifyCode" v-if="imgSrc" :src="imgSrc" alt="test" @click="changeImg" />
                    </el-form-item>
                    <el-form-item label="验证码" :label-width="formLabelWidth" class="formStyle">
                        <el-input v-model="verifyForm.verifyCode" autocomplete="off" placeholder="请输入验证码" type="verifyCode"></el-input>
                        <el-button class="getVerifyCode" @click="getVerifyCode" :disabled="time > 0">{{ text }}</el-button>
                    </el-form-item>
                </el-form>
            </el-tab-pane>

            <el-tab-pane label="密码登陆" name="passport">
                <el-form @keyup.enter.native="login">
                    <el-form-item label="账号" :label-width="formLabelWidth" class="formStyle">
                        <el-input v-model="userAccount" autocomplete="off" placeholder="请输入手机号码或者邮箱"></el-input>
                    </el-form-item>
                    <el-form-item label="密码" :label-width="formLabelWidth" class="formStyle">
                        <el-input v-model="passportForm.pass" autocomplete="off" placeholder="请输入密码" type="password"></el-input>
                    </el-form-item>
                </el-form>
            </el-tab-pane>

        </el-tabs>

        <div slot="footer" class="dialog-footer">
            <el-button @click="dialogFormVisible = false">取 消</el-button>
            <el-button type="primary" @click="login">确 定</el-button>
        </div>
    </el-dialog>
</template>
<script>
export default {
    data() {
        return {
            time: 0,
            activeName: 'verify',
            dialogFormVisible: true,
            formLabelWidth: '80px',
            showImgVerify: false,
            passportForm: {
                account: '',
                pass: '',
            },
            verifyForm: {
                account: '',
                verifyCode: '',
                verifyKey: '',
            },
            imageVerifyCode: '',
            imageKey: '',
            imgSrc: '',
            userAccount: '',
        };
    },
    computed: {
        text: function() {
            if (this.time > 0) {
                return `重新发送(${this.time}s)`;
            } else {
                return '获取验证码';
            }
        },
    },
    watch: {
        userAccount(value) {
            this.passportForm.account = value;
            this.verifyForm.account = value;
        },
    },
    methods: {
        async login() {
            const {
                data,
            } = await this.$http.post('/auth-center/user/login', this.activeName === 'passport' ? this.passportForm : this.verifyForm);
            if (data) {
                const {
                    access_token,
                    refresh_token,
                } = data;
                this.$token.save(access_token, refresh_token);
                location.reload();
            }
        },
        timer() {
            this.time = 10;
            const inTimer = setInterval(() => {
                if (this.time > 0) {
                    this.time--;
                } else {
                    clearInterval(inTimer);
                }
            }, 1000);
        },
        getVerifyCode() {
            this.$http.sendVerCodeNoLogin({
                code: 'B010',
                sendType: this.verifyForm.account.includes('@') ? 'E' : 'S',
                target: this.verifyForm.account,
                imageCode: this.imageVerifyCode,
                imageKey: this.imageKey,
                noToast: 1,
            })
                .then((res) => {
                    this.verifyForm.verifyKey = res.data.value;
                    this.timer();
                })
                .catch((err) => {
                    if (err.response.data.code === '902' || err.response.data.code === '100006') {
                        this.showImgVerify = true;
                        this.changeImg();
                    }
                });
        },
        changeImg() {
            this.$http.get('/users/ignore/captcha/base64-image')
                .then((res) => {
                    this.imageKey = res.data.imageKey;
                    this.imgSrc = `data:image/jpeg;base64,${res.data.image}`;
                });
        },
    },
};
</script>
<style lang='scss'>
    .formStyle {
        height: 30px;
        [dir="rtl"] & {
            .el-form-item__content {
                margin-left: 0 !important;
                margin-right: 80px !important;
            }
        }
    }
    .getVerifyCode {
        position: relative;
        width: 120px;
        left: 140px;
        top: -30px;
        height: 29px;
        [dir=rtl] & {
            left: auto;
            right: 140px;
        }
    }
    .imgVerifyCode {
        position: relative;
        left: 194px;
        top: -29px;
        height: 28px;
        [dir=rtl] & {
            left: auto;
            right: 194px;
        }
    }
</style>
