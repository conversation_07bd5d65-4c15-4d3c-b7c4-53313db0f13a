import Vue from 'vue';
import Login from './login.vue';

const LoginController = Vue.extend(Login);
let instance;

export default function loginByPassword() {
    if (instance) {
        if (!instance.dialogFormVisible) {
            instance.dialogFormVisible = true;
            return;
        }
        return;
    }
    instance = new LoginController().$mount();
    document.body.appendChild(instance.$el);
}
