// 业务组件翻译
const businessComponents = {
    userHeader: {
        hi: 'Hi',
        exit: 'Exit',
        help: 'Help',
        hotline: 'Service hotline',
    },
    ssoLoginConfig: {
        notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',
        operationStep: {
            one: '第一步 点击继续后，返回登录页面',
            two: '第二步 输入密码，进入上上签平台',
            three: '第三步 发送合同（或管理模板）',
        },
        continue: 'Continue',
        cancel: 'Cancel',
        tip: 'Tip',
    },
    TagManage: {
        labelOperate: {
            editLabel: 'Edit label name',
            setRule: 'Set labeling rules',
            dropRule: 'Set label rules',
            setPutRemind: 'Set up label reminders',
            afterDays: ' days later',
            remindPeople: ' remind the specified personnel',
            edit: 'Edit',
            labelNameLengthLimit: 'The label name can be up to ten Chinese characters long',
            labelNameRequire: 'Please enter a label name',
            setLblRuleTip: 'Set "{name}" label rules',
            remind: 'Remind',
            done: 'To complete',
        },
        putLabelRules: {
            name: 'Attach label',
            tip: '*When the event is triggered, the contract will be automatically affixed with this label. So please select the trigger event first:',
            manual: 'Manual labeling',
            afterSent: 'After the contract is sent',
            inputDaysTip: 'Please enter the number of days',
            beforeSignDeadline: 'Before the deadline of signing',
            afterDone: 'After the contract is completed',
            beforeDue: 'Before the contract expires',
            afterDue: 'After the contract expires',
            afterReject: 'After the contract is rejected',
            day: 'day',
            minSet: 'At least set',
            maxSet: 'Up to set',
        },
        dropLabelRules: {
            name: 'Detach label',
            tip: 'When the event is triggered, the contract label is automatically torn off. So please select the trigger event:',
            manual: 'Manually detach label',
            afterDone: 'After the contract is completed',
            afterDue: 'After the contract expires',
        },
        remindRule: {
            tip: 'Set a timer to send notifications to specified person:',
            remindDays: 'Reminder time (e.g. send a notification 3 days after the label is attached)',
            remindDayTip: 'Please enter the number of days',
            remindPerson: 'Recipient ',
            remindPersonTip: 'Please input the email address',
            addRemindPerson: 'New notification email',
            remindEmailTip: 'Please input the correct email address',
        },
    },
    batchAddOptionsDialog: {
        batchAddOption: 'Bulk add items',
        batchAddTip1: 'One line for one field.',
        batchAddTip2: 'Maxium for 500 lines, and it\'s recommended that within 300 lines. The field name can not exceed 100 characters.',
        batchAddPlaceHolder: 'Please enter content',
        searchContentLengthLimitTip: 'Up to 500 standby options are supported, beyond which no processing is required',
        searchContentPlaceholder: 'Please enter a search term',
        confirm: 'Confirm',
        cancel: 'Cancel',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: 'Has been real-named',
        assignedIdentity: 'The contract subject filled in by the sender is:',
        entBeenAuthenticated: 'The company has been real-named, and the main administrator information is as follows:',
        entAdminName: 'Administrator name:',
        entAdminAccount: 'Administrator account:',
        applyToBeAdmin: 'I want to appeal',
        contactToJoin: 'Join the company',
        applicant: 'Applicant',
        inputYourName: 'Please enter your name',
        account: 'Account',
        send: 'Send',
        sendWishToJoin: 'You can become an administrator through account appeal, or you can send an application to the administrator to join the company',
        applyToJoin: 'You have not joined the company and cannot sign the contract. Do you want to apply for joining?',
        sentSuccessful: 'Sent successfully',
    },
    autoSeal: {
        add: 'Add',
        tip: 'Note',
        confirm: 'Confirm',
        cancel: 'Cancel',
        save: 'Save',
        preStep: 'Previous Step',
        nextStep: 'Next Step',
        done: 'Confirm',
        manageConfig: 'Administrator Settings',
        codeVerify: 'Enter Verification Code',
        signerConfig: 'Signer Settings',
        step1Tip: 'Step 1: Please assign the "{name}" to the enterprise members who need to use the  auto-stamping function.',
        step2Tip: 'Step 2: Enterprise members should go to',
        step2Tip1: 'User Center - My Seal',
        step2Tip2: 'page and enable the "Auto-stamping" switch. Thereafter, the system will automatically stamp any contracts they receive that meet the auto-stamping conditions.',
        openAutoTip: 'Below is the list of users who have enabled auto-stamping with \'{name}\':',
        howToOpenTip: 'How to configure automatic signing for enterprise members',
        signPsw: 'Signing password',
        forgetPsw: 'Forgot password',
        msgTip: 'Can\'t receive SMS all the time? try ',
        verificationCode: 'Verification Code',
        voiceVerCode: 'Voice verification code',
        SMSVerCode: 'SMS verification code',
        or: ' or ',
        emailVerCode: 'Email verification code',
        SentSuccessfully: 'Sending succeeded!',
        mail: 'Email',
        phone: 'Phone',
        inputPwd: 'Please enter the signing password',
        verCodeInputErr: 'Please get the verification code first!',
        verCodeFail: 'Incorrect verification code',
        inputNumber6: 'Please enter 6 digits',
        inputPwd6: 'Please enter a 6-digit signing password',
        sendInternalErr: 'The sending interval is too short',
        openAutoSeal: 'Enable automatic signing',
        closeAutoSeal: 'Disable automatic signing',
        closeAutoSealTip: 'After closing, the seal holder role/personnel cannot use the seal to automatically sign',
        autoSignTipTitle: 'Company members are allowed to automatically sign with this seal. The triggering conditions must meet the following requirements：',
        autoPersonSignTipTitle: 'Individuals are allowed to sign automatically with this signature stamp. The triggering conditions must meet the following requirements：',
        autoSealDialogTip: {
            1: 'The automatic signing feature has been activated. Newly received documents "Requiring My Signature" will be automatically signed soon.',
            2: 'Now you need to confirm: Do you want the system to automatically sign previously received contracts as well?',
            3: 'If yes, select "Yes" and click "Complete". The system will automatically use this seal to sign all eligible contracts. However, these contracts may take longer to process, please be patient.',
            4: 'If no, select "No" and click "Complete".',
        },
        iKnow: 'Complete',
        iSee: 'Get it',
        yes: 'Yes',
        no: 'No',
        defaultText: '* Default：',
        account: 'Account：',
        enterprise: '*Company：',
        contractContent: 'Contract content：',
        senderEnable: 'The sender allows the contract to be signed automatically',
        selfEntEnable: 'Contracts sent by the self-company',
        senderEntEnable: 'Contracts sent by designated companies',
        senderEnt: 'Sender company',
        receiveEnt: 'Receiver company',
        inputEntNamePlaceholder: 'Enter the company name',
        includeGroupMemeber: 'Include contracts sent by subsidiaries of our group',
        senderAccountEnable: 'Designated sender account',
        inputAccountPlaceholder: 'Enter account',
        phoneOrEamilTip: 'Please enter the correct cell phone number or email addres',
        unpointAccountTips: 'Without verification if not designated',
        contractIdEnable: 'Specify contract ID and lock similar contracts',
        inputContractIdPlaceholder: 'Enter comtract Id',
        inputContractIdTips: 'Please enter the correct contract ID',
        templateIdEnable: 'Designated contract template ID(Get from sender)',
        inputTemplateIdPlaceholder: 'Enter template Id',
        inputTemplateIdTips: 'Please enter the correct contract template Id',
        unpointContractContentTips: 'Without verification if not designated',
        pointSenderEnt: 'Please specify the sender company',
        versionTip: 'You need to open a higher version to check the "contract sent by this company".',
        pointEntName: 'Please specify the specific sender\'s company name',
        pointReceiveEnt: 'Please specify the recipient company',
        pointReceiveEntName: 'Please select the company name of the receiver',
        ponitSenderAccount: 'Please specify the sender\'s account number',
        pointContractContent: 'Please specify the specific contract content',
        notClickAddIconTip: 'Please click the Add button and then click the Save button again',
        maxAddLimit: 'Add up to 10 pieces of data',

        specialConfirmTip: 'Special note: When you fill in the information according to the automatic sign function page,check the corresponding function then saved. It means that you have fully read, understood and accepted all the checked items of this function.',
        warningTip: {
            title: '*During the operations, if you do not agree with any relevant content or items, you should immediately stop：',
            tip1: '(1) The content of the automatically signed contract is uncertain. Although you can designate the template ID to restrict the sender from using a certain contract template. However, the values of some fields in the contract template are not fixed, and the contract template may be modified.',
            tip2: '(2) You can not recall your seal or signature of the automatically signed contract. After you receive contracts which meet your setting conditions, the contract will be automatically signed immediately. If you read the contract in "Contract Management" later and it is inconsistent with your expectation, you need to contact the sender to cancel the contract or sign the Void statement.',
        },
        suggestionsTip: {
            title: '*If you have worries about the above risks, we suggest:',
            tip1: '（(1) Disable the automatic signing function.',
            tip2: '（(2) Confirm the sender\'s account number, contract content with the sender, and the automatic signing function can only be enabled when you fullly trust the sender.',
        },
        hasReadAndAccept: 'I have read and accepted the above notes.',
        acceptTipFirst: 'Please accept the above tips first',
        enableTipText: 'After the verification code is passed, the BestSign will use your digital certificate when signing corresponding contacts, and also provides automatic renewal service for your digital certificate. ',
    },
    certificationOutOfCourt: {
        dataRejected: 'material rejected',
        your: 'Your',
        authRejectReason: 'Reason for rejection',
        yourAuthInfo: 'Your certification information: | Real name: | Identification number:',
        baseInfo: 'Basic Information',
        handheldIdNum: 'Handhold ID card authentication',
        faceAuth: 'Facial verification authentication',
        noMainlandAuth: 'Non-Chinese mainland authentication',
        authNow: 'Authenticate immediately',
        underReview: 'Under approval',
        bankCardAuth: 'Bank card authentication',
        phoneAuth: 'Mobile phone number authentication',
        noMainlandPersonAuth: 'Non-Chinese mainland resident authentication',
    },
    certificationPermissionNoticeStatic: {
        authPermissions: {
            0: 'After finishing authentication, you will get the following permissions:',
            1: 'Start a contract ',
            2: 'with a new electronic signing mode',
            3: 'Improve account security',
            4: 'Enjoy more membership security services',
            5: 'Online contract management function',
            6: 'Improve business efficiency',
            7: 'Get electronic contract copies',
            8: 'Meet your needs for e-contracting',
        },
    },
    companyDeptTree: {
        searchTip: 'Search',
        searchDeptTip: 'Search',
        loadMore: 'load more...',
    },
    companyFull: {
        addMember: 'Add member',
        chooseMember: 'Select member',
        selectAll: 'Select all',
        choosedMember: 'Selected member',
        confirm: 'Confirm',
        noneMemberChoosedTip: 'Please select a member first',
    },
    recharge: {
        balance: 'Account balance',
        noLimitContract: 'Unlimited contract quantity',
        unLimitUseTip: 'Unlimited during the validity period',
        expireDate: 'Expiry date',
        recharge: 'Top-up',
        aiRecharge: 'Purchase of AI functions',
        ai: 'AI functions',
        riskJudge: 'Risk judgment',
        publicContract: ' To company',
        tip1: 'Documents with corporate accounts among the signers (excluding the sender)',
        tip2: 'Documents with no company signatory (excluding the sender)',
        useCopies: 'Number of copies available',
        withHoldCopies: 'Pre-deducted',
        withHoldCopiesTips: 'Number of contracts pre-deducted by the system after sending this contract. Refunded if transaction is not completed.',
        actuallDeductedCopies: 'Actual deduction',
        actuallDeductedCopiesTips: 'Number of contracts actually deducted by the system after completion of this transaction.',
        alreadyDeductedCopies: 'Consumed contracts quantity',
        privateContract: ' To individual',
        onlinePay: 'Online payment',
        noRefundAfterPay: '（Once purchased, this product/service cannot be returned or exchanged）',
        orderNum: 'Order number:',
        getMoreContract: 'Get more contracts as a bonus',
        discountCode: 'Discount code',
        plsInputDiscountCode: 'Please enter the discount code',
        discountCodeErrorTip: 'Discount code can only contain numbers!',
        discountTip: 'Before payment, check if the account has a discount code that can be used,A single order can only use a discount code once. If you need to use another discount code, please return and resubmit the order',
        documentAmout: 'Number of documents：{num} copies',
        amoutTo: 'Amount: ￥{money}',
        validUntil: 'Valid till',
        document: 'Documents',
        memberMumer: 'Number of members:',
        number: 'Number：',
        aliPay: 'Alipay',
        wechatPay: 'WeChat Pay',
        bankTransfer: 'Bank transfer',
        hotline: 'Hotline',
        customerService: 'Customer service',
        rechareSubject: 'Top-up by',
        rechareAccout: 'Top-up account',
        invoiceTip: '(i.e. invoicing entity, does not support changing the invoicing entity)',
        person: 'Person',
        ent: 'Enterprise',
        notAuth: 'No real name',
        curRechareDialog: {
            subject: 'Current top-up by',
            amount: 'Top-up account',
            tip: 'Please confirm the current top-up entity so as not to affect the signing process',
            notAuthTip: 'The current account has not undergone real name authentication, and the contract should be used after real name authentication. If the recharge contract needs to be used as an enterprise, please complete real name authentication before recharging.',
        },
        tip: 'Prompt',
        amout1: 'Amount:',
        yuan: 'Yuan',
        payValidTime: 'Payment time:',
        within24h: 'Within 24 hours',
        payNow: 'Pay now',
        selfTransferBankAccout: 'You can also self-transfer to the following bank account',
        accountName: 'Account name',
        account: 'Account',
        accountBank: 'Bank of Account',
        lineNumber: 'Bank code',
        rechareTip1: {
            title: 'Friendly reminder:',
            tip1: 'Please provide the order number in the payment information.',
            tip2: 'After transfer successfully, please contact customer service.',
        },
        paySuccess: 'Payment successful',
        myOrder: 'My order',
        useRecord: 'Usage history',
        batchInvoicing: 'Bulk invoicing',
        selectOrderTip: '{length} orders selected',
        orderMap: {
            num: 'Order number',
            setMeal: 'Package',
            amountAndType: 'Quantity and type',
            amountYuan: 'Amount (yuan)',
            amountYuanUae: 'Amount（$）',
            expireDate: 'Expiry date',
            expireNum: 'Expiration number',
            payStatus: 'Payment status',
            noPay: 'Unpaid',
            payDate: 'Payment time',
            invoiceStatus: 'Invoice status',
            applyInvoice: 'Apply for invoicing',
            reApply: 'Re-apply for invoicing',
            infoError: 'Incorrect information',
            createDate: 'Creation time',
            selectOneOrder: 'Please select one order or more',
            noInvoiceOrder: 'No invoiceable orders',
        },
        payStatusMap: {
            status1: 'Paid',
            status2: 'Closed',
            status3: 'Cancellation',
            status4: 'Error',
            status5: 'Unpaid',
        },
        billStatusMap: {
            status1: 'Apply for invoicing',
            status2: 'Applied',
            status3: 'Invoiced',
            status4: 'Re-apply',
        },
        supportBatchInvoiceTip: 'The following orders support bulk application for invoicing',
        batchApply: 'Bulk application',
        ssqDealTip: '( once received, will be processed within 7 working days by BestSign)',
        submit: 'Submit',
        confirm: 'Confirm',
        addNomalInvoice: 'VAT ordinary invoice',
        addSpecialInvoice: 'VAT special invoice',
        entNomalInvoiceMap: {
            tip1: 'VAT ordinary electronic invoice will be sent by email',
            entName: 'Company Name',
            taxNum: 'Tax code',
            amount: 'Invoicing amount',
            inbox: 'Inbox',
            plsInputEntName: 'Please enter company name',
            plsInputInbox: 'Please enter the inbox',
            plsInputEmail: 'Please enter the correct email address',
            plsInputTaxNum: 'Please enter the tax code',
            name: 'Name',
            plsInputName: 'Please enter your full name',

        },
        entSpecialInvoiceMap: {
            mailTip: 'Due to the nationwide promotion of digital electronic invoices, the platform no longer uses paper invoices. VAT special invoices are also received in the form of electronic mail （Default filling of last invoicing information, please check carefully）.',
            // tip1: 'Kind tips: Invoices below 600 yuan will be mailed with payment on delivery.',
            // plsAgreeTip: 'Invoices below 600 yuan, only support to pay by mail, please tick',
            entAddress: 'Company address',
            entTel: 'Company telephone number',
            bankAccount: 'Account bank',
            invoiceAmout: 'Account number',
            tip2: 'Upload general taxpayer certificate (pictures in jpg or png format)',
            tip3: 'Fill in the recipient information, Mailing you the invoice.',
            // recipientName: 'Recipient name',
            // recipientPhone: 'Recipient telephone',
            // recipientAddress: 'Address of the recipient',
            plsInputEntAddress: 'Please enter the company address.',
            plsInputEntPhone: 'Please enter the company phone number.',
            plsInputBankName: 'Please enter the account bank.',
            plsInputBankAccount: 'Please enter the account number.',
            plsUploadCertificate: 'Please upload the general taxpayer certificate (pictures in jpg or png format).',
            // plsInputRecipientName: 'Please enter the recipient name.',
            // plsInputRecipientPhone: 'Please enter the recipient telephone.',
            // plsInputRecipientAddress: 'Please enter the recipient address.',
            picNotExceetTip: 'Picture size cannot exceed 5M',
            picNotMeetRequire: 'The image format does not meet the requirements',
            clickUpload: 'Click to upload',
            certifyProvePic: 'General taxpayer certificate ',
        },
        errorTip: 'Error',
        applyInvoiceSubmit: 'Your invoicing request has been submitted!',
        getInvoiceTip: 'You will receive the invoice within 7-15 working days after the information is verified!',
    },
    common: {
        close: 'close',
        confirmClose: 'Confirm to close?',
        time: 'Time',
        day: 'Day',
        month: 'Month',
        groupAgentAuth: '(Group assist authentication)',
        plsSelectEnt: 'Please select the company first',
        checkVideoTip: 'View operation guide video!',
        selectBusLine: 'Please select business line',
        entChanged: 'Please refresh the page.',
        refreshPage: 'Refresh',
    },
    commonHeader: {
        console: 'Organization Management Platform',
        home: 'Home',
        contractManagement: 'Contract',
        templateManage: 'Template',
        statisticCharts: 'Report',
        service: 'Service',
        offlineContractManage: 'Offline Contract',
        contractDrafting: 'Contract drafting',
        fileService: 'File +',
        authenticating: 'Authenticates...',
        unAuthenticate: 'Do not authenticate',
        rejectAuthenticate: 'Authenticate rejected',
        entAccount: 'Company account ',
        personAccount: 'Individual account ',
        enterpriseConsole: 'Company console',
        createEnt: 'Create a company',
        viewDetail: 'Click for details',
        message: 'Message',
        video: 'Set video',
        tip: 'Notice',
        ok: 'Understood',
        entName: 'Company Name',
        createCompnayP: {
            hasUnAuthEnt: 'If there are still enterprises under your account that have not completed the certification, please complete the certification of the "Enterprises that have not completed the certification" or cancel the certification before adding a new enterprise.',
            toAuth: 'To certification',
            toCancel: 'To cancel',
            p1: 'Coming soon to generate a new business for you, please improve the company name',
            p2: 'Immediately provide the relevant company materials (for identification), and get the name of the company',
            p3: 'Temporarily not authenticated, fill in manually',
            p4: 'Uncertified business names show only to themselves,',
            loadingTxt: 'Checking data, please wait.',
            p5: 'Please submit your enterprise certification information. The expected review time is 1-3 working days.',
            p6: 'If you need to know the progress of the review or have any questions, please feel free to contact us by email at any time: <EMAIL> We will wholeheartedly provide you with assistance.',
            p7: 'Seal certificate',
            p8: 'Please upload the seal certificate first',
            p9: 'Under review. If you need to know the progress of the review, you can contact us by email: <EMAIL>',
            areaRegister: 'Country of Incorporation',
            jp: 'Japan',
            cn: 'Chinese Mainland',
            are: 'United Arab Emirates',
            other: 'Other',
            plsSelect: 'Please select',
            tip1: 'Enterprises registered in Chinese Mainland need to complete real name registration in ent.bestsign.cn. When signing contracts with enterprises outside the Chinese Mainland, the "cross-border signing" function can be used to efficiently complete mutual signing of contracts on the premise of ensuring the security of user data without leakage.',
            tip2: 'If your enterprise has signed on the Chinese Mainland version to complete the real name authentication, you can directly log on to ent.bestsign.cn for convenient use of related services. It should be noted that the data generated by signing the overseas version is completely independent from the Chinese Mainland version.',
            tip3: 'Please provide the identification number you obtained from the local commercial regulatory agency',
            tip4: 'Supporting Documents',
            tip5: '1. Please contact your dedicated account manager to guide you through enterprise verification.',
            tip6: '2. Please provide screenshots of your commercial contract with Esign or business correspondence emails with your dedicated account manager.',
            tip7: '3. This verification method is only available for enterprises outside Mainland China and Japan.',
            tip8: '4. The review will be completed within 3 business days after submission.',
            comNum: 'Enterprise ID number',
            plsEnterComNum: 'Please enter the Enterprise ID number',
            plsUploadBuyRecord: 'Please upload supporting documents',
            uaeTip1: 'Enterprises registered in the United Arab Emirates must complete real name registration on uae.bestsign.com. When signing contracts with companies outside the United Arab Emirates, the "cross-border signing" function can be used to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',
            uaeTip2: 'If your company has completed real name authentication on the UAE version of Shangshang, you can directly log in to uae.bestsign.com to conveniently use related services. It should be noted that the data generated by the overseas version you signed on is completely separate from the UAE version.',
            uaeTip3: 'Enterprises registered outside the United Arab Emirates and Chinese Mainland need to complete real name registration in ent.bestsign.com. When signing contracts with companies in the United Arab Emirates, the "cross-border signing" function can be utilized to efficiently complete the mutual signing of contracts while ensuring the security and confidentiality of user data.',
        },
        enterEnterprise: 'Please enter company name',
        cancel: 'Cancel ',
        chooseEnterprise: 'Choose a company',
        noResult: 'no result',
        confirm: 'Confirm',
        plzEnterRightEnt: 'Please enter the correct company name',
        plzEnterRightCorporate: 'Please enter the correct corporate number',
        selectArea: 'Please select the registered address of the enterprise',
        corporateNumber: 'corporate identification number',
        corporateNumberTip: 'Please enter the corporate identification number',
        createSuccess: 'Created successfully',
        viewAllEnt: 'View all companies',
        addEnt: 'Add new company ',
        exit: 'Log Out',
        userCenter: 'Admin',
        groupConsole: 'Group console',
        backToHome: 'Back to Home',
        connectToPrivateNetTip: 'Your company has adopted contract private storage. The current network is connected to the contract storage server.',
        noConnectToPrivateNetTip: 'Your company uses contract private storage, and the current network cannot connect to the contract storage server',
        advice1: 'Suggest: ',
        checkNetTip1: 'Check whether the current network can access the corporate intranet',
        ecology: '来自{developerName}的企业账号',
        ecologyPerson: '来自{developerName}的个人账号',
        hybridNetworkDetail: '混合云网络详情',
        requestUrl: '请求地址',
        statusCode: '状态码',
        timeCost: '耗时',
        hybridStatusCodeExplain: {
            Error: '请检查当前网络状态是否正常连接',
            200: '成功',
            403: '请求地址不可用',
            404: '请求地址不存在',
            409: '业务异常',
            500: '请检查合同存储服务器是否正常运行',
        },
        switchConfirm: {
            title: '切换主体提示',
            main: '请确认，您将切换至个人主体?',
            tip: '切换至个人主体后，无法使用模板配置、企业管理等企业功能。发出的合同也将以您的个人身份发出。如需开展企业业务，请务必切回企业主体。',
            confirm: '暂不切换',
            cancel: '继续切换至个人',
        },
    },
    commonFooter: {
        record: 'ICP main body record number: Zhejiang ICP No. 14031930',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: 'Open platform',
        aboutBestSign: 'About BestSign',
        contact: 'Contact us',
        recruitment: 'Recruitment',
        help: 'Help Center',
        copyright: 'Copyright',
        company: 'HangZhou BestSign Ltd.',
        ssqLogo: 'BestSign bottom bar logo',
        provideTip: 'E-signing service is provided by',
        ssq: ' BestSign',
        provide: '',
        signHotline: 'Service hotline',
        langSwitch: 'Language',
    },
    footerSimpler: {
        ssqDes: 'Leader of electronic signing cloud platform',
    },
    mainChart: {
        sendVolume: 'Number of sendings',
        signFinishAmout: 'Number of completed signings',
        contractSendAmout: 'Number of contracts sendings',
        contractSignAmout: 'Number of contracts signings',
        billion: '100 million',
        wan: '10 thousand',
        fen: 'copies',
        ci: 'copies',
    },
    resetPassword: {
        safeTip: 'Safety Warning!',
        weakPswTip: 'Your password is not secure, there are potential security risks. Please reset your password!',
        oldPsw: 'Old password',
        plsInputOldPsw: 'Please enter the old password.',
        newPsw: 'New password',
        pswRule: ' 6-18 digits, upper and lower case letters, support common special characters',
        confirm: 'Confirm',
        oldPswNotEmpty: 'The old password cannot be empty.',
        newPswNotEmpty: 'The new password cannot be empty.',
        modifySuccess: 'Successfully modified',
    },
    passwordExpiration: {
        notice: 'Your password has not been changed for 90 days. Please change it in time according to the requirements of your enterprise.',
        btn: 'Go to change',
    },
    giftContract: {
        expiryDateUtil: 'Use period until ',
        buyTip: 'It is recommended that you finish real name authentication first and then purchase the E-signature package. The default account is a individual account.',
        personAuthTip: 'Congratulations, individual authentication succeed.',
        entAuthTip: 'Congratulations, company authentication succeed.',
        openSsqTip: 'Congratulations on Completing Your Account Registration.',
        registerTip1: 'Upon completing the first contract dispatch, additional contract copies can be obtained for free.',
        registerTip2: ' more free copies',
        registerTip3: 'Complete company real name certification to get ',
        giftTip1: 'BestSign gifts you ',
        giftTip2: ' copies of "To individual contracts"',
        and: ' and ',
        giftTip3: ' copies of "To company contracts"',
        sendContractTip: 'Upon completing the first contract dispatch, additional contract copies can be obtained for free.',
        entAuth: 'Go to Company Certification',
        personAuth: 'Go to Personal Certification',
    },
    noticeMap: {
        notice: 'Messages ',
        notRead: ' unread)',
        allRead: 'All read',
        viewMore: 'View more',
    },
    rechargeOrder: {
        searchType: 'Search type:',
        deductTime: 'Deduction date',
        selectDateRange: 'Select the period of time:',
        till: 'to',
        startDate: 'Starting date',
        endDate: 'Ending date',
        search: 'Search',
        sendTime: 'Sending time',
        contractTitle: 'Contract name',
        contractId: 'Contract ID',
        user: 'User',
        belongDepartment: 'Department',
        contractType: 'Contract type',
        consumedCopies: 'Consumed contracts quantity',
        consumedCopiesTips: 'Theoretical number of contracts required for this transaction.',
        personConsumedCopiesTips1: '1. The consumption record shall be based on the actual contract package category used. For example, if a private contract is sent but there are no available private contract packages at the time, and they are deducted from the corporate package, the consumption record will be displayed as one corporate contract.',
        personConsumedCopiesTips2: '2. Starting from April 21, 2025, ordinary individual users will no longer enjoy the discount of returning the number of contracts due to contract expiration, withdrawal, or refusal to sign.',
        exportMaxTip: 'Export data cannot be greater than 10000',
        export: 'Export',
        exportTip: "Development time is too long, please don't hesitate to contact us before closing. Open window processing at your convenience",
        batchCenter: 'Batch Task Center',
        batchExportTip: 'In the background analysis, please check the results of this operation in the "Batch Task Center".',
    },
    rechargePackage: {
        manage: 'Top-up management',
        selectPackage: 'Select a package',
        tip1: 'More than ￥40000 please call customer service: ************',
        onlineService: 'Online customer service: ',
        tip2: 'Offer ends on December 31st',
        ccbPublicContract: 'To company contract of Construction Bank ',
        publicContract: 'To company contract',
        copiesTip: 'Copies of contracts: {num}',
        vailidityPeriodTip: 'Validity period: {month} months',
        ccbPrivateContract: 'To individual contract of Construction Bank ',
        privateContract: 'To individual contract',
        copiesTip1: 'Copies of contracts:',
        giveCopiesTip: '+ {num}',
        buyNow: 'Buy Now',
        advancedFeature: 'Advanced features purchase',
        learnDetail: 'Learn more',
        isUsing: 'Opened',
        unUsing: 'Not opened',
        payAmount: 'Payment amount',
        placeOrder: 'Buy Now',
        renewal: '续费购买',
        year: 'year',
        limitBugTip: 'Special offer limited to one purchase',
    },
    editFieldDialog: {
        chooseFieldType: 'Select field type:',
        fieldType: {
            text: 'Text',
            single: 'Radio',
            multi: 'Checkbox',
            date: 'Date',
            combo: 'Dropdown',
            number: 'Number',
            datetime: 'Time',
        },
        name: 'Name',
        namePlace: 'Please enter a name',
        options: 'Optional items',
        addOptions: 'New optional items',
        contentFiller: 'Content Filler',
        sender: 'Sender',
        dateFormate: 'Date format',
        senderContent: 'If you select a sender, the content of this contract will be filled in by the sender before it is sent.',
        signer: 'Signatory',
        signerContent: 'If you select a signatory, the content of this contract will be filled in by the signatory upon signing.',
        fontSize: 'Font size',
        fontSizePlace: 'Please select the font size',
        requirements: 'Requirements on filling in the inforamtion',
        required: 'Required',
        confirm: 'Confirm',
        cancel: 'Cancel',
        addField: 'Add Field',
        editField: 'Edit Field',
        integer: 'Integer',
        decimalLimit: 'Limit',
        decimal: 'decimals',
        errorMsg: {
            optionEmpty: 'The name of the optional item cannot be empty',
            optionHasEnComma: 'Option names cannot include English commas',
            optionNameFormat: 'The names for optional items can only be a combination of Chinese, English, and numbers',
            optionNameRepeat: 'The names of optional items cannot be the same',
            enterFieldName: 'Please enter the field name',
            fieldNameExist: 'The field name already exists. Please rename it',
            fieldNameExistToOn: 'The field name already exists. Please enable the field that has the same name',
            optionsNeed: 'Please add at least two options',
            overCountLimit: '最多支持500个备选项',
        },
        editSuccess: 'Successfully edited',
        addSuccess: 'Successfully added',
        labelAlign: 'Alignment',
    },
    functionSupportDialog: {
        title: 'Function introduction',
        inputTip: 'If you have relevant use needs, please fill in your needs in the following form. BestSign will arrange professionals to contact you and provide service guidance within 24 hours.',
        useSence: 'Application scenario',
        useSenceTip: 'Such as HR, dealers, logistics documents, etc',
        estimatedOnlineTime: 'Expected launch date',
        requireContent: 'Requirements',
        requireContentTip: 'Please describe how your company will use e-signature so that we can provide appropriate solution for you',
        getSupport: 'Get professional service support',
        callServiceHotline: 'Hotline：************',
        useSenceNotEmpty: 'The usage scenario cannot be empty',
        requrieContentNotEmpty: 'The demand content cannot be empty',
        oneWeek: 'Within a week',
        oneMonth: 'Within a month',
        other: 'Others',
        submitSuccess: 'Submitted successfully',
        submitTrial: 'Submit for trial',
        toTrial: 'To trial',
        trialTip: 'After submitting a trial application, the current function will be immediately activated and available for trial. In order to better help you use the features, you can fill in more requirements in the form below. BestSign consultant will contact you to provide services.',
        applyTrial: 'Apply for trial',
        trialSuccTip: 'The function has been activated. Welcome to try it out',
        goBuy: 'Buy now',
        buyLater: 'Buy later',
        trialTipMap: {
            title: 'Trial instructions',
            tip1: '1. Instant use, valid for 7 days；',
            tip2: '2. During the trial period, the function will not be charged；',
            tip3: '3. Each company entity has only one trial opportunity for a function；',
            tip4: '4. Self-service purchase is available during the probation period, and the use is uninterrupted;',
            tip5: '5. If your trial has ended, you can scan the code and contact the BestSign professional consultant for details:',
        },
        contactAdminTip: 'To use, please contact your enterprise administrator {tip} to purchase and open',
        trialEndTip: 'After the trial period, click to buy',
        trialRemainDayTip: 'There are {day} days left in the trial period, click to purchase copies',
        trialEnd: 'Trial End/Feature Expiration',
        trialEndMap: {
            deactivateTip: '{feature} feature has been disabled. Please clear the configuration or renew it before continuing to use it.',
            feature1: 'Contract Collateral',
            remove1: 'The method for clearing the configuration is: Edit the template - find the configured additional contract attachment data and delete it.',
            feature2: 'Handwriting handwriting recognition',
            remove2: 'The method for clearing the configuration is: Edit the template - Find the configured handwriting recognition and delete it.',
            feature3: 'Contract decoration: riding seal+watermark',
            remove3: 'The method to clear the configuration is: Edit Template - Find the configured contract decoration and delete it.',
            feature4: 'Contract sending approval',
            remove4: 'The method for clearing the configuration is: Enterprise Console - Deactivate all approval processes',
            feature5: 'The template authorization function has expired and been deactivated. The template authorization schemes have all been deactivated, and only the main administrator has permission to use them. You can recreate the template for use, or notify the administrator to renew it before continuing to use it.',
            remove5: '',
        },
    },
    RechargeAccount: {
        orders: {
            pay: 'Pay',
            money: 'Amount',
            monyUnit: 'Dollars',
            jaMonyUnit: 'Yen',
            main: 'The entity of recharge',
            chargeNow: 'Pay',
            payApproachType: 'Debit or credit card',
            poweredBy: 'Powered by',
            // orderId: 'Order number',
            // contractNum: 'Contract num',
            // moneyWithUnit: 'Amount(yuan)',
            // actualMoney: 'Actual payment amount',
            // date: 'Due Date',
            numberUnit: 'copies',
            // goPay: 'Pay Now',
            // dueDate: 'Expiration date',
            // account: 'The account of recharge',
            // unpaied: 'Did not pay',
            // paied: 'Have to pay',
            // paymentTime: 'Payment time',
            // paymentStatus: 'Payment status',
            // payResultDialogTitle: 'Payment Info',
            // payResultSuccess: 'Successfully Payed',
            // useNow: 'Immediate use',
            // limitedTime: 'Limit free use',
        },
    },
};
// 基础组件翻译
const components = {
    helperFloat: {
        advice: 'Advice',
        hide: 'Hide',
        suggest: 'Suggestions',
        onlineService: 'Online customer service',
        minimize: 'Minimize',
        enlarge: 'Enlarge',
        zoomOut: 'Zoom out',
        person: 'Individual',
        ent: 'Company',
        checkMoreVersion: 'View more versions',
        submitSuccess: 'Successfully reported',
        reportLog: 'Report',
    },
    countDown: {
        resendCode: 'Reacquire',
        getVerifyCode: 'Get code',
    },
    messageBox: {
        confirm: 'Confirm',
    },
    example: {
        sideDisplay: 'Side display',
        clickPreview: 'Click to preview',
        previewTitle: 'Preview the title',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: 'Seal Notification',
            tip: 'We detected that your current seal image does not match your company identity, the current seal image recognition result:',
            tip2: 'Do you want to continue using the current seal image?',
            guide: 'How to upload the correct seal >>',
            next: 'Continue to use',
            tip3: 'And your seal name does not comply with the specifications, with the words “{keyWord}” .',
            tip4: 'Detected that the seal name does not comply with the specifications and contains the words “{keyWord}”. Do you want to continue using it?',
        },
        exampleSeal: {
            title: 'Ways to Upload Seal Images',
            way1: ['Method One:', '1. Stamp a physical seal on white paper', '2. Take a photo and upload the image to the platform'],
            way2: ['Method Two:', 'Directly use the electronic seal generation feature on the platform, as shown:'],
            errorWay: ['Incorrect Methods:', 'Holding the seal in hand', 'Irrelevant images', 'Business license'],
        },
        confirm: 'Confirm',
        cancel: 'Cancel',
    },
};

// 公共库内其他用到翻译的内容
const commonother = {
    validationMsg: {
        notEmpty: 'Can not be empty',
        enter6to18n: 'Please enter 6-18 digits, uppercase or lowercase letters',
        enter8to18n: 'Please enter 8-18 digits, uppercase or lowercase letters',
        errEmailOrTel: 'Please input correct Email or telephone number!',
        verCodeFormatErr: 'Incorrect verification code',
        signPwdType: 'Please enter 6 digits',
        enterActualEntName: 'Please enter the real company name',
        enterCorrectName: 'Please enter the correct name',
        enterCorrectPhoneNum: 'Please enter the correct mobile phone number',
        enterCorrectEmail: 'Please enter the correct email address',
        imgCodeErr: 'Incorrect graphic verification code',
        enterCorrectIdNum: 'Please fill in the correct ID number',
    },
    pubTips: {
        loginOverdue: 'Login has expired, please log in again',
        errorTip: 'Error message',
        know: 'Understood',
        serverError: 'The server has a small gap, please try again later.',
    },
    commonNomal: {
        yesterday: 'Yesterday',
        ssq: 'BestSign',
        ssqPlatform: 'BestSign E-signature cloud platform',
        ssqTestPlatform: '(For testing purposes only) BestSign E-Signature Cloud Platform',
        pageExpiredTip: 'The page has expired, please refresh and try again',
        pswCodeSimpleTip: 'The password must contain 6-18 digits and uppercase and lowercase letters, please reset',
    },
    codeHandlerMap: {
        tip1: 'Your right to use the template was withdrawn by the creator of the template',
        tip2: 'This template has been deleted by the creator',
        tip3: 'This template has been disabled by the creator and can no longer be used',
        tip4: 'Unable to use the template',
        tip5: 'Back to the list of templates',
        errorTip: 'The server went up a bit, please try again later',
        getNewJar: 'Please contact BestSign to upgrade the jar package',
        networkNotConnect: 'The network is unavailable, please check your network settings.',
        notAccessPage: 'Can\'t access this page',
        page404: 'The page you visited was not found (404)',
        goHome: 'Back to Home',
        downloadApp: 'Download the BestSign app',
        advancedFeatureError: 'This feature is only available for premium users. Please contact BestSign for an upgrade.',

    },
    catchMap: {
        view: 'View',
        download: 'Download',
        reject: 'Refuse',
        revoke: 'Revoke',
        delete: 'Delete',
        cantOperate: 'Unable to {operate} contract',
        hybridNetHeader: 'The sender\'s enterprise uses the contract private storage method, but the current network cannot connect to the sender\'s contract storage server.',
        hybridNetMsg: 'I suggest you check if the network has been connected to the sender\'s intranet and try again.',
        checkNet: 'Please check if the network is connected to the intranet.',
        hybridNotConnect: 'Reason: Your company uses contract private storage, but the current network cannot connect to the contract storage server.',
        hybridSuggest: 'Recommended: (1) Check whether the network is normal; (2) Check whether the contract storage server is running normally',
        goHomePage: 'Back to Home',

    },
    // 日文版签署前实名
    authBeforeSignJa: {
        plsConfirmIdentity: 'Please confirm your identity',
    },
    fontSizeMap: {
        one: '26',
        smallOne: '24',
        two: '22',
        smallTwo: '18',
        three: '16',
        smallThree: '15',
        four: '14',
        smallFour: '12',
        five: '10.5',
        smallFive: '9',
    },
    replaceEmpty: 'It is detected that the enterprise name/person name you entered has spaces, which may affect the signing. We have removed the space for you.',
};

export default {
    ...businessComponents,
    ...components,
    ...commonother,
    lang: 'en',
};
