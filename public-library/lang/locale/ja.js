// 業務モジュール翻訳
const businessComponents = {
    userHeader: {
        hi: 'こんにちは',
        exit: 'ログアウト',
        help: 'ヘルプ',
        hotline: 'サービスホットライン',
    },
    TagManage: {
        labelOperate: {
            editLabel: 'ラベル名の編集',
            setRule: 'ラベリングルールの設定',
            dropRule: '设置撕标签规则',
            setPutRemind: 'ティアタグのルールを設定する',
            afterDays: '日後',
            remindPeople: '義務者の方への注意喚起',
            edit: '編集部',
            labelNameLengthLimit: 'ラベル名は最大15文字までとする',
            labelNameRequire: 'ラベル名を入力してください',
            setLblRuleTip: '“{name}”のタグ付けルールを設定する',
            remind: 'リマインダー',
            done: '仕上がり',
        },
        putLabelRules: {
            name: 'ラベリング',
            tip: '*イベントが発生すると、コントラクトに自動的にそのラベルが付けられます。 そのため、最初にトリガーとなるイベントを選択してください：',
            manual: '手動ラベリング',
            afterSent: '契約書送付後',
            inputDaysTip: '日数を入力してください',
            beforeSignDeadline: '契約締結前',
            afterDone: '契約完了後',
            beforeDue: '契約満了前',
            afterDue: '契約満了後',
            afterReject: '契約拒否',
            day: '日',
            minSet: '少なくとも設定する',
            maxSet: 'ほとんどの設定',
        },
        dropLabelRules: {
            name: 'ラベルをはがす',
            tip: 'イベントがトリガーされると、コントラクト タグが自動的に切り離されます。 トリガーイベントを選択してください：',
            manual: 'ラベルを手ではがす',
            afterDone: '契約完了後',
            afterDue: '契約満了後',
        },
        remindRule: {
            tip: '担当者に通知を送信するリマインダー時間を設定する：',
            remindDays: 'リマインダー時間（ラベルを貼ってから 3 日後に通知を送信するなど）',
            remindDayTip: '日数を入力してください',
            remindPerson: '人々に思い出させる',
            remindPersonTip: 'メールアドレスを入力してください',
            addRemindPerson: '通知メールを追加',
            remindEmailTip: '正しいメールアドレスを入力してください',
        },
    },
    batchAddOptionsDialog: {
        batchAddOption: '一括追加任意',
        batchAddTip1: 'デフォルトでは行を単位に、1行1項目として、順番に記入してください。',
        batchAddTip2: '最大500件までサポートされ、最大300件、名前の長さは100文字以内を推奨しています。',
        batchAddPlaceHolder: '内容を入力してください',
        searchContentLengthLimitTip: '500件までの任意に対応しており、それ以上は処理されません',
        searchContentPlaceholder: '検索ワードを入力してください',
        confirm: '確認',
        cancel: 'キャンセル',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: '実名登録されています',
        assignedIdentity: '発信者の記入した契約主体：',
        entBeenAuthenticated: '当該企業は実名登録されています。管理者主任の情報は次のとおりです：',
        entAdminName: '管理者氏名：',
        entAdminAccount: '管理者アカウント：',
        applyToBeAdmin: '管理者主任として申請します',
        contactToJoin: '管理者に連絡して企業に参加',
        applicant: '申請者',
        inputYourName: 'お客様の氏名を入力してください',
        account: 'アカウント',
        send: '発信',
        sendWishToJoin: 'アカウントを通して管理者として申請できます。また管理者に企業の参加申請を送ることもできます',
        applyToJoin: 'お客様はまだ当該企業に参加されていないため、当該契約に署名できません。参加申請をしますか？',
        sentSuccessful: '送信完了',
    },
    autoSeal: {
        add: '追加',
        tip: '注意',
        confirm: '確認',
        cancel: 'キャンセル',
        save: '保存＃ホゾン＃',
        preStep: '前へ',
        nextStep: '次のステップ',
        done: '完了',
        manageConfig: '管理者構成',
        codeVerify: '認証コードの入力',
        signerConfig: '署名者設定',
        step1Tip: 'ステップ1：自動捺印機能を使用する必要があるエンタープライズメンバーに「{name}」を割り当ててください。',
        step2Tip: 'ステップ2：企業メンバーの皆さんに入力してください',
        step2Tip1: 'ユーザーセンター-マイスタンプ',
        step2Tip2: '「自動押印」スイッチをオンにします。その後、彼が受け取った契約書の中で自動署名の条件に合致すれば、システムは自動的に押印する。',
        openAutoTip: '「{name}」オートスタンプ機能が有効になっているリストは次のとおりです:',
        howToOpenTip: 'エンタープライズメンバーが自動チェックインを構成する方法',
        signPsw: '署名パスワード',
        forgetPsw: 'パスワードをお忘れの場合',
        msgTip: 'SMSを受け取っていません。試す',
        verificationCode: '認証コード',
        voiceVerCode: '音声認証コード',
        SMSVerCode: 'SMS認証コード',
        or: 'または',
        emailVerCode: '電子メール認証コード',
        SentSuccessfully: '送信完了',
        mail: 'メールアドレス',
        phone: '携帯電話',
        inputPwd: '契約パスワードを入力してください',
        verCodeInputErr: '認証コードを取得してください',
        verCodeFail: '認証コードエラーです',
        inputNumber6: '6桁の数字を入力してください',
        inputPwd6: '6桁の契約パスワードを入力してください',
        sendInternalErr: '送信時間間隔が短すぎます',
        openAutoSeal: '自動捺印を有効にする',
        closeAutoSeal: '自動捺印を無効にする',
        autoSealDialogTip: {
            1: '自動署名機能が有効になりました。新しく受信した「署名が必要」な文書は自動署名されます。',
            2: 'で確認してください：以前に受信した契約書も自動署名しますか？',
            3: '自動署名する場合は、「はい」を選択して「完了」をクリックしてください。システムは自動的にこの印鑑を使用して、すべての対象となる契約書に署名します。これらの契約書の処理には時間がかかる場合がありますので、しばらくお待ちください。',
            4: '自動署名しない場合は、「いいえ」を選択して「完了」をクリックしてください。',
        },
        iKnow: '完了',
        iSee: 'わかりました。',
        yes: 'はい',
        no: 'いいえ',
        closeAutoSealTip: '無効にすると、印章を所有する役割/所有者が継続してこの印章の自動署名を使えなくなります',
        autoSignTipTitle: '企業メンバーの許可によりこの印章の自動署名を使用するには、トリガーとなる条件には次のことを同時に満足する必要があります：',
        autoPersonSignTipTitle: '個人の許可によりこの署名の自動署名を使用するには、トリガーとなる条件には次のことを同時に満足する必要があります：',

        defaultText: '*デフォルト：',
        account: 'アカウント：',
        enterprise: '*企業：',
        contractContent: '契約内容：',
        senderEnable: '発信者が契約の自動署名を許可',
        selfEntEnable: '本企業の発信した契約書',
        senderEntEnable: '発信者企業の指定',
        senderEnt: '発信者企業',
        receiveEnt: '受信先企業',
        inputEntNamePlaceholder: '企業名を入力してください',
        includeGroupMemeber: '当社グループ子会社から送付された契約を含む',
        senderAccountEnable: '発信者アカウントの指定',
        inputAccountPlaceholder: 'アカウントを入力してください',
        phoneOrEamilTip: '正しいメールアドレスを入力してください',
        unpointAccountTips: '指定しない場合、アカウントの検証は行われません',
        contractIdEnable: '契約書番号を指定すると、酷似する契約書がロックされます',
        inputContractIdPlaceholder: '契約書番号を入力してください',
        inputContractIdTips: '正しい契約書番号を入力してください',
        templateIdEnable: '契約書テンプレート番号の指定（発信者から取得する必要があります）',
        inputTemplateIdPlaceholder: 'テンプレート番号を入力してください',
        inputTemplateIdTips: '正しい契約書テンプレート番号を入力してください',
        unpointContractContentTips: '指定しない場合、契約内容の検証は行われません',
        pointSenderEnt: '発信者企業を指定してください',
        versionTip: 'お客様はより上位のバージョンを有効にすることで「本企業が発送した契約書」にチェックを入れることができます。',
        pointEntName: '具体的な発信者企業名を指定してください',
        pointReceiveEnt: '受信者企業を指定してください',
        pointReceiveEntName: '具体的な受信者企業名を指定してください',
        ponitSenderAccount: '具体的な発信者アカウントを指定してください',
        pointContractContent: '具体的な契約内容を指定してください',
        notClickAddIconTip: '最初に「+追加」ボタンをクリックしてから「保存」ボタンを再度クリックしてください',
        maxAddLimit: '最大10件までのデータを追加',

        specialConfirmTip: '重要注意：自動署名機能ページで指示される通り情報を入力し、対応する機能にチェックを入れ保存をクリックすると、お客様は規約のすべての内容についてすべて読み、理解し、当該機能のチェック済みの内容すべてを承諾したことを示すことになります。',
        warningTip: {
            title: '*上記の操作過程において、関連内容もしくはその中のいかなる内容の取り決めに同意されない場合、すぐに操作を中止しなければなりません。',
            tip1: '（1）自動署名の契約内容が不明確です。テンプレートIDを指定することで、送信者は必須契約書テンプレートに限定することができますが、契約書テンプレートの一部のフィールドの値は固定されておらず、契約書テンプレートは修正される可能性があります。',
            tip2: '（2）自動署名後の契約書にお客様は捺印や署名の撤回ができません。お客様が契約書を受け取った後お客様の設定条件を満足してからすぐに署名してください。もしもその後「契約書管理」の中で閲覧した契約書がお客様の想定と異なる場合、発信者に連絡して契約書を解約するか、署名の無効宣言をする必要があります。',
        },
        suggestionsTip: {
            title: '*もしもお客様が上記のリスクに懸念があるようであれば、',
            tip1: '（1）自動捺印機能を無効にする。',
            tip2: '（2）もしくは発送者企業と契約書発信者アカウント・契約内容を明確にしてもらい、十分に信頼できるという基準の上で自動捺印機能を有効にしてください。',
        },
        hasReadAndAccept: '私は以上の注意をよく読み同意いたしました',
        acceptTipFirst: '先に以上の注意を同意してください',
        enableTipText: '有効後、署名時にベストサインはお客様のデジタル証明書をコールし、お客様のために有効期限まで自動継続署名サービスを提供します。',
    },
    certificationOutOfCourt: {
        dataRejected: '資料は却下されました',
        your: 'お客様の',
        authRejectReason: '却下理由',
        yourAuthInfo: 'お客様の認証情報：| 実名：| 証明書番号：',
        baseInfo: '基本情報',
        handheldIdNum: '所持身分証明書認証',
        faceAuth: '顔認証',
        noMainlandAuth: '非中国大陸認証',
        authNow: '直ちに認証',
        underReview: '審査中',
        bankCardAuth: '銀行口座認証',
        phoneAuth: '携帯電話認証',
        noMainlandPersonAuth: '非中国大陸人士認証',

    },
    certificationPermissionNoticeStatic: {
        authPermissions: {
            0: '実名認証認証後、以下の権限が取得できます。',
            1: '契約書の起稿',
            2: '全く新しい電子契約パターンの使用',
            3: 'アカウントの安全性を向上',
            4: 'より多くのメンバー保障サービスを享受',
            5: 'オンライン契約書管理機能',
            6: '企業効率の向上',
            7: '電子契約部数の取得',
            8: '電子契約に対するニーズへの対応',
        },
    },
    companyDeptTree: {
        searchTip: 'アカウントor氏名で検索',
        searchDeptTip: 'エンタープライズ/部門検索の入力をサポート',
        loadMore: 'load more...',
    },
    companyFull: {
        addMember: 'メンバーの追加',
        chooseMember: 'メンバーの選択',
        selectAll: 'すべて選択',
        choosedMember: '選択済みメンバー',
        confirm: '確定',
        noneMemberChoosedTip: '先にメンバーを選択してください',
    },
    recharge: {
        balance: 'アカウント残高',
        noLimitContract: '契約書無制限',
        unLimitUseTip: '有効期間内は無制限で使用',
        expireDate: '満期日時',
        recharge: 'リチャージ',
        aiRecharge: 'AI機能購入',
        ai: 'AI機能',
        riskJudge: 'リスク判断',
        publicContract: '企業向け契約書',
        tip1: '署名者（送信者を含まず）の中に企業アカウントがある文書',
        tip2: '署名者（送信者を含まず）の中に企業アカウントがない文書',
        useCopies: '使用可能部数',
        withHoldCopies: '予定差し引き部数',
        withHoldCopiesTips: '本契約業務送信後、契約件数が引き落とされ、契約業務完了しない場合は、引き落とした件数が返還されます。',
        actuallDeductedCopies: '実質差し引き部数',
        actuallDeductedCopiesTips: '本契約業務完了後、実際に引き落とした契約件数。',
        alreadyDeductedCopies: '消費部数',
        privateContract: '個人向け契約書',
        onlinePay: 'オンライン支払い',
        noRefundAfterPay: '（この製品/サービスは購入すると返品できません）',
        orderNum: '注文番号：',
        getMoreContract: 'より多くの契約を贈ることができます',
        discountCode: '割引コード',
        plsInputDiscountCode: '割引コードを入力してください',
        discountCodeErrorTip: '割引コードには数字しか含まれません！',
        discountTip: '支払い前にアカウントに割引コードがあるかどうかをチェックして使用できます,1つの注文には1回の割引コードしか使用できません。他の割引コードを使用するには、再発行注文に戻ってください',
        documentAmout: 'ファイル数量：{num}部',
        amoutTo: '金額：￥{money}',
        validUntil: '有効期間',
        document: 'ファイル',
        memberMumer: 'メンバー人数：',
        number: '数量：',
        aliPay: 'Alipay',
        wechatPay: 'WechatPay',
        bankTransfer: '銀行振込',
        hotline: 'ホットライン',
        customerService: 'カスタマーセンター',
        rechareSubject: 'リチャージ主体',
        rechareAccout: 'リチャージアカウント',
        invoiceTip: '（請求書本体であり、請求書の交換はサポートされていない）',
        person: '個人',
        ent: '企業',
        notAuth: '未実名',
        curRechareDialog: {
            subject: '現在のリチャージ主体',
            amount: 'リチャージ金額',
            tip: 'リチャージミスによる契約プロセスに影響がないように、現在のリチャージ主体を確認してください',
            notAuthTip: '現在のアカウントは実名認証が行われていません。契約書は実名認証後使用できますが、リチャージ契約は企業名義で使用する場合、実名認証を完了してからリチャージしてください。',
        },
        amout1: '金額：',
        yuan: '円',
        payValidTime: '支付时间：',
        within24h: '24小时内',
        payNow: 'すぐに支払い',
        selfTransferBankAccout: 'また、以下の銀行口座への自動振替も可能です',
        accountName: '口座名',
        account: 'アカウント',
        accountBank: '取引銀行',
        lineNumber: '銀行番号',
        rechareTip1: {
            title: '注意事項：',
            tip1: '注文番号を支払い情報の中に注記してください',
            tip2: '振込完了後、カスタマーサービスまで連絡ください',
        },
        tip: '注意',
        paySuccess: '支払い完了',
        myOrder: '注文履歴',
        useRecord: '使用記録',
        batchInvoicing: '領収書の一括発行',
        selectOrderTip: '{length}個の注文を選択済みです',
        orderMap: {
            num: '注文番号',
            setMeal: 'セット',
            amountAndType: '数量とタイプ',
            amountYuan: '金額（円）',
            amountYuanUae: '金額（$）',
            expireDate: '満期日',
            expireNum: '有効期限切れ数量',
            payStatus: '支払い状態',
            noPay: '未支払い',
            payDate: '支払い日時',
            invoiceStatus: '正規領収書状態',
            applyInvoice: '領収書発行申請',
            reApply: '再申請',
            infoError: '情報エラー',
            createDate: '生成日時',
            selectOneOrder: '1つ以上の注文を選択してください',
            noInvoiceOrder: '領収書の送信可能な注文がありません',
        },
        payStatusMap: {
            status1: '支払済',
            status2: '閉じる',
            status3: '抹消',
            status4: 'エラー',
            status5: '未支払い',
        },
        billStatusMap: {
            status1: '領収書の申請',
            status2: '申請済み',
            status3: '発行済',
            status4: '再申請',
        },
        supportBatchInvoiceTip: '下記の注文は一括領収書発行申請をサポートしています',
        batchApply: '一括申請',
        ssqDealTip: '（ベストサインが受け取った後7営業日以内に処理を完了させます）',
        submit: '提出',
        confirm: '確定',
        addNomalInvoice: '増値税一般領収書',
        addSpecialInvoice: '増値税専用領収書',
        entNomalInvoiceMap: {
            tip1: '増値税一般領収書は電子領収書形式にて電子メールで送信されます',
            entName: '会社名',
            taxNum: '納税者番号',
            amount: '領収金額',
            inbox: 'メールボックス',
            plsInputEntName: '会社名を入力してください',
            plsInputInbox: 'メールボックスを入力してください',
            plsInputEmail: '正しいメールアドレスを入力してください',
            plsInputTaxNum: '納税者番号を入力してください',
            name: '氏名',
            plsInputName: '氏名を入力してください',
        },
        entSpecialInvoiceMap: {
            mailTip: '国のデジタル電子請求書の全面的な普及に伴い、当プラットフォームは紙の請求書を使用しなくなりました。付加価値税専用請求書も電子メール形式で受け取るようになりました（デフォルトでは、前回の請求情報が移入されています。チェックしてください）。',
            // tip1: '注意：600円以下の領収書は料金着払いで郵送されます',
            // plsAgreeTip: '600円以下の領収書は料金着払いの郵送のみ対応しています。チェックしてください',
            entAddress: '会社住所',
            entTel: '会社電話番号',
            bankAccount: '取引口座番号',
            invoiceAmout: '領収金額',
            tip2: '一般納税者証明のアップロード（画像キャプチャもしくはファイル）',
            tip3: '受取人情報を記入すると、お客様に領収書を郵送します',
            // recipientName: '受取人氏名',
            // recipientPhone: '受取人電話番号',
            // recipientAddress: '受取人住所',
            plsInputEntAddress: '会社住所を入力してください',
            plsInputEntPhone: '会社電話番号を入力してください',
            plsInputBankName: '取引銀行を入力してください',
            plsInputBankAccount: '取引口座番号を入力してください',
            plsUploadCertificate: '一般納税者証明をアップロードしてください（画像キャプチャもしくはファイル）',
            // plsInputRecipientName: '受取人氏名を入力してください',
            // plsInputRecipientPhone: '受取人電話番号を入力してください',
            // plsInputRecipientAddress: '受取人住所を入力してください',
            picNotExceetTip: '画像は5Mを超えないでください',
            picNotMeetRequire: '画像フォーマットが要件に適合していません',
            clickUpload: 'ファイルをアップロード',
            certifyProvePic: '一般納税者証明書',
        },
        errorTip: 'エラーが発生しました',
        applyInvoiceSubmit: '領収書発行申請書を提出しています',
        getInvoiceTip: '情報確認でミスがなければ、7～15営業日で領収書が届きます。',
    },
    common: {
        confirmClose: '閉じますか？',
        day: '日',
        month: '月',
        groupAgentAuth: '（グループ代理認証）',
        plsSelectEnt: '先に企業を選択してください',
        checkVideoTip: 'ここでは操作を動画で解説しています。',
        selectBusLine: '業務ラインを選択してください',
        entChanged: 'ページを更新してください。',
        refreshPage: '更新',
    },
    commonHeader: {
        usercenter: 'ユーザーセンター',
        console: '企業管理コンソール',
        home: 'トップページ',
        sender: '発信者による管理',
        businessStaff: '業務担当者による管理',
        contractManagement: '契約書管理',
        templateManage: 'テンプレート管理',
        statisticCharts: '統計報告書',
        service: 'サービス',
        offlineContractManage: 'オフライン契約書管理',
        contractDrafting: '契約書起草',
        fileService: 'ファイルキャビネット',
        authenticating: '認証中の企業',
        unAuthenticate: '未認証の企業',
        rejectAuthenticate: '実名は却下されました',
        entAccount: '企業アカウント',
        personAccount: '個人アカウント',
        enterpriseConsole: '企業管理コンソール',
        createEnt: '企業を登録',
        viewDetail: '詳細確認をクリック',
        message: '情報',
        video: '操作動画',
        tip: '注意',
        ok: 'わかりました',
        entName: '企業名',
        createCompnayP: {
            hasUnAuthEnt: 'アカウントの下に認定を完了していない企業がまだある場合は、「認定を完了していない企業」の認定を完了するか、認定をキャンセルしてから新しい企業を追加してください。',
            toAuth: '認証',
            toCancel: '削除',
            p1: '新しい企業がすぐに作成されますので、企業名を入力してください',
            p2: '企業関連資料をすぐに提出し（実名認証を使用）、企業名を取得',
            p3: '認証は行わず、手動で入力',
            p4: '認証されていない企業名は自分のみ表示されます',
            loadingTxt: 'データを確認中です。少々お待ちください。',
            p5: '会社の本人確認にあたり、法人名称および法人番号を登記情報と完全に一致するようにご入力のうえ、印鑑証明書のキャプチャ画像をアップロードしてください。本人確認の審査には通常1～3営業日ほど時間かかります。',
            p6: '審査状況のご確認やその他のお問い合わせにつきましては、<EMAIL> までご連絡ください。',
            p7: '印鑑証明書',
            p8: 'まず印鑑証明書をアップロードしてください',
            p9: 'レビュー中。レビューの進行状況を理解する必要がある場合は、メールでお問い合わせください：<EMAIL>',
            areaRegister: '企業登記国',
            jp: '日本',
            cn: '中国本土',
            are: 'アラブ首長国連邦',
            other: 'その他',
            plsSelect: '選択してください',
            tip1: '登録先は中国大陸部の企業で、ent.bestsign.cnで実名登録を完了する必要があります。中国大陸部以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',
            tip2: '企業が中国大陸版に署名して実名認証を完了している場合は、ent.bestsign.cnに直接ログインし、関連サービスを簡単に利用できます。海外版に署名したデータは、中国大陸版とは完全に独立していることに注意してください。',
            tip3: '現地の商業規制当局から取得した証明書番号を提供してください',
            tip4: '証明書資料',
            tip5: '1. 御社の担当カスタマーマネージャーに連絡し、企業実名認証の完了までご案内させていただきます。',
            tip6: '2. 御社とベストサインとの契約書のキャプチャ、または、カスタマーマネージャーとやり取りしたメールのキャプチャをアップロードしてください。',
            tip7: '3. この方式は、日本および中国本土以外の企業のみ、ご利用いただけます。',
            tip8: '4. 資料提出後、ベストサインは3営業日以内に認証を完了します。',
            comNum: '企業証明書番号',
            plsEnterComNum: '企業証明書番号を入力してください',
            plsUploadBuyRecord: '証明書類をアップロードしてください',
            uaeTip1: '登録先はアラブ首長国連邦の企業で、uae.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦以外の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',
            uaeTip2: '企業がアラブ首長国連邦版に署名して実名認証を完了している場合は、uae.bestsign.comに直接ログインして、関連サービスを簡単に利用できます。注意する必要があるのは、海外版に署名したデータは、アラブ首長国連邦版とは完全に独立しています。',
            uaeTip3: '登録先はアラブ首長国連邦と中国大陸以外の企業で、ent.bestsign.comで実名登録を完了する必要があります。アラブ首長国連邦の企業と契約を締結する際には、「国境を越えた署名」機能を利用して、ユーザーデータの安全性を確保し、流出しないことを前提に、契約の相互署名を効率的に完了することができる。',
        },
        enterEnterprise: '企業名を入力してください',
        cancel: 'キャンセル',
        chooseEnterprise: '企業の選択',
        noResult: '結果はありません',
        confirm: '確定',
        plzEnterRightEnt: '正しい企業名を入力してください',
        plzEnterRightCorporate: '正しい法人番号を入力してください',
        selectArea: '企業登録先を選択してください',
        corporateNumber: '法人番号',
        corporateNumberTip: '法人番号を入力してください',
        createSuccess: '作成の完了',
        viewAllEnt: '全企業の確認',
        addEnt: '企業を登録',
        exit: '終了',
        userCenter: 'ユーザーセンター',
        groupConsole: 'グループ管理コンソール',
        backToHome: 'トップページに戻る',
        connectToPrivateNetTip: 'お客様の企業は契約書を個人契約のストレージを使用し、現在のネットワークで契約ストレージのサーバーに接続しています',
        noConnectToPrivateNetTip: 'お客様の企業は契約書を個人契約のストレージを使用しています。現在のネットワークでは契約ストレージのサーバーに接続することができません',
        advice1: 'アドバイス：',
        checkNetTip1: '①　現在のネットワークに企業内ネットワークにアクセスできるかどうか検査する',
        checkNetTip2: '②　契約ストレージサーバーが正常に運用しているかどうか検査する',
        ecology: '{developerName}の企業アカウントから',
        ecologyPerson: '{developerName}の個人アカウントから',
    },
    commonFooter: {
        record: 'ICP主体企業登録番号：浙ICP備14031930号',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: 'オープンプラットフォーム',
        aboutBestSign: '弊社について',
        contact: 'お問い合わせ先',
        recruitment: '採用情報',
        help: 'ヘルプセンター',
        copyright: '無断転載禁止',
        company: 'ベストサイン・ジャパン株式会社',
        ssqLogo: 'ベストサイン ボトムバーロゴ',
        provideTip: '電子契約サービスは',
        ssq: 'ベストサインにより',
        provide: '提供',
        signHotline: '契約サービスホットライン',
        langSwitch: '言語',
    },
    footerSimpler: {
        ssqDes: '電子契約クラウドプラットフォームのリーディングカンパニー',
    },
    mainChart: {
        sendVolume: '発信量',
        signFinishAmout: '署名完了量',
        contractSendAmout: '契約書発送量',
        contractSignAmout: '契約書署名量',
        billion: '億',
        wan: '万',
        fen: '部',
        ci: '回',
    },
    resetPassword: {
        safeTip: '安全上の注意',
        weakPswTip: 'システムでお客様のパスワードの安全係数が低いことを検知しました。セキュリティリスクが存在しますので、パスワードを再設定してください。',
        oldPsw: '元のパスワード',
        plsInputOldPsw: '元のパスワードを入力してください',
        newPsw: '新規パスワード',
        pswRule: '数字・大文字/小文字からなる6～18桁で、常用の特殊文字をサポートしています',
        confirm: '確定',
        oldPswNotEmpty: '元のパスワードは空欄に出来ません。',
        newPswNotEmpty: '新規パスワードは空欄に出来ません。',
        modifySuccess: '修正に成功しました',
    },
    passwordExpiration: {
        notice: '您的密码已经90天未更换了，根据企业要求，请及时更换。',
        btn: '去更换',
    },
    giftContract: {
        expiryDateUtil: '使用期限は',
        buyTip: '電子契約セットを購入いただく場合、デフォルトで購入するのは個人アカウントのため、実名認証を完了させて購入してください。',
        personAuthTip: 'ありがとうございます。個人実名認証が完了しました。',
        entAuthTip: 'ありがとうございます。企業実名認証が完了しました。',
        openSsqTip: 'アカウント登録を完了されたことをお祝いします。',
        registerTip1: '実名認証を完了させ、無料の契約書部数を取得します。',
        registerTip2: '部の無料契約書サービスを提供します',
        registerTip3: '実名認証を行った企業には',
        giftTip1: 'すでに',
        giftTip2: '部の個人向け契約書',
        and: 'および',
        giftTip3: '部の企業用向け契約書を提供します',
        sendContractTip: '初回の契約書送信を完了すると、更に無料の契約書部数を取得できます。',
        entAuth: '企業認証に進む',
        personAuth: '個人認証に進む',
    },
    noticeMap: {
        notice: '情報',
        notRead: '通の未読',
        allRead: '全既読済み',
        viewMore: '詳細を確認',
    },
    rechargeOrder: {
        searchType: '検索タイプ',
        deductTime: '引落時間',
        selectDateRange: '選択時間範囲',
        till: 'は',
        startDate: '開始日から',
        endDate: '終了日まで',
        search: '検索',
        sendTime: '発信日時',
        contractTitle: '契約書名',
        contractId: '契約書番号',
        user: '使用者',
        belongDepartment: '所属部門',
        contractType: '契約タイプ',
        consumedCopies: '消費部数',
        consumedCopiesTips: '今回の契約業務で消費する契約件数.',
        personConsumedCopiesTips1: '1、消費記録は実際に使用された契約パッケージのカテゴリに準じる。例えば、1件の対私契約が送信されたが、その時点で利用可能な対私契約パッケージがなく、対公契約パッケージから差し引かれると、消費履歴は1件の対公契約として表示される。',
        personConsumedCopiesTips2: '2、2025年4月21日から、一般の個人ユーザーは契約の期限切れ、撤回、または契約部数の返還を拒否する優遇を受けなくなった。',
        exportMaxTip: 'データのエクスポートは10,000通を超えないでください',
        export: 'エクスポート',
        exportTip: 'エクスポートに時間がかかります。現在のページを閉じないでください。 別のウィンドウを開いて他のビジネスを処理できます',
        batchCenter: '一括タスクセンター',
        batchExportTip: 'バックグラウンド解析では、一括タスクセンターでこの操作の結果を表示してください.',
    },
    rechargePackage: {
        manage: 'チャージ管理',
        selectPackage: 'セットの選択',
        tip1: 'リチャージ金額が40,000円以上になった場合、カスタマーサービスまでお問い合わせください：',
        onlineService: 'オンラインカスタマーサービス：',
        tip2: '優待期間は12月31日まで',
        ccbPublicContract: '建設銀行の企業向け契約書',
        publicContract: '企業向け契約書',
        copiesTip: '部数：{num}部',
        vailidityPeriodTip: '有効期限：{month}ヶ月',
        ccbPrivateContract: '建設銀行の個人向け契約書',
        privateContract: '個人向け契約書',
        copiesTip1: '部数',
        giveCopiesTip: '+{num}部',
        buyNow: '購入する',
        advancedFeature: 'Advanced features purchase',
        learnDetail: 'Learn more',
        isUsing: 'Opened',
        unUsing: 'Not opened',
        payAmount: 'Payment amount',
        placeOrder: 'Buy Now',
        renewal: '续费购买',
        year: '年',
        limitBugTip: '特恵1回限りの購入',
    },
    editFieldDialog: {
        chooseFieldType: 'フィールドタイプの選択：',
        fieldType: {
            text: 'テキスト',
            single: '単一選択',
            multi: '複数選択',
            date: '日付',
            combo: '検索候補',
            number: 'デジタル',
            datetime: '時刻',
        },
        name: '名称',
        namePlace: '名称を入力してください',
        options: '任意',
        addOptions: '任意の追加',
        contentFiller: '内容記入者',
        sender: '発信者',
        dateFormate: 'パターン',
        senderContent: '契約内容が発信する前に発信者が記入するため、「発信者」を選択します',
        signer: '署名者',
        signerContent: '署名者がこの契約内容で署名するときに記入するため、「署名者」を選択します',
        fontSize: 'フォントサイズ',
        fontSizePlace: 'フォントサイズを選択してください',
        requirements: '要件の記入',
        required: '必須項目',
        confirm: '確定',
        cancel: 'キャンセル',
        addField: 'フィールドの追加',
        editField: 'フィールドの編集',
        integer: '整数',
        decimalLimit: '制限',
        decimal: '小数点X桁',
        errorMsg: {
            optionEmpty: '任意名は空欄に出来ません',
            optionHasEnComma: '選択肢の名称に英語のカンマを入力することはできません',
            optionNameFormat: '任意名は中国語/英語/数字の組み合わせのみです',
            optionNameRepeat: '任意名は重複できません',
            enterFieldName: 'フィールド名を入力してください',
            fieldNameExist: 'このフィールド名は存在しています。再度名前をつけてください',
            fieldNameExistToOn: 'このフィールド名は存在しています。重複した名前のフィールドを有効にしてください',
            optionsNeed: '少なくとも任意を2つ追加してください',
            overCountLimit: '500件までの任意に対応しています',
        },
        editSuccess: '編集成功',
        addSuccess: '新規追加完了',
        labelAlign: 'アライメント方法',
    },
    functionSupportDialog: {
        title: '機能紹介',
        inputTip: '関連する使用条件がある場合は、以下のフォームにご記入ください。ベストサインでは24時間以内に専門スタップからご連絡し、サービスのご案内を差し上げます。',
        useSence: '使用場面',
        useSenceTip: '例：人事/販売業者/物流帳票...',
        estimatedOnlineTime: '予定オンライン時間',
        requireContent: '必要内容',
        requireContentTip: '御社がどのように電子契約を使用するのか大まかに説明ください。弊社からお客様のために適切なプランを作成します。',
        getSupport: '専門サービスサポートの提供',
        callServiceHotline: 'すぐにカスタマーサポート：<EMAIL>',
        useSenceNotEmpty: '使用場面は空欄に出来ません',
        requrieContentNotEmpty: '必要内容は空欄にできません',
        oneWeek: '一週間以内',
        oneMonth: '一ヶ月以内',
        other: 'その他',
        submitSuccess: 'コミット成功',
        submitTrial: '試用に提出する',
        toTrial: '試用に行く',
        trialTip: '試用申請を提出すると、現在の機能はすぐに開通し、試用することができる。機能の使用を支援するために、次の表により多くのニーズを記入することができます。電子契約コンサルタントに署名すると、サービスが提供されます。',
        applyTrial: '試用申請',
        trialSuccTip: '機能が開通しましたので、お試しください',
        goBuy: '直接購入',
        buyLater: '後で買います',
        trialTipMap: {
            title: '試用の心得',
            tip1: '1. オープン即使用、有効期間は7日間；',
            tip2: '2. 試用期間中、機能は無料；',
            tip3: '3. ビジネス主体ごとに、1つの機能を1回だけ試用する機会；',
            tip4: '4. 試用期間中は自由に購入でき、無停止で使用できる；',
            tip5: '5. 試用が終了した場合は、スキャンコードを確認して、詳細については前に署名した専門コンサルタントに連絡してください：',
        },
        contactAdminTip: '使用する場合は、Enterprise Administrator {tip} にお問い合わせください。',
        trialEndTip: '試用期間が終了したら、クリックして購入してください',
        trialRemainDayTip: '試用期間が残りました{day}日、クリックして購入してください',
        trialEnd: '試用終了/機能期限',
        trialEndMap: {
            deactivateTip: '{feature}機能は無効になっています。構成をクリアするか、継続料金を払ってから使用できます。',
            feature1: '契約付属資料',
            remove1: '構成の消去方法は、「テンプレートの編集」-構成済みの追加契約付属資料を見つけて削除します。',
            feature2: '手書きの筆跡認識',
            remove2: '構成をクリアする方法は、「テンプレートを編集」-構成済みのストローク認識を見つけて削除します。',
            feature3: '契約装飾：スリット章+透かし',
            remove3: '構成の消去方法は、「テンプレートの編集」-構成された契約装飾を見つけて削除します。',
            feature4: '契約送信承認',
            remove4: '構成方法の消去：Company Console-すべての承認フローを非アクティブにする',
            feature5: 'テンプレート認可機能は期限切れで無効になり、テンプレート認可スキームはすべて無効になり、管理者のみが使用権限を持っています。テンプレート使用を再作成するか、管理者に継続料金を通知してから使用することができます。',
            remove5: '',
        },
    },
    RechargeAccount: {
        orders: {
            pay: 'お支払い',
            money: '金額',
            monyUnit: '円',
            jaMonyUnit: '円',
            main: 'トップアップ会社',
            chargeNow: 'すぐチャージ',
            payApproachType: 'デビットカードまたはクレジットカード',
            poweredBy: 'テクニカルサポートプロバイダー',
            // orderId: '注文番号',
            // contractNum: '部数',
            // actualMoney: '实际付款金额',
            // date: '有効期限',
            numberUnit: '部',
            // goPay: 'すぐ払い',
            // account: '充值账号',
            // unpaied: '支払待ち',
            // paied: '支払済み',
            // limitedTime: '期間限定無料',
            // payResultDialogTitle: '補充通知',
            // payResultSuccess: '補充が成功しました。',
            // useNow: '今すぐ使います',
            // paymentTime: 'お支払日時',
            // dueDate: '有効期限',
            // moneyWithUnit: '金額（円）',
            // paymentStatus: 'お支払状況',
        },
    },
};
// 基础组件翻译
const components = {
    helperFloat: {
        advice: '問い合わせ/アドバイス',
        hide: '隠す',
        suggest: 'アドバイスの申し出',
        onlineService: 'オンラインカスタマーサービス',
        minimize: '最小化',
        person: '個人',
        ent: '企業',
        checkMoreVersion: 'その他のバージョンを確認',
        submitSuccess: '上报成功',
        reportLog: '上报日志',
    },
    countDown: {
        resendCode: '再取得',
        getVerifyCode: '認証コードを取得',
    },
    messageBox: {
        confirm: '確定',
    },
    example: {
        sideDisplay: '側面展示',
        clickPreview: 'プレビューをクリック',
        previewTitle: '表題のプレビュー',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: '印章の提示',
            tip: '現在の印章画像がお客様の企業身分と適合していないことが検出されました。現在の印章画像識別の結果は：',
            tip2: 'このまま現在の印章画像を使用しますか？',
            guide: '正しい印章をアップロードするには >>',
            next: '続けて使用',
            tip3: 'また、あなたの印鑑名は規範に合わず、「{keyWord}」という文字が付いています。',
            tip4: '印鑑名が規範に合わないことが検出されました。「{keyWord}」という文字が付いていますが、引き続き使用しますか？',
        },
        exampleSeal: {
            title: '印章図案のアップロード方法',
            way1: ['方法1：', '1、白い紙の上で実物の印章図案を捺印します。', '2、撮影し、画像をプラットフォームにアップロードします'],
            way2: ['方法2：', '図のように直接プラットフォームの電子印章機能で生成します：'],
            errorWay: ['間違った方法：', '印章を手で持つ', '関係ない画像', '営業許可証'],
        },
        confirm: '確認',
        cancel: 'キャンセル',
    },
};

// 公共ライブラリー内でその他翻訳する内容
const commonother = {
    validationMsg: { // 検証関連の注意
        notEmpty: 'このフィールドを入力してください。',
        enter6to18n: '数字・大文字/小文字からなる6～18桁を入力してください',
        enter8to18n: '数字・大文字/小文字からなる8～18桁を入力してください',
        errEmailOrTel: '正しいメールアドレスを入力してください',
        verCodeFormatErr: '認証コードエラーです',
        signPwdType: '6桁の数字を入力してください。',

        enterActualEntName: '実企業名を入力してください',
        enterCorrectName: '正しい氏名を入力してください',
        enterCorrectPhoneNum: '正しい携帯番号を入力してください',
        enterCorrectEmail: '正しいメールアドレスを入力してください',
        imgCodeErr: '画像認証コードが正しくありません',
        enterCorrectIdNum: '正しい証明書番号を入力してください',

    },
    pubTips: {
        loginOverdue: 'ログインが失効しています。改めてログインしてください',
        errorTip: 'エラーアラーム',
        know: 'わかりました',
        serverError: 'サーバーエラーが発生しています。しばらくしてから試してください',
    },
    commonNomal: {
        yesterday: '昨日',
        ssq: 'ベストサイン',
        ssqPlatform: 'ベストサイン電子契約クラウドプラットフォーム',
        ssqTestPlatform: '（テスト用限定）ベストサイン電子署名クラウドプラットフォーム',
        pageExpiredTip: 'ページは有効期限切れです。ページをリロードしてください',
        pswCodeSimpleTip: 'パスワードは数字・大文字/小文字を含む6～18桁にする必要があります。再設定してください',
    },
    codeHandlerMap: {
        tip1: 'このテンプレートの使用権限はテンプレート作成者によって取り消されました',
        tip2: 'このテンプレートは作成者によって削除されました',
        tip3: 'このテンプレートは作成者によって無効化されたため、引き続き使用することはできません',
        tip4: 'このテンプレートは使用できません',
        tip5: 'テンプレートリストに戻る',
        errorTip: 'サーバーエラーが発生しています。しばらくしてから試してください',
        getNewJar: 'ベストサインに連絡してjarパッケージをアップグレードしてください',
        networkNotConnect: 'ネットワークエラーです。ネットワーク設定を確認してください',
        notAccessPage: 'このページにアクセスできません',
        page404: 'アクセスしたページが見つかりません（404）',
        goHome: 'トップページに戻る',
        downloadApp: 'ベストサインアプリをダウンロード',
        advancedFeatureError: 'この機能はプレミアム バージョンのみ利用できます。ベストサインに連絡して、アップグレードしてください。',
    },
    catchMap: {
        view: '確認',
        download: 'ダウンロード',
        reject: '拒否',
        revoke: '抹消',
        delete: '削除',
        cantOperate: '{operate}契約できません',
        hybridNetHeader: '発信側の企業は契約書を個人契約のストレージを使用しています。ただし、現在のネットワークでは発信者の契約ストレージのサーバーに接続することができません。',
        hybridNetMsg: 'アドバイス：ネットワークが正常かどうか検査してください',
        checkNet: 'ネットワークが正常かどうか検査してください',
        hybridNotConnect: '原因：お客様の企業は契約書を個人契約のストレージを使用しています。現在のネットワークでは契約ストレージのサーバーに接続することができません。',
        hybridSuggest: 'アドバイス：（1）ネットワークが正常かどうか検査してください。（2）契約ストレージサーバーが正常に運用しているかどうか検査してください。',
        goHomePage: 'トップページに戻る',
    },
    // 日文版签署前实名
    authBeforeSignJa: {
        plsConfirmIdentity: 'ご本人確認をお願いします',
    },
    // https://blog.csdn.net/QAQ_King/article/details/128776411 对照表
    fontSizeMap: {
        one: '26',
        smallOne: '24',
        two: '22',
        smallTwo: '18',
        three: '16',
        smallThree: '15',
        four: '14',
        smallFour: '12',
        five: '10.5',
        smallFive: '9',
    },
    replaceEmpty: '入力された企業名/名前にスペースが含まれていることが検出され、実際の企業名/氏名と一致せず署名に影響を与える可能性がありますので、スペースを削除しました。',
};
export default {
    ...businessComponents,
    ...components,
    ...commonother,
    lang: 'JP',
};
