// 业务组件翻译
const businessComponents = {
    userHeader: {
        hi: '你好',
        exit: '退出',
        help: '帮助',
        hotline: '服务热线',
    },
    ssoLoginConfig: {
        notBelongToEntTip: '需要重新登录上上签平台才能发送合同（或管理模板）',
        operationStep: {
            one: '第一步 点击继续后，返回登录页面',
            two: '第二步 输入密码，进入上上签平台',
            three: '第三步 发送合同（或管理模板）',
        },
        continue: '继续',
        cancel: '取消',
        tip: '提示',
    },
    TagManage: {
        labelOperate: {
            editLabel: '编辑标签名称',
            setRule: '设置贴标签规则',
            dropRule: '设置撕标签规则',
            setPutRemind: '设置贴标签提醒',
            afterDays: '天后',
            remindPeople: '提醒责任人',
            edit: '编辑',
            labelNameLengthLimit: '标签名称最长为十五个中文字符',
            labelNameRequire: '请输入标签名称',
            setLblRuleTip: '设置“{name}”标签规则',
            remind: '提醒',
            done: '完成',
        },
        putLabelRules: {
            name: '贴标签',
            tip: '*当事件触发时，合同会自动贴上该标签。所以请先选择触发事件：',
            manual: '手动贴标签',
            afterSent: '合同发送后',
            inputDaysTip: '请输入天数',
            beforeSignDeadline: '合同截止签署前',
            afterDone: '合同完成后',
            beforeDue: '合同到期前',
            afterDue: '合同到期后',
            afterReject: '合同被拒签',
            day: '天',
            minSet: '至少设置',
            maxSet: '最多设置',
        },
        dropLabelRules: {
            name: '撕标签',
            tip: '当事件触发时，会自动撕掉合同标签。所以请选择触发事件：',
            manual: '手动撕标签',
            afterDone: '合同完成后',
            afterDue: '合同到期后',
        },
        remindRule: {
            tip: '设置提醒时间以便向相关责任人发送通知：',
            remindDays: '提醒时间(如粘贴标签3天后发送通知)',
            remindDayTip: '请输入天数',
            remindPerson: '提醒人员',
            remindPersonTip: '请输入邮箱地址',
            addRemindPerson: '新增通知邮箱',
            remindEmailTip: '请输入正确的邮箱地址',
        },
    },
    batchAddOptionsDialog: {
        batchAddOption: '批量添加选项',
        batchAddTip1: '默认以行为单位，一行一项，请依次填写。',
        batchAddTip2: '最多支持500条，建议300条以内，名字长度100字以内。',
        batchAddPlaceHolder: '请输入内容',
        searchContentLengthLimitTip: '最多支持500个备选项，超出不做处理',
        searchContentPlaceholder: '请输入搜索词',
        confirm: '确认',
        cancel: '取消',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: '已被实名',
        assignedIdentity: '发件方填写的签约主体为：',
        entBeenAuthenticated: '该企业已被实名，主管理员信息如下：',
        entAdminName: '管理员姓名：',
        entAdminAccount: '管理员账号：',
        applyToBeAdmin: '我要申诉成为主管理员',
        contactToJoin: '联系管理员加入企业',
        applicant: '申请人',
        inputYourName: '请输入您的姓名',
        account: '账号',
        send: '发送',
        sendWishToJoin: '您未加入该企业，将无法以企业身份进行后续业务，可点击下方按钮成为企业成员后再操作。',
        applyToJoin: '您还未加入该企业，无法签署该合同，是否要申请加入？',
        sentSuccessful: '发送成功',
    },
    autoSeal: {
        add: '添加',
        tip: '提示',
        confirm: '确认',
        cancel: '取消',
        save: '保存',
        preStep: '上一步',
        nextStep: '下一步',
        done: '完成',
        manageConfig: '管理员配置',
        codeVerify: '填写验证码',
        signerConfig: '签署人配置',
        step1Tip: '第一步：请将“{name}”分配给需要使用自动盖章功能的企业成员。',
        step2Tip: '第二步：请各位企业成员进入',
        step2Tip1: '用户中心-我的印章',
        step2Tip2: '开启“自动盖章”开关。此后，他收到的合同中只要符合自动签条件，系统将自动盖章。',
        openAutoTip: '已启用“{name}”自动盖章功能的名单如下：',
        howToOpenTip: '企业成员如何配置自动签',
        signPsw: '签约密码',
        forgetPsw: '忘记密码',
        msgTip: '一直收不到短信？试试',
        verificationCode: '验证码',
        voiceVerCode: '语音验证码',
        SMSVerCode: '短信验证码',
        or: '或',
        emailVerCode: '邮箱验证码',
        SentSuccessfully: '发送成功！',
        mail: '邮箱',
        phone: '手机号',
        inputPwd: '请输入签约密码',
        verCodeInputErr: '请先获取验证码！',
        verCodeFail: '验证码错误',
        inputNumber6: '请输入6位数字',
        inputPwd6: '请输入6位签约密码',
        sendInternalErr: '发送时间间隔过短',
        openAutoSeal: '启用自动盖章',
        closeAutoSeal: '关闭自动盖章',
        autoSealDialogTip: {
            1: '自动签署功能已生效，新收到的“需要我签署的”都将很快完成自动签署。',
            2: '现在需要你确认：之前收到的合同是否也需要系统自动签署？',
            3: '如果需要，勾选“是”并点击“完成”，系统会自动用该印章去签署所有符合条件的合同。不过，这些合同可能需要等待较长时间，请耐心等待。',
            4: '如果不需要，选择“否”并点击“完成”即可。',
        },
        iKnow: '完成',
        iSee: '我知道了',
        yes: '是',
        no: '否',
        closeAutoSealTip: '关闭后，印章持有角色/持有人无法继续使用该印章自动签署',
        autoSignTipTitle: '允许企业成员使用此印章自动签，触发条件需要同时满足：',
        autoPersonSignTipTitle: '允许个人使用此签名章自动签，触发条件需要同时满足：',

        defaultText: '*默认：',
        account: '账号：',
        enterprise: '*企业：',
        contractContent: '合同内容：',
        senderEnable: '发件方允许合同被自动签',
        selfEntEnable: '本企业发送的合同',
        senderEntEnable: '指定发件方企业',
        senderEnt: '发件方企业',
        receiveEnt: '接收方企业',
        inputEntNamePlaceholder: '请输入企业名称',
        includeGroupMemeber: '包含本集团子公司发送的合同',
        senderAccountEnable: '指定发件方账号',
        inputAccountPlaceholder: '请输入账号',
        phoneOrEamilTip: '请输入正确的手机号或邮箱',
        unpointAccountTips: '未指定则不对账号做校验',
        contractIdEnable: '指定合同ID，锁定类似的合同',
        inputContractIdPlaceholder: '请输入合同Id',
        inputContractIdTips: '请输入正确的合同Id',
        templateIdEnable: '指定合同模板ID（需向发件方索取）',
        inputTemplateIdPlaceholder: '请输入模板Id',
        inputTemplateIdTips: '请输入正确的合同模板Id',
        unpointContractContentTips: '未指定则不对合同内容做校验',
        pointSenderEnt: '请指定发件方企业',
        versionTip: '您需求开启更高版本才能勾选“本企业发送的合同”。',
        pointEntName: '请指定具体发件方企业名称',
        pointReceiveEnt: '请指定接收方企业',
        pointReceiveEntName: '请选择接收方企业名称',
        ponitSenderAccount: '请指定具体发件方账号',
        pointContractContent: '请指定具体合同内容',
        notClickAddIconTip: '请先点击“+添加”按钮，然后再重新点击“保存”按钮',
        maxAddLimit: '最多添加10条数据',

        specialConfirmTip: '特别提示：当您按照自动签功能页面提示填写信息、勾选相应功能并点击保存后，即表示您已充分阅读、理解并接受该功能的已勾选选项的全部内容。',
        warningTip: {
            title: '*上述操作的过程中，如果您不同意任何相关内容或条款的约定，您应立即停止操作行为：',
            tip1: '（1）自动签署的合同内容是不确定的。虽然您可以指定模板ID限制发件方必须使用某一个合同模板，但是合同模板上的部分字段的数值是不固定的，而且合同模板有可能会被修改。',
            tip2: '（2）自动签署后的合同你是不可以撤回盖章或签名的。您收到的合同后满足您的设置条件后，立即签署，如果您事后在“合同管理”中阅读合同与您的预期不一致，需联系发件方撤销合同，或签署作废申明。',
        },
        suggestionsTip: {
            title: '*如果您对上述风险有疑虑，建议您：',
            tip1: '（1）不启用自动盖章功能。',
            tip2: '（2）或与发件方企业明确合同的发件人账号、合同内容，并在充分信任的基础上才能启用自动盖章功能。',
        },
        hasReadAndAccept: '我已阅读并接受以上提示',
        acceptTipFirst: '请先接受以上提示',
        enableTipText: '开启后，签署时上上签将调用您的数字证书，也为您提供到期自动续签服务。',
    },
    certificationOutOfCourt: {
        dataRejected: '材料被驳回',
        your: '您的',
        authRejectReason: '驳回原因',
        yourAuthInfo: '您的认证信息： | 真实姓名： | 证件号码：',
        baseInfo: '基本信息',
        handheldIdNum: '手持身份证认证',
        faceAuth: '刷脸认证',
        noMainlandAuth: '非大陆认证',
        authNow: '立即认证',
        underReview: '审核中',
        bankCardAuth: '银行卡认证',
        phoneAuth: '手机号认证',
        noMainlandPersonAuth: '非大陆人士认证',

    },
    certificationPermissionNoticeStatic: {
        authPermissions: {
            0: '通过实名认证，您将获得如下权限：',
            1: '发起合同',
            2: '开启全新电子签约新模式',
            3: '提升账号安全性',
            4: '享受更多会员保障服务',
            5: '线上合同管理功能',
            6: '提升企业效率',
            7: '获得电子合同份数',
            8: '满足您对电子签约的需求',
        },
    },
    companyDeptTree: {
        searchTip: '支持输入账号/姓名搜索',
        searchDeptTip: '支持输入企业/部门搜索',
        loadMore: '加载更多...',
    },
    companyFull: {
        addMember: '添加成员',
        chooseMember: '选择成员',
        selectAll: '全选',
        choosedMember: '已选成员',
        confirm: '确定',
        noneMemberChoosedTip: '请先选择成员',
    },
    recharge: {
        balance: '账户余额',
        noLimitContract: '不限量合同',
        unLimitUseTip: '有效期内不限量使用',
        expireDate: '到期时间',
        recharge: '充值',
        aiRecharge: '合同风控AI顾问',
        ai: '合同风控AI顾问',
        riskJudge: '风险判断',
        publicContract: '对公合同',
        tip1: '签署人（不包含发件人）中有企业账户的文档',
        tip2: '签署人（不包含发件人）中没有企业账户的文档',
        useCopies: '可用份数',
        withHoldCopies: '预扣份数',
        withHoldCopiesTips: '您本次合同业务发出后，系统预扣除的合同份数，若业务未完结则会返还。',
        actuallDeductedCopies: '实扣份数',
        actuallDeductedCopiesTips: '您本次合同业务完结后，系统实际扣除的合同份数。',
        alreadyDeductedCopies: '消费份数',
        privateContract: '对私合同',
        onlinePay: '在线支付',
        noRefundAfterPay: '（此产品/服务一经购买，无法退换）',
        orderNum: '订单号：',
        getMoreContract: '可获赠更多合同',
        discountCode: '折扣码',
        plsInputDiscountCode: '请输入折扣码',
        discountCodeErrorTip: '折扣码只能包含数字！',
        discountTip: '付款前可检查账户是否有折扣码可以使用，单个订单只能使用一次折扣码，如需使用其他折扣码请返回重新提交订单',
        documentAmout: '文档数量：{num}份',
        amoutTo: '金额：￥{money}',
        validUntil: '有效期至',
        document: '文档',
        memberMumer: '成员数量：',
        number: '数量：',
        aliPay: '支付宝',
        wechatPay: '微信支付',
        bankTransfer: '银行转账',
        hotline: '热线',
        customerService: '客服',
        rechareSubject: '充值主体',
        rechareAccout: '充值账号',
        invoiceTip: '（即开票主体，不支持更换主体开票）',
        person: '个人',
        ent: '企业',
        notAuth: '未实名',
        curRechareDialog: {
            subject: '当前充值主体',
            amount: '充值金额',
            tip: '请确认当前充值主体，以免充错影响签约流程',
            notAuthTip: '当前账户未进行实名认证，合同需在实名认证后使用，如充值合同需以企业的身份使用，请完成实名认证后再充值。',
        },
        amout1: '金额：',
        yuan: '元',
        payValidTime: '支付时间：',
        within24h: '24小时内',
        payNow: '立即支付',
        selfTransferBankAccout: '您也可以自助转账到以下银行账号',
        accountName: '户名',
        account: '账号',
        accountBank: '开户行',
        lineNumber: '行号',
        rechareTip1: {
            title: '温馨提示：',
            tip1: '请将订单号备注在付款信息里',
            tip2: '转账成功后，请联系客服提供订单号和转账凭证',
        },
        tip: '提示',
        paySuccess: '支付成功',
        myOrder: '我的订单',
        useRecord: '使用记录',
        batchInvoicing: '批量开票',
        selectOrderTip: '已选中{length}个订单',
        orderMap: {
            num: '订单号',
            setMeal: '套餐',
            amountAndType: '数量和类别',
            amountYuan: '金额（元）',
            amountYuanUae: '金额（$）',
            expireDate: '到期日',
            expireNum: '过期数量',
            payStatus: '付款状态',
            noPay: '未支付',
            payDate: '付款时间',
            invoiceStatus: '发票状态',
            applyInvoice: '申请开票',
            reApply: '重新申请',
            infoError: '信息有误',
            createDate: '创建时间',
            selectOneOrder: '请选择一个以上订单',
            noInvoiceOrder: '没有可开票的订单',
        },
        payStatusMap: {
            status1: '已支付',
            status2: '已关闭',
            status3: '注销',
            status4: '错误',
            status5: '未支付',
        },
        billStatusMap: {
            status1: '申请开票',
            status2: '已申请',
            status3: '已开票',
            status4: '重新申请',
        },
        supportBatchInvoiceTip: '以下订单支持批量申请开票',
        batchApply: '批量申请',
        ssqDealTip: '（上上签收到后会在7个工作日内处理完成）',
        submit: '提交',
        confirm: '确定',
        addNomalInvoice: '增值税普通发票',
        addSpecialInvoice: '增值税专用发票',
        entNomalInvoiceMap: {
            tip1: '增值税普通发票是以电子发票形式发送至邮箱',
            entName: '公司名称',
            taxNum: '税号',
            amount: '开票金额',
            inbox: '接收邮箱',
            plsInputEntName: '请输入公司名称',
            plsInputInbox: '请输入接收邮箱',
            plsInputEmail: '请输入正确的邮箱地址',
            plsInputTaxNum: '请输入税号',
            name: '姓名',
            plsInputName: '请输入姓名',
        },
        entSpecialInvoiceMap: {
            mailTip: '由于国家全面推广数字化电子发票，平台不再使用纸质发票，增值税专用发票也使用电子邮箱形式接收（默认填充上一次开票信息，请注意检查）。',
            // tip1: '温馨提示: 600元以下发票，以到付形式邮寄',
            // plsAgreeTip: '600元以下发票，仅支持到付邮寄，请勾选',
            entAddress: '公司地址',
            entTel: '公司电话',
            bankAccount: '开户账号',
            invoiceAmout: '开票金额',
            tip2: '上传一般纳税人证明（jpg或png格式图片）',
            tip3: '填写收件信息，向您邮寄发票',
            // recipientName: '收件人姓名',
            // recipientPhone: '收件人电话',
            // recipientAddress: '收件人地址',
            plsInputEntAddress: '请输入公司地址',
            plsInputEntPhone: '请输入公司电话',
            plsInputBankName: '请输入开户行',
            plsInputBankAccount: '请输入开户账号',
            plsUploadCertificate: '请上传一般纳税人证明（jpg或png格式图片）',
            // plsInputRecipientName: '请输入收件人姓名',
            // plsInputRecipientPhone: '请输入收件人电话',
            // plsInputRecipientAddress: '请输入收件人地址',
            picNotExceetTip: '图片大小不能超过5M',
            picNotMeetRequire: '图片格式不符合要求',
            clickUpload: '点击上传',
            certifyProvePic: '一般纳税人证明',
        },
        errorTip: '出错啦',
        applyInvoiceSubmit: '您的开票申请已提交！',
        getInvoiceTip: '信息审核无误后，7-15个工作日您将收到发票！',
    },
    common: {
        close: '关闭',
        confirmClose: '确认关闭？',
        time: '时间',
        day: '日',
        month: '月',
        groupAgentAuth: '（集团代认证）',
        plsSelectEnt: '请先选择企业',
        checkVideoTip: '这里可以查看操作视频指导！',
        selectBusLine: '请选择业务线',
        entChanged: '请刷新页面。',
        refreshPage: '刷新',
    },
    commonHeader: {
        usercenter: '用户中心',
        console: '企业控制台',
        home: '首页',
        sender: '按发件方管理',
        businessStaff: '按业务对接人管理',
        contractManagement: '合同管理',
        templateManage: '模板管理',
        statisticCharts: '统计报表',
        service: '服务',
        offlineContractManage: '线下合同管理',
        contractDrafting: '合同起草',
        fileService: '档案+',
        authenticating: '认证中企业',
        unAuthenticate: '未认证企业',
        rejectAuthenticate: '实名已驳回',
        entAccount: '企业账号',
        personAccount: '个人账号',
        enterpriseConsole: '企业控制台',
        createEnt: '创建企业',
        viewDetail: '点击查看详情',
        message: '消息',
        video: '操作视频',
        tip: '提示',
        ok: '知道了',
        entName: '企业名称',
        createCompnayP: {
            hasUnAuthEnt: '当前账号下仍有未完成认证的企业，请您先完成“未完成认证的企业”的认证或注销后再添加新的企业。',
            toAuth: '去认证',
            toCancel: '去注销',
            p1: '即将为您生成新企业，请完善企业名称',
            p2: '立即提交企业相关材料（用于实名认证），获得企业名称',
            p3: '暂不认证，手动填写',
            p4: '未经认证的企业名称仅对自己展示',
            loadingTxt: '正在校验数据，请稍等',
            p5: '请提交您的企业认证信息，预计审核时间为1-3个工作日。',
            p6: '如果您需要了解审核进度或有任何疑问，欢迎随时通过邮件联系我们：<EMAIL>，我们将竭诚为您提供帮助。',
            p7: '印鉴证明书',
            p8: '请先上传印鉴证明书',
            p9: '审核中。如果您需要了解审核进度可邮件联系我们：<EMAIL>',
            areaRegister: '企业注册地',
            jp: '日本',
            cn: '中国大陆',
            are: '阿拉伯联合酋长国',
            other: '其他',
            plsSelect: '请选择',
            tip1: '注册地为中国大陆地区的企业，需在 ent.bestsign.cn 中完成实名注册。在与中国大陆地区以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',
            tip2: '若您的企业已在上上签中国大陆版完成实名认证，可直接登录 ent.bestsign.cn，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与中国大陆版是完全独立隔离的。',
            tip3: '请提供您从当地商业监管机构获取的证件编号',
            tip4: '证明材料',
            tip5: '1、请您与您的专属客户经理联系，引导您完成企业实名。',
            tip6: '2、请上传贵司与上上签的商务合同截图或与专属客户经理的业务往来邮件。',
            tip7: '3、非日本、中国大陆地区的企业才可以使用此方式。',
            tip8: '4、提交后上上签在三个工作日内进行审核。',
            comNum: '企业证件号',
            plsEnterComNum: '请输入企业证件号',
            plsUploadBuyRecord: '请上传证明材料',
            uaeTip1: '注册地为阿联酋的企业，需在 uae.bestsign.com中完成实名注册。在与阿联酋以外的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',
            uaeTip2: '若您的企业已在上上签阿联酋版完成实名认证，可直接登录 uae.bestsign.com，便捷使用相关服务。需注意的是，您在上上签海外版所产生的数据，与阿联酋版是完全独立隔离的。',
            uaeTip3: '注册地为阿联酋和中国大陆以外的企业，需在 ent.bestsign.com中完成实名注册。在与阿联酋的企业签署合同时，可利用“跨境签”功能，在确保用户数据安全、不外泄的前提下，高效完成合同互签。',
        },
        enterEnterprise: '请输入企业名称',
        cancel: '取消',
        chooseEnterprise: '选择企业',
        noResult: '没有结果',
        confirm: '确定',
        plzEnterRightEnt: '请输入正确的企业名称',
        plzEnterRightCorporate: '请输入正确的法人番号',
        selectArea: '请选择企业注册地',
        corporateNumber: '法人番号',
        corporateNumberTip: '请输入法人番号',
        createSuccess: '创建成功',
        viewAllEnt: '查看全部企业',
        addEnt: '新增企业',
        exit: '退出',
        userCenter: '用户中心',
        groupConsole: '集团控制台',
        backToHome: '返回首页',
        connectToPrivateNetTip: '您的企业采用了合同私有存储的方式当前网络已连接至合同存储服务器',
        noConnectToPrivateNetTip: '您的企业采用了合同私有存储的方式，当前网络无法连接至合同存储服务器',
        advice1: '建议：',
        checkNetTip1: '检查当前网络能否访问企业内网',
        ecology: '来自{developerName}的企业账号',
        ecologyPerson: '来自{developerName}的个人账号',
        hybridNetworkDetail: '混合云网络详情',
        requestUrl: '请求地址',
        statusCode: '状态码',
        timeCost: '耗时',
        hybridStatusCodeExplain: {
            Error: '请检查当前网络状态是否正常连接',
            200: '成功',
            400: '请求出错',
            403: '请求地址不可用',
            404: '请求地址不存在',
            409: '业务异常',
            500: '请检查合同存储服务器是否正常运行',
        },
        switchConfirm: {
            title: '切换主体提示',
            main: '请确认，您将切换至个人主体?',
            tip: '切换至个人主体后，无法使用模板配置、企业管理等企业功能。发出的合同也将以您的个人身份发出。如需开展企业业务，请务必切回企业主体。',
            confirm: '暂不切换',
            cancel: '继续切换至个人',
        },
    },
    commonFooter: {
        record: 'ICP主体备案号：浙ICP备14031930号',
        hubbleRecordId: '网信算备：330106973391501230011',
        openPlatform: '开放平台',
        aboutBestSign: '关于公司',
        contact: '联系我们',
        recruitment: '诚聘英才',
        help: '帮助中心',
        copyright: '版权所有',
        company: '杭州尚尚签网络科技有限公司',
        ssqLogo: '上上签底栏Logo',
        provideTip: '电子签约服务由',
        ssq: '上上签',
        provide: '提供',
        signHotline: '签约服务热线',
        langSwitch: '切换语言',
    },
    footerSimpler: {
        ssqDes: '电子签约云平台领导者',
    },
    mainChart: {
        sendVolume: '发送量',
        signFinishAmout: '签署完成量',
        contractSendAmout: '合同发送量',
        contractSignAmout: '合同签署量',
        billion: '亿',
        wan: '万',
        fen: '份',
        ci: '次',
    },
    resetPassword: {
        safeTip: '安全提示！',
        weakPswTip: '系统检测到您的密码安全系数低，存在安全隐患，请重新设置密码。',
        oldPsw: '原密码',
        plsInputOldPsw: '请输入原密码',
        newPsw: '新密码',
        pswRule: '6-18位数字和大小写字母，支持常用特殊字符',
        confirm: '确定',
        oldPswNotEmpty: '原密码不能为空',
        newPswNotEmpty: '新密码不能为空',
        modifySuccess: '修改成功',
    },
    passwordExpiration: {
        notice: '您的密码已经90天未更换了，根据企业要求，请及时更换。',
        btn: '去更换',
    },
    giftContract: {
        expiryDateUtil: '使用期限至',
        buyTip: '购买电子合同套餐的用户，建议完成实名认证后购买，默认购买是个人账户',
        personAuthTip: '恭喜您成功完成个人实名认证',
        entAuthTip: '恭喜您成功完成企业实名认证',
        openSsqTip: '恭喜您完成账号注册',
        registerTip1: '去完成实名认证，继续获得免费合同份数',
        registerTip2: '份免费合同',
        registerTip3: '实名认证企业再得',
        giftTip1: '上上签赠送您',
        giftTip2: '份对私合同',
        and: '和',
        giftTip3: '份对公合同',
        sendContractTip: '完成首次合同发送，可免费再获取合同份数',
        entAuth: '进行企业认证',
        personAuth: '进行个人认证',
    },
    noticeMap: {
        notice: '消息',
        notRead: '条未读）',
        allRead: '全部已读',
        viewMore: '查看更多',
    },
    rechargeOrder: {
        searchType: '搜索类型：',
        deductTime: '实扣时间',
        selectDateRange: '选择时间范围：',
        till: '至',
        startDate: '开始日期',
        endDate: '结束日期',
        search: '搜索',
        sendTime: '发送时间',
        contractTitle: '合同名称',
        contractId: '合同ID',
        user: '使用人',
        belongDepartment: '所属部门',
        contractType: '合同类别',
        consumedCopies: '消费份数',
        consumedCopiesTips: '您本次合同业务理论上需要消费的合同份数。',
        personConsumedCopiesTips1: '1、消费记录以实际使用的合同套餐包类别为准。例如，若发送了 1 份对私合同，但当时无可用的对私合同套餐包，而是从对公套餐包中扣除，则消费记录将显示为 1 份对公合同。',
        personConsumedCopiesTips2: '2、2025 年 4 月 21 日起，普通个人用户不再享受因合同逾期、撤回或拒签返还合同份数的优惠。',
        exportMaxTip: '导出数据不能大于10000条',
        export: '导出',
        exportTip: '导出时间较长，请勿关闭当前页面。您可另开窗口处理其它业务',
        batchCenter: '批量任务中心',
        batchExportTip: '后台解析中，请在“批量任务中心”查看本次操作结果。',
    },
    rechargePackage: {
        manage: '充值管理',
        selectPackage: '选择套餐',
        tip1: '充值金额达40000元以上请咨询客服:',
        onlineService: '在线客服：',
        tip2: '优惠期截至12月31日',
        ccbPublicContract: '建设银行对公合同',
        publicContract: '对公合同',
        copiesTip: '份数：{num}',
        vailidityPeriodTip: '有效期：{month}个月',
        ccbPrivateContract: '建设银行对私合同',
        privateContract: '对私合同',
        copiesTip1: '份数:',
        giveCopiesTip: '+{num}份',
        buyNow: '立即购买',
        advancedFeature: '高级功能购买',
        learnDetail: '了解详情',
        isUsing: '已开通',
        unUsing: '未开通',
        payAmount: '支付金额',
        placeOrder: '下单购买',
        renewal: '续费购买',
        year: '年',
        limitBugTip: '特惠限购一次',
    },
    editFieldDialog: {
        chooseFieldType: '选择字段类型：',
        fieldType: {
            text: '文本',
            single: '单选',
            multi: '多选',
            date: '日期',
            combo: '下拉框',
            number: '数字',
            datetime: '时刻',
        },
        name: '名称',
        namePlace: '请输入名称',
        options: '备选项',
        addOptions: '新增备选项',
        contentFiller: '内容填写人',
        sender: '发件人',
        dateFormate: '样式',
        senderContent: '选择“发件人”，则此合同内容 在合同发出前由发件人填写',
        signer: '签署人',
        signerContent: '选择“签署人”，则此合同 内容在签署人签署时填写',
        fontSize: '字号',
        fontSizePlace: '请选择字号',
        requirements: '填写要求',
        required: '必填',
        confirm: '确 定',
        cancel: '取 消',
        addField: '添加字段',
        editField: '编辑字段',
        integer: '整数',
        decimalLimit: '限制',
        decimal: '位小数',
        errorMsg: {
            optionEmpty: '备选项名不能为空',
            optionHasEnComma: '备选项的名称，不允许输入英文逗号',
            optionNameFormat: '备选项名字只能为中文、英文、数字的组合',
            optionNameRepeat: '备选项名不能重复',
            enterFieldName: '请输入字段名称',
            fieldNameExist: '该字段名称已存在，请重新取名',
            fieldNameExistToOn: '该字段名称已存在，请启用重名的字段',
            optionsNeed: '请至少添加两个备选项',
            overCountLimit: '最多支持500个备选项',
        },
        editSuccess: '编辑成功',
        addSuccess: '新增成功',
        labelAlign: '对齐方式',
    },
    functionSupportDialog: {
        title: '功能介绍',
        inputTip: '若您有相关使用需求，欢迎在以下表格中填写您的需求，上上签会在24小时内安排专业人士联系您并提供服务指导。',
        useSence: '使用场景',
        useSenceTip: '如：人事/经销商/物流单据...',
        estimatedOnlineTime: '预计上线时间',
        requireContent: '请详细描述您的需求场景(选填)',
        requireContentTip: '请描述场景需求与期望的解决方案',
        getSupport: '获取专业服务支持',
        callServiceHotline: '立即拨打客服热线：400-993-6665',
        useSenceNotEmpty: '使用场景不能为空',
        requrieContentNotEmpty: '需求内容不能为空',
        oneWeek: '一周内',
        oneMonth: '一月内',
        other: '其他',
        submitSuccess: '提交成功',
        submitTrial: '立即试用',
        toTrial: '去试用',
        trialTip: '提交试用申请后，当前功能将立即开通。为了便于我们精准了解您的需求，您可以补充填写更多问题场景，我们将尽快与您联系。',
        applyTrial: '申请试用',
        trialSuccTip: '功能已开通，欢迎试用',
        goBuy: '直接购买',
        buyLater: '稍后再买',
        trialTipMap: {
            title: '试用须知',
            tip1: '1. 即开即用，有效期为7天；',
            tip2: '2. 试用期间，功能不收费；',
            tip3: '3. 每个企业主体，一个功能仅一次试用机会；',
            tip4: '4. 试用期间可自助购买，使用不间断；',
            tip5: '5. 如您的试用已结束，可扫码联系上上签专业顾问了解详情：',
        },
        contactAdminTip: '如需使用，请联系您的企业管理员{tip}购买开通',
        trialEndTip: '试用期结束，点击购买',
        trialRemainDayTip: '试用期剩{day}天，点击购买',
        trialEnd: '试用结束/功能到期',
        trialEndMap: {
            deactivateTip: '{feature}功能已到期停用，请清除配置，或者续费后，方可继续使用。',
            feature1: '合同附属资料',
            remove1: '清除配置方法为：编辑模板-找到配置好的添加合同附属资料，将其删除。',
            feature2: '手写笔迹识别',
            remove2: '清除配置方法为：编辑模板-找到配置好的笔迹识别，将其删除。',
            feature3: '合同装饰：骑缝章+水印',
            remove3: '清除配置方法为：编辑模板-找到配置好的合同装饰，将其删除。',
            feature4: '合同发送审批',
            remove4: '清除配置方法为：企业控制台-停用所有审批流',
            feature5: '模板授权功能已到期停用，模板授权方案均已停用，仅主管理员有使用权限。可重新创建模板使用，或者通知管理员续费，之后继续使用。',
            remove5: '',
        },
    },
    RechargeAccount: {
        orders: {
            pay: '支付',
            money: '金额',
            monyUnit: '元',
            jaMonyUnit: '日元',
            main: '充值主体',
            chargeNow: '立即充值',
            payApproachType: '借记卡或信用卡',
            poweredBy: '技术支持提供方',
            // orderId: '订单号',
            // contractNum: '文档数量',
            // actualMoney: '实际付款金额',
            // date: '有效期至',
            numberUnit: '份',
            // goPay: '立即支付',
            // account: '充值账号',
            // unpaied: '未支付',
            // paied: '已支付',
            // limitedTime: '限免使用',
            // payResultDialogTitle: '充值提醒',
            // payResultSuccess: '充值成功',
            // useNow: '立即使用',
            // paymentTime: '付款时间',
            // dueDate: '到期日',
            // moneyWithUnit: '金额（元）',
            // paymentStatus: '付款状态',
        },
    },
};
// 基础组件翻译
const components = {
    helperFloat: {
        advice: '咨询建议',
        hide: '隐藏',
        suggest: '提建议',
        onlineService: '在线客服',
        minimize: '最小化',
        enlarge: '放大',
        zoomOut: '缩小',
        person: '个人',
        ent: '企业',
        checkMoreVersion: '查看更多版本',
        submitSuccess: '上报成功',
        reportLog: '上报日志',
    },
    countDown: {
        resendCode: '重新获取',
        getVerifyCode: '获取验证码',
    },
    messageBox: {
        confirm: '确定',
    },
    example: {
        sideDisplay: '侧边展示',
        clickPreview: '点击预览',
        previewTitle: '预览标题',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: '印章提示',
            tip: '检测到您当前印章图片与您的企业身份不符，当前印章图片识别结果：',
            tip2: '是否要继续使用当前印章图片？',
            guide: '如何上传正确的印章 >>',
            next: '继续使用',
            tip3: '且您的印章名称不符合规范，带有“{keyWord}”字样。',
            tip4: '检测到印章名称不符合规范，带有“{keyWord}”字样，是否要继续使用？',
        },
        exampleSeal: {
            title: '上传印章图案方式',
            way1: ['方式一：', '1、在白色纸张上盖一个实体印章图案', '2、拍照，并将图片上传至平台'],
            way2: ['方式二：', '直接使用平台的生成电子章功能，如图：'],
            errorWay: ['错误方式：', '手持印章', '无关图片', '营业执照'],
        },
        confirm: '确认',
        cancel: '取消',
    },
};

// 公共库内其他用到翻译的内容
const commonother = {
    validationMsg: { // 校验相关提示
        notEmpty: '不能为空!',
        enter6to18n: '请输入6-18位数字、字母',
        enter8to18n: '请输入8-18位数字、字母',
        errEmailOrTel: '请输入正确的邮箱或手机号!',
        verCodeFormatErr: '验证码错误',
        signPwdType: '请输入6位数字',

        enterActualEntName: '请填写真实的企业名称',
        enterCorrectName: '请输入正确的姓名',
        enterCorrectPhoneNum: '请输入正确的手机号',
        enterCorrectEmail: '请输入正确的邮箱',
        imgCodeErr: '图形验证码错误',
        enterCorrectIdNum: '请输入正确的证件号码',

    },
    pubTips: {
        loginOverdue: '登录已失效，请重新登录',
        errorTip: '错误提示',
        know: '知道了',
        serverError: '服务器开了点小差，请稍后再试',
    },
    commonNomal: {
        yesterday: '昨天',
        ssq: '上上签',
        ssqPlatform: '上上签电子签约云平台',
        ssqTestPlatform: '（限测试用）BestSign电子签约云平台',
        pageExpiredTip: '页面已过期，请刷新重试',
        pswCodeSimpleTip: '密码需包含6-18位数字和大小写字母，请重新设置',
    },
    codeHandlerMap: {
        tip1: '您对该模板的使用权限被模板创建者收回',
        tip2: '此模板已被创建者删除',
        tip3: '此模板已被创建者停用，无法继续使用',
        tip4: '无法使用该模板',
        tip5: '回到模板列表',
        errorTip: '服务器开了点小差，请稍后再试',
        getNewJar: '请联系上上签升级jar包',
        networkNotConnect: '当前网络不可用，请检查你的网络设置',
        notAccessPage: '无法访问此页面',
        page404: '没有找到您访问的页面（404）',
        goHome: '返回首页',
        downloadApp: '下载上上签APP',
        advancedFeatureError: '该功能仅限高级版本客户使用，请联系上上签升级',
    },
    catchMap: {
        view: '查看',
        download: '下载',
        reject: '拒签',
        revoke: '撤销',
        delete: '删除',
        cantOperate: '无法{operate}合同',
        hybridNetHeader: '发件方企业采用了合同私有存储的方式，但当前网络无法连接至发件方的合同存储服务器。',
        hybridNetMsg: '建议您：检查网络是否正常',
        checkNet: '请检查网络是否正常',
        hybridNotConnect: '原因：您的企业采用了合同私有存储的方式，但当前网络无法连接至合同存储服务器。',
        hybridSuggest: '建议您：(1)检查网络是否正常；(2)检查合同存储服务器是否正常运行',
        goHomePage: '返回首页',
    },
    // 日文版签署前实名
    authBeforeSignJa: {
        plsConfirmIdentity: '请确认您的身份',
    },
    fontSizeMap: {
        one: '一号',
        smallOne: '小一号',
        two: '二号',
        smallTwo: '小二号',
        three: '三号',
        smallThree: '小三号',
        four: '四号',
        smallFour: '小四号',
        five: '五号',
        smallFive: '小五号',
    },
    replaceEmpty: '检测到您输入的名称中有空格，可能导致姓名与实际不符，影响签署。我们已帮您去掉空格。',
};
export default {
    ...businessComponents,
    ...components,
    ...commonother,
    lang: 'zh',
};
