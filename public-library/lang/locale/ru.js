// 业务组件翻译
const businessComponents = {
    TagManage: {
        labelOperate: {
            editLabel: '编辑标签名称',
            setRule: '设置贴标签规则',
            dropRule: '设置撕标签规则',
            setPutRemind: '设置贴标签提醒',
            afterDays: '天后',
            remindPeople: '提醒责任人',
            edit: '编辑',
            labelNameLengthLimit: '标签名称最长为十五个中文字符',
            labelNameRequire: '请输入标签名称',
            setLblRuleTip: '设置“{name}”标签规则',
            remind: '提醒',
            done: '完成',
        },
        putLabelRules: {
            name: '贴标签',
            tip: '*当事件触发时，合同会自动贴上该标签。所以请先选择触发事件：',
            manual: '手动贴标签',
            afterSent: '合同发送后',
            inputDaysTip: '请输入天数',
            beforeSignDeadline: '合同截止签署前',
            afterDone: '合同完成后',
            beforeDue: '合同到期前',
            afterDue: '合同到期后',
            afterReject: '合同被拒签',
            day: '天',
            minSet: '至少设置',
            maxSet: '最多设置',
        },
        dropLabelRules: {
            name: '撕标签',
            tip: '当事件触发时，会自动撕掉合同标签。所以请选择触发事件：',
            manual: '手动撕标签',
            afterDone: '合同完成后',
            afterDue: '合同到期后',
        },
        remindRule: {
            tip: '设置提醒时间以便向相关责任人发送通知：',
            remindDays: '提醒时间(如粘贴标签3天后发送通知)',
            remindDayTip: '请输入天数',
            remindPerson: '提醒人员',
            remindPersonTip: '请输入邮箱地址',
            addRemindPerson: '新增通知邮箱',
            remindEmailTip: '请输入正确的邮箱地址',
        },
    },
    batchAddOptionsDialog: {
        batchAddOption: '批量添加选项',
        batchAddTip1: '默认以行为单位，一行一项，请依次填写。',
        batchAddTip2: '最多支持500条，建议300条以内，名字长度100字以内。',
        batchAddPlaceHolder: '请输入内容',
        searchContentLengthLimitTip: '最多支持500个备选项，超出不做处理',
        searchContentPlaceholder: '请输入搜索词',
        confirm: '确认',
        cancel: '取消',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: '已被实名',
        assignedIdentity: '发件方填写的签约主体为：',
        entBeenAuthenticated: '该企业已被实名，主管理员信息如下：',
        entAdminName: '管理员姓名：',
        entAdminAccount: '管理员账号：',
        applyToBeAdmin: '我要申诉成为主管理员',
        contactToJoin: '联系管理员加入企业',
        applicant: '申请人',
        inputYourName: '请输入您的姓名',
        account: '账号',
        send: '发送',
        sendWishToJoin: '您可以通过账号申诉成为管理员，也可以向管理员发送加入企业的申请',
        applyToJoin: '您还未加入该企业，无法签署该合同，是否要申请加入？',
        sentSuccessful: '发送成功',
    },
    autoSeal: {
        add: '添加',
        tip: '提示',
        confirm: '确认',
        cancel: '取消',
        save: '保存',
        signPsw: '签约密码',
        forgetPsw: '忘记密码',
        msgTip: '一直收不到短信？试试',
        verificationCode: '验证码',
        voiceVerCode: '语音验证码',
        SMSVerCode: '短信验证码',
        or: '或',
        emailVerCode: '邮箱验证码',
        SentSuccessfully: '发送成功！',
        mail: '邮箱',
        phone: '手机号',
        inputPwd: '请输入签约密码',
        verCodeInputErr: '请先获取验证码！',
        inputNumber6: '请输入6位数字',
        sendInternalErr: '发送时间间隔过短',
        openAutoSeal: '启用自动盖章',
        closeAutoSeal: '关闭自动盖章',
        closeAutoSealTip: '关闭后，印章持有角色/持有人无法继续使用该印章自动签署',
        autoSignTipTitle: '允许企业成员使用此印章自动签，触发条件需要同时满足：',
        autoPersonSignTipTitle: '允许个人使用此签名章自动签，触发条件需要同时满足：',
        autoSealDialogTip: '自动签的配置已生效，新接收到的“需要我签署”的合同很快就能被自动签署。 | 但此前收到的合同需要等待较长时间才能完成签署，请耐心等待。',
        iKnow: '我知道了',
        iSee: '我知道了',
        defaultText: '*默认：',
        account: '账号：',
        enterprise: '*企业：',
        contractContent: '合同内容：',
        senderEnable: '发件方允许合同被自动签',
        selfEntEnable: '本企业发送的合同',
        senderEntEnable: '指定发件方企业',
        senderEnt: '发件方企业',
        receiveEnt: '接收方企业',
        inputEntNamePlaceholder: '请输入企业名称',
        includeGroupMemeber: '包含本集团子公司发送的合同',
        senderAccountEnable: '指定发件方账号',
        inputAccountPlaceholder: '请输入账号',
        phoneOrEamilTip: '请输入正确的手机号或邮箱',
        unpointAccountTips: '未指定则不对账号做校验',
        contractIdEnable: '指定合同ID，锁定类似的合同',
        inputContractIdPlaceholder: '请输入合同Id',
        inputContractIdTips: '请输入正确的合同Id',
        templateIdEnable: '指定合同模板ID（需向发件方索取）',
        inputTemplateIdPlaceholder: '请输入模板Id',
        inputTemplateIdTips: '请输入正确的合同模板Id',
        unpointContractContentTips: '未指定则不对合同内容做校验',
        pointSenderEnt: '请指定发件方企业',
        versionTip: '您需求开启更高版本才能勾选“本企业发送的合同”。',
        pointEntName: '请指定具体发件方企业名称',
        pointReceiveEnt: '请指定接收方企业',
        pointReceiveEntName: '请选择接收方企业名称',
        ponitSenderAccount: '请指定具体发件方账号',
        pointContractContent: '请指定具体合同内容',
        maxAddLimit: '最多添加10条数据',

        specialConfirmTip: '特别提示：当您按照自动签功能页面提示填写信息、勾选相应功能并点击保存后，即表示您已充分阅读、理解并接受该功能的已勾选选项的全部内容。',
        warningTip: {
            title: '*上述操作的过程中，如果您不同意任何相关内容或条款的约定，您应立即停止操作行为：',
            tip1: '（1）自动签署的合同内容是不确定的。虽然您可以指定模板ID限制发件方必须使用某一个合同模板，但是合同模板上的部分字段的数值是不固定的，而且合同模板有可能会被修改。',
            tip2: '（2）自动签署后的合同你是不可以撤回盖章或签名的。您收到的合同后满足您的设置条件后，立即签署，如果您事后在“合同管理”中阅读合同与您的预期不一致，需联系发件方撤销合同，或签署作废申明。',
        },
        suggestionsTip: {
            title: '*如果您对上述风险有疑虑，建议您：',
            tip1: '（1）不启用自动盖章功能。',
            tip2: '（2）或与发件方企业明确合同的发件人账号、合同内容，并在充分信任的基础上才能启用自动盖章功能。',
        },
        hasReadAndAccept: '我已阅读并接受以上提示',
        acceptTipFirst: '请先接受以上提示',
        enableTipText: '开启后，签署时上上签将调用您的数字证书，也为您提供到期自动续签服务。',
    },
    certificationOutOfCourt: {
        dataRejected: 'mатериал отклонен',
        your: 'Ваша',
        authRejectReason: 'Причина отказа',
        yourAuthInfo: 'Ваша аутентифицированная информация: | Подлинная имя: | Идентификационный номер:',
        baseInfo: 'Основная информация',
    },
    certificationPermissionNoticeStatic: {
        authPermissions: {
            0: 'Пройдя аутентификацию вы получите следующие разрешения:',
            1: 'Отправить контракт',
            2: 'Откройте новую электронную подпись, новой модели',
            3: 'Улучшить безопасность аккаунта',
            4: 'Получите больше услуг по защите членства',
            5: 'Функция онлайн управления контрактами',
            6: 'Повысить эффективность бизнеса',
            7: 'Получить тираж электронных контрактов',
            8: 'Удовлетворить ваши потребности в электронной подписи',
        },
    },
    companyDeptTree: {
        searchTip: 'Поддержка ввода учетной записи / поиска имени',
        loadMore: '加载更多...',
    },
    companyFull: {
        addMember: 'Добавить участника',
        chooseMember: 'Выберите участника',
        selectAll: 'Выбрать все',
        choosedMember: 'Выбранный участник',
        confirm: 'Подтвердить ',
        noneMemberChoosedTip: 'Пожалуйста, сначала выберите участника',
    },
    recharge: {
        balance: 'Баланс счета',
    },
    commonHeader: {
        usercenter: 'Личный кабинет',
        console: 'Платформа управления организацией',
        home: 'Страница ',
        contractManagement: 'Управление контрактами',
        templateManage: 'Управление шаблонами',
        statisticCharts: 'Статистический отчет',
        service: 'Сервис',
        offlineContractManage: 'Управление оффлайн контрактами',
        contractDrafting: 'Составить контракт',
        fileService: 'лог-файл / Архив ',
        authenticating: 'Аутентифицируется… ',
        unAuthenticate: 'Не аутентифицировать ',
        rejectAuthenticate: '实名已驳回',
        entAccount: 'Аккаунт компаний ',
        personAccount: 'Аккаунт физического лица ',
        enterpriseConsole: 'Платформа управления компаниями',
        createEnt: 'Создать бизнес',
        viewDetail: 'Нажмите, чтобы посмотреть детали',
        message: 'Сообщение ',
        video: 'Настроить видео ',
        tip: 'Напоминать',
        ok: 'Понятно ',
        entName: 'Название компании',
        createCompnayP: {
            p1: 'Чтобы создать новый бизнес для вас, пожалуйста, улучшите название компании',
            p2: 'Немедленно предоставьте соответствующие материалы компаний (для подтверждение личности), и получите название компаний ',
            p3: 'Временно не аутентифицируется, заполните вручную',
            p4: 'Не аутентифицированные названия компаний, показывает только для себя. ',
        },
        enterEnterprise: 'Пожалуйста, введите название компании',
        cancel: 'Отмена ',
        chooseEnterprise: 'Выберите компанию',
        noResult: 'Нет результата ',
        confirm: 'Подтверждение ',
        plzEnterRightEnt: 'Пожалуйста, введите правильное название компании',
        plzEnterRightCorporate: 'Please enter the correct corporate number',
        corporateNumber: '法人番号',
        createSuccess: 'Создан успешно',
        viewAllEnt: 'Посмотреть все компании',
        addEnt: 'Добавить новую компанию ',
        exit: 'Выход',
        userCenter: 'Личный кабинет',
        hybridNetworkDetail: '混合云网络详情',
        requestUrl: '请求地址',
        statusCode: '状态码',
        timeCost: '耗时',
        hybridStatusCodeExplain: {
            Error: '请检查当前网络状态是否正常连接',
            200: '成功',
            403: '请求地址不可用',
            404: '请求地址不存在',
            409: '业务异常',
            500: '请检查合同存储服务器是否正常运行',
        },
    },
    commonFooter: {
        record: 'Главный регистрационный номер ICP: провинция Чжэцзян ICP номер 14031930',
        openPlatform: 'Открытая платформа',
        aboutBestSign: 'О компании',
        contact: 'Свяжитесь с нами',
        recruitment: 'Рекрутинг талант',
        help: 'Справочный центр',
        copyright: 'авторское право',
        company: 'HangZhou BestSign Ltd.',
        langSwitch: 'Переключение языков',
    },
    footerSimpler: {
        ssqDes: 'Лидер облачной платформы Электронной Цифровой Подписи',
    },
};
// 基础组件翻译
const components = {
    helperFloat: {
        advice: 'совет',
        enlarge: '放大',
        zoomOut: '缩小',
    },
    countDown: {
        resendCode: 'Получить заново',
        getVerifyCode: 'Получить код подтверждения',
    },
    messageBox: {
        confirm: 'Подтвердить',
    },
};

// 公共库内其他用到翻译的内容
const commonother = {
    validationMsg: {
        notEmpty: 'Не должно быть пустым!',
        enter6to18n: 'Пожалуйста, введите 6-18 цифр, букв',
        errEmailOrTel: 'Пожалуйста, введите правильный E-mail или номер телефона!',
        verCodeFormatErr: 'Ошибочный код подтверждения',
        signPwdType: 'Пожалуйста, введите 6 цифр',
    },
    pubTips: {
        loginOverdue: 'Логин истек, пожалуйста, войдите снова',
        errorTip: 'сообщение об ошибке',
        know: 'Понятно',
        serverError: 'На сервере небольшой пробел, повторите попытку позже.',
    },
    // 日文版签署前实名
    authBeforeSignJa: {
        plsConfirmIdentity: 'Пожалуйста, подтвердите свою личность',
    },
    fontSizeMap: {
        one: '一号',
        smallOne: '小一号',
        two: '二号',
        smallTwo: '小二号',
        three: '三号',
        smallThree: '小三号',
        four: '四号',
        smallFour: '小四号',
        five: '五号',
        smallFive: '小五号',
    },
};

export default {
    ...businessComponents,
    ...components,
    ...commonother,
    lang: 'ru',
};
