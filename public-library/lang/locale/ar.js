// 业务组件翻译
const businessComponents = {
    userHeader: {
        hi: 'مرحباً',
        exit: 'خروج',
        help: 'مساعدة',
        hotline: 'الخط الساخن للخدمة',
    },
    ssoLoginConfig: {
        notBelongToEntTip: 'تحتاج إلى إعادة تسجيل الدخول إلى منصة بست ساين لإرسال العقد (أو إدارة القوالب)',
        operationStep: {
            one: 'الخطوة الأولى: بعد النقر على متابعة، العودة إلى صفحة تسجيل الدخول',
            two: 'الخطوة الثانية: إدخال كلمة المرور والدخول إلى منصة بست ساين',
            three: 'الخطوة الثالثة: إرسال العقد (أو إدارة القوالب)',
        },
        continue: 'متابعة',
        cancel: 'إلغاء',
        tip: 'تنبيه',
    },
    TagManage: {
        labelOperate: {
            editLabel: 'تعديل اسم العلامة',
            setRule: 'تعيين قواعد العلامات',
            dropRule: 'تعيين قواعد إزالة العلامات',
            setPutRemind: 'إعداد تذكيرات العلامات',
            afterDays: ' يوم لاحقاً',
            remindPeople: ' تذكير الأشخاص المحددين',
            edit: 'تعديل',
            labelNameLengthLimit: 'يمكن أن يصل طول اسم العلامة إلى عشرة أحرف صينية',
            labelNameRequire: 'الرجاء إدخال اسم العلامة',
            setLblRuleTip: 'تعيين قواعد علامة "{name}"',
            remind: 'تذكير',
            done: 'إكمال',
        },
        putLabelRules: {
            name: 'إضافة علامة',
            tip: '*عند تفعيل الحدث، سيتم وضع هذه العلامة على العقد تلقائياً. لذا يرجى اختيار حدث التفعيل أولاً:',
            manual: 'وضع العلامات يدوياً',
            afterSent: 'بعد إرسال العقد',
            inputDaysTip: 'الرجاء إدخال عدد الأيام',
            beforeSignDeadline: 'قبل الموعد النهائي للتوقيع',
            afterDone: 'بعد اكتمال العقد',
            beforeDue: 'قبل انتهاء صلاحية العقد',
            afterDue: 'بعد انتهاء صلاحية العقد',
            afterReject: 'بعد رفض العقد',
            day: 'يوم',
            minSet: 'على الأقل تعيين',
            maxSet: 'حتى تعيين',
        },
        dropLabelRules: {
            name: 'إزالة العلامة',
            tip: 'عند تفعيل الحدث، تتم إزالة علامة العقد تلقائياً. لذا يرجى اختيار حدث التفعيل:',
            manual: 'إزالة العلامة يدوياً',
            afterDone: 'بعد اكتمال العقد',
            afterDue: 'بعد انتهاء صلاحية العقد',
        },
        remindRule: {
            tip: 'تعيين مؤقت لإرسال إشعارات إلى شخص محدد:',
            remindDays: 'وقت التذكير (مثال: إرسال إشعار بعد 3 أيام من وضع العلامة)',
            remindDayTip: 'الرجاء إدخال عدد الأيام',
            remindPerson: 'المستلم ',
            remindPersonTip: 'الرجاء إدخال عنوان البريد الإلكتروني',
            addRemindPerson: 'بريد إلكتروني جديد للإشعار',
            remindEmailTip: 'الرجاء إدخال عنوان بريد إلكتروني صحيح',
        },
    },
    batchAddOptionsDialog: {
        batchAddOption: 'إضافة عناصر بالجملة',
        batchAddTip1: 'سطر واحد لكل حقل.',
        batchAddTip2: 'الحد الأقصى 500 سطر، ويُوصى بأن يكون ضمن 300 سطر. لا يمكن أن يتجاوز اسم الحقل 100 حرف.',
        batchAddPlaceHolder: 'الرجاء إدخال المحتوى',
        searchContentLengthLimitTip: 'يتم دعم ما يصل إلى 500 خيار احتياطي، وما يتجاوز ذلك لا يتطلب معالجة',
        searchContentPlaceholder: 'الرجاء إدخال مصطلح البحث',
        confirm: 'تأكيد',
        cancel: 'إلغاء',
    },
    dialogApplyJoinEnt: {
        beenAuthenticated: 'تم التحقق من الاسم الحقيقي',
        assignedIdentity: 'موضوع العقد الذي أدخله المرسل هو:',
        entBeenAuthenticated: 'تم التحقق من اسم الشركة الحقيقي، ومعلومات المسؤول الرئيسي كما يلي:',
        entAdminName: 'اسم المسؤول:',
        entAdminAccount: 'حساب المسؤول:',
        applyToBeAdmin: 'أريد تقديم التماس',
        contactToJoin: 'الانضمام إلى الشركة',
        applicant: 'مقدم الطلب',
        inputYourName: 'الرجاء إدخال اسمك',
        account: 'الحساب',
        send: 'إرسال',
        sendWishToJoin: 'يمكنك أن تصبح مسؤولاً من خلال التماس الحساب، أو يمكنك إرسال طلب إلى المسؤول للانضمام إلى الشركة',
        applyToJoin: 'لم تنضم إلى الشركة ولا يمكنك توقيع العقد. هل تريد التقدم بطلب للانضمام؟',
        sentSuccessful: 'تم الإرسال بنجاح',
    },
    autoSeal: {
        add: 'إضافة',
        tip: 'ملاحظة',
        confirm: 'تأكيد',
        cancel: 'إلغاء',
        save: 'حفظ',
        preStep: 'الخطوة السابقة',
        nextStep: 'الخطوة التالية',
        done: 'إتمام',
        manageConfig: 'إعدادات المسؤول',
        codeVerify: 'إدخال رمز التحقق',
        signerConfig: 'إعدادات الموقّع',
        step1Tip: 'الخطوة الأولى: يرجى تخصيص "{name}" لأعضاء المؤسسة الذين يحتاجون إلى استخدام وظيفة الختم التلقائي.',
        step2Tip: 'الخطوة الثانية: يرجى من كل عضو في المؤسسة الدخول إلى',
        step2Tip1: 'مركز المستخدم - أختامي',
        step2Tip2: 'وتفعيل زر "الختم التلقائي". بعد ذلك، سيقوم النظام تلقائياً بختم العقود التي تستوفي شروط التوقيع التلقائي.',
        openAutoTip: 'فيما يلي قائمة الأعضاء الذين قاموا بتفعيل وظيفة الختم التلقائي لـ "{name}":',
        howToOpenTip: 'كيفية إعداد التوقيع التلقائي لأعضاء المؤسسة',
        signPsw: 'كلمة مرور التوقيع',
        forgetPsw: 'نسيت كلمة المرور',
        msgTip: 'لا يمكنك استلام الرسائل القصيرة دائماً؟ جرب ',
        verificationCode: 'رمز التحقق',
        voiceVerCode: 'رمز التحقق الصوتي',
        SMSVerCode: 'رمز التحقق عبر الرسائل القصيرة',
        or: ' أو ',
        emailVerCode: 'رمز التحقق عبر البريد الإلكتروني',
        SentSuccessfully: 'تم الإرسال بنجاح!',
        mail: 'البريد الإلكتروني',
        phone: 'الهاتف',
        inputPwd: 'الرجاء إدخال كلمة مرور التوقيع',
        verCodeInputErr: 'الرجاء الحصول على رمز التحقق أولاً!',
        verCodeFail: 'رمز التحقق غير صحيح',
        inputNumber6: 'الرجاء إدخال 6 أرقام',
        inputPwd6: 'الرجاء إدخال كلمة مرور التوقيع المكونة من 6 أرقام',
        sendInternalErr: 'الفترة الزمنية بين الإرسال قصيرة جداً',
        openAutoSeal: 'تفعيل التوقيع التلقائي',
        closeAutoSeal: 'تعطيل التوقيع التلقائي',
        closeAutoSealTip: 'بعد الإغلاق، لا يمكن لدور/موظفي حامل الختم استخدام الختم للتوقيع تلقائياً',
        autoSignTipTitle: 'يُسمح لأعضاء الشركة بالتوقيع تلقائياً بهذا الختم. يجب أن تستوفي شروط التفعيل المتطلبات التالية:',
        autoPersonSignTipTitle: 'يُسمح للأفراد بالتوقيع تلقائياً بختم التوقيع هذا. يجب أن تستوفي شروط التفعيل المتطلبات التالية:',
        autoSealDialogTip: {
            1: 'The automatic signing feature has been activated. Newly received documents "Requiring My Signature" will be automatically signed soon.',
            2: 'Now you need to confirm: Do you want the system to automatically sign previously received contracts as well?',
            3: 'If yes, select "Yes" and click "Complete". The system will automatically use this seal to sign all eligible contracts. However, these contracts may take longer to process, please be patient.',
            4: 'If no, select "No" and click "Complete".',
        },
        iKnow: 'Complete',
        iSee: 'فهمت',
        yes: 'Yes',
        no: 'No',
        defaultText: '* الافتراضي:',
        account: 'الحساب:',
        enterprise: '*الشركة:',
        contractContent: 'محتوى العقد:',
        senderEnable: 'يسمح المرسل بالتوقيع التلقائي للعقد',
        selfEntEnable: 'العقود المرسلة من الشركة نفسها',
        senderEntEnable: 'العقود المرسلة من شركات محددة',
        senderEnt: 'الشركة المرسلة',
        receiveEnt: 'الشركة المستلمة',
        inputEntNamePlaceholder: 'أدخل اسم الشركة',
        includeGroupMemeber: 'تشمل العقود المرسلة من الشركات التابعة لمجموعتنا',
        senderAccountEnable: 'حساب المرسل المحدد',
        inputAccountPlaceholder: 'أدخل الحساب',
        phoneOrEamilTip: 'الرجاء إدخال رقم الهاتف المحمول أو عنوان البريد الإلكتروني الصحيح',
        unpointAccountTips: 'بدون تحقق إذا لم يتم التحديد',
        contractIdEnable: 'تحديد معرف العقد وقفل العقود المماثلة',
        inputContractIdPlaceholder: 'أدخل معرف العقد',
        inputContractIdTips: 'الرجاء إدخال معرف العقد الصحيح',
        templateIdEnable: 'معرف قالب العقد المحدد (احصل عليه من المرسل)',
        inputTemplateIdPlaceholder: 'أدخل معرف القالب',
        inputTemplateIdTips: 'الرجاء إدخال معرف قالب العقد الصحيح',
        unpointContractContentTips: 'بدون تحقق إذا لم يتم التحديد',
        pointSenderEnt: 'الرجاء تحديد الشركة المرسلة',
        versionTip: 'تحتاج إلى فتح نسخة أعلى للتحقق من "العقد المرسل من هذه الشركة".',
        pointEntName: 'الرجاء تحديد اسم الشركة المرسلة المحدد',
        pointReceiveEnt: 'الرجاء تحديد الشركة المستلمة',
        pointReceiveEntName: 'الرجاء اختيار اسم الشركة المستلمة',
        ponitSenderAccount: 'الرجاء تحديد رقم حساب المرسل',
        pointContractContent: 'الرجاء تحديد محتوى العقد المحدد',
        notClickAddIconTip: 'الرجاء النقر على زر الإضافة ثم النقر على زر الحفظ مرة أخرى',
        maxAddLimit: 'إضافة حتى 10 عناصر من البيانات',

        specialConfirmTip: 'ملاحظة خاصة: عند ملء المعلومات وفقاً لصفحة وظيفة التوقيع التلقائي، تحقق من الوظيفة المقابلة ثم احفظها. هذا يعني أنك قد قرأت وفهمت وقبلت بالكامل جميع العناصر المحددة لهذه الوظيفة.',
        warningTip: {
            title: '*أثناء العمليات، إذا كنت لا توافق على أي محتوى أو عناصر ذات صلة، يجب عليك التوقف فوراً:',
            tip1: '(1) محتوى العقد الموقع تلقائياً غير مؤكد. على الرغم من أنه يمكنك تحديد معرف القالب لتقييد المرسل من استخدام قالب عقد معين. ومع ذلك، قيم بعض الحقول في قالب العقد ليست ثابتة، ويمكن تعديل قالب العقد.',
            tip2: '(2) لا يمكنك استرجاع ختمك أو توقيعك من العقد الموقع تلقائياً. بعد استلام العقود التي تلبي شروط الإعداد الخاصة بك، سيتم توقيع العقد تلقائياً على الفور. إذا قرأت العقد في "إدارة العقود" لاحقاً وكان غير متوافق مع توقعاتك، فأنت بحاجة إلى الاتصال بالمرسل لإلغاء العقد أو توقيع بيان الإلغاء.',
        },
        suggestionsTip: {
            title: '*إذا كانت لديك مخاوف بشأن المخاطر المذكورة أعلاه، نقترح:',
            tip1: '(1) تعطيل وظيفة التوقيع التلقائي.',
            tip2: '(2) تأكيد رقم حساب المرسل ومحتوى العقد مع المرسل، ولا يمكن تمكين وظيفة التوقيع التلقائي إلا عندما تثق تماماً في المرسل.',
        },
        hasReadAndAccept: 'لقد قرأت وقبلت الملاحظات أعلاه.',
        acceptTipFirst: 'الرجاء قبول النصائح أعلاه أولاً',
        enableTipText: 'بعد اجتياز رمز التحقق، ستستخدم بست ساين شهادتك الرقمية عند توقيع العقود المقابلة، كما توفر خدمة التجديد التلقائي لشهادتك الرقمية.',
    },
    certificationOutOfCourt: {
        dataRejected: 'تم رفض المواد',
        your: 'الخاص بك',
        authRejectReason: 'سبب الرفض',
        yourAuthInfo: 'معلومات التوثيق الخاصة بك: | الاسم الحقيقي: | رقم الهوية:',
        baseInfo: 'المعلومات الأساسية',
        handheldIdNum: 'توثيق بطاقة الهوية المحمولة',
        faceAuth: 'توثيق التحقق من الوجه',
        noMainlandAuth: 'توثيق خارج البر الرئيسي الصيني',
        authNow: 'التوثيق فوراً',
        underReview: 'قيد المراجعة',
        bankCardAuth: 'توثيق البطاقة المصرفية',
        phoneAuth: 'توثيق رقم الهاتف المحمول',
        noMainlandPersonAuth: 'توثيق المقيم خارج البر الرئيسي الصيني',
    },
    certificationPermissionNoticeStatic: {
        authPermissions: {
            0: 'بعد إكمال التوثيق، ستحصل على الأذونات التالية:',
            1: 'بدء عقد',
            2: 'بنمط توقيع إلكتروني جديد',
            3: 'تحسين أمان الحساب',
            4: 'الاستمتاع بالمزيد من خدمات أمان العضوية',
            5: 'وظيفة إدارة العقود عبر الإنترنت',
            6: 'تحسين كفاءة العمل',
            7: 'الحصول على نسخ العقود الإلكترونية',
            8: 'تلبية احتياجاتك للتعاقد الإلكتروني',
        },
    },
    companyDeptTree: {
        searchTip: 'بحث',
        searchDeptTip: 'بحث',
        loadMore: 'تحميل المزيد...',
    },
    companyFull: {
        addMember: 'إضافة عضو',
        chooseMember: 'اختيار عضو',
        selectAll: 'اختيار الكل',
        choosedMember: 'العضو المختار',
        confirm: 'تأكيد',
        noneMemberChoosedTip: 'الرجاء اختيار عضو أولاً',
    },
    recharge: {
        balance: 'رصيد الحساب',
        noLimitContract: 'عدد غير محدود من العقود',
        unLimitUseTip: 'غير محدود خلال فترة الصلاحية',
        expireDate: 'تاريخ انتهاء الصلاحية',
        recharge: 'شحن',
        aiRecharge: 'مستشار الذكاء الاصطناعي للرقابة على مخاطر العقود',
        ai: 'مستشار الذكاء الاصطناعي للرقابة على مخاطر العقود',
        riskJudge: 'تقييم المخاطر',
        publicContract: ' للشركة',
        tip1: 'المستندات التي تضم حسابات شركات بين الموقعين (باستثناء المرسل)',
        tip2: 'المستندات التي لا تحتوي على موقع من الشركة (باستثناء المرسل)',
        useCopies: 'عدد النسخ المتاحة',
        withHoldCopies: 'مخصوم مسبقاً',
        withHoldCopiesTips: 'عدد العقود المخصومة مسبقاً من قبل النظام بعد إرسال هذا العقد. يتم استردادها إذا لم تكتمل المعاملة.',
        actuallDeductedCopies: 'الخصم الفعلي',
        actuallDeductedCopiesTips: 'عدد العقود المخصومة فعلياً من قبل النظام بعد إتمام هذه المعاملة.',
        privateContract: ' للفرد',
        onlinePay: 'الدفع عبر الإنترنت',
        noRefundAfterPay: '(بمجرد الشراء، لا يمكن إرجاع أو استبدال هذا المنتج/الخدمة)',
        orderNum: 'رقم الطلب:',
        getMoreContract: 'احصل على المزيد من العقود كمكافأة',
        discountCode: 'رمز الخصم',
        plsInputDiscountCode: 'الرجاء إدخال رمز الخصم',
        discountCodeErrorTip: 'رمز الخصم يمكن أن يحتوي على أرقام فقط!',
        discountTip: 'قبل الدفع، تحقق مما إذا كان الحساب لديه رمز خصم يمكن استخدامه، يمكن استخدام رمز خصم واحد فقط لكل طلب. إذا كنت بحاجة إلى استخدام رمز خصم آخر، يرجى العودة وإعادة تقديم الطلب',
        documentAmout: 'عدد المستندات: {num} نسخة',
        amoutTo: 'المبلغ: ￥{money}',
        validUntil: 'صالح حتى',
        document: 'المستندات',
        memberMumer: 'عدد الأعضاء:',
        number: 'العدد:',
        aliPay: 'علي باي',
        wechatPay: 'وي تشات باي',
        bankTransfer: 'تحويل بنكي',
        hotline: 'الخط الساخن',
        customerService: 'خدمة العملاء',
        rechareSubject: 'الشحن بواسطة',
        rechareAccout: 'حساب الشحن',
        invoiceTip: '(أي كيان إصدار الفواتير، لا يدعم تغيير كيان إصدار الفواتير)',
        person: 'شخص',
        ent: 'مؤسسة',
        notAuth: 'بدون اسم حقيقي',
        curRechareDialog: {
            subject: 'الشحن الحالي بواسطة',
            amount: 'حساب الشحن',
            tip: 'يرجى تأكيد كيان الشحن الحالي حتى لا يؤثر على عملية التوقيع',
            notAuthTip: 'لم يخضع الحساب الحالي للتحقق من الاسم الحقيقي، ويجب استخدام العقد بعد التحقق من الاسم الحقيقي. إذا كان عقد الشحن يحتاج إلى استخدامه كمؤسسة، يرجى إكمال التحقق من الاسم الحقيقي قبل الشحن.',
        },
        tip: 'تنبيه',
        amout1: 'المبلغ:',
        yuan: 'يوان',
        payValidTime: 'وقت الدفع:',
        within24h: 'خلال 24 ساعة',
        payNow: 'ادفع الآن',
        selfTransferBankAccout: 'يمكنك أيضاً التحويل الذاتي إلى الحساب المصرفي التالي',
        accountName: 'اسم الحساب',
        account: 'الحساب',
        accountBank: 'البنك المعتمد',
        lineNumber: 'رمز البنك',
        rechareTip1: {
            title: 'تذكير ودي:',
            tip1: 'يرجى تقديم رقم الطلب في معلومات الدفع.',
            tip2: 'بعد نجاح التحويل، يرجى الاتصال بخدمة العملاء.',
        },
        paySuccess: 'تم الدفع بنجاح',
        myOrder: 'طلبي',
        useRecord: 'سجل الاستخدام',
        batchInvoicing: 'إصدار الفواتير بالجملة',
        selectOrderTip: 'تم اختيار {length} طلبات',
        orderMap: {
            num: 'رقم الطلب',
            setMeal: 'الباقة',
            amountAndType: 'الكمية والنوع',
            amountYuan: 'المبلغ (يوان)',
            amountYuanUae: 'المبلغ',
            expireDate: 'تاريخ انتهاء الصلاحية',
            expireNum: 'رقم انتهاء الصلاحية',
            payStatus: 'حالة الدفع',
            noPay: 'غير مدفوع',
            payDate: 'وقت الدفع',
            invoiceStatus: 'حالة الفاتورة',
            applyInvoice: 'طلب إصدار فاتورة',
            reApply: 'إعادة طلب إصدار فاتورة',
            infoError: 'معلومات غير صحيحة',
            createDate: 'وقت الإنشاء',
            selectOneOrder: 'الرجاء اختيار طلب واحد أو أكثر',
            noInvoiceOrder: 'لا توجد طلبات قابلة للفوترة',
        },
        payStatusMap: {
            status1: 'مدفوع',
            status2: 'مغلق',
            status3: 'ملغي',
            status4: 'خطأ',
            status5: 'غير مدفوع',
        },
        billStatusMap: {
            status1: 'طلب إصدار فاتورة',
            status2: 'تم الطلب',
            status3: 'تم إصدار الفاتورة',
            status4: 'إعادة الطلب',
        },
        supportBatchInvoiceTip: 'الطلبات التالية تدعم طلب إصدار الفواتير بالجملة',
        batchApply: 'طلب بالجملة',
        ssqDealTip: '(بمجرد الاستلام، ستتم المعالجة خلال 7 أيام عمل من قبل بست ساين)',
        submit: 'تقديم',
        confirm: 'تأكيد',
        addNomalInvoice: 'فاتورة ضريبة القيمة المضافة العادية',
        addSpecialInvoice: 'فاتورة ضريبة القيمة المضافة الخاصة',
        entNomalInvoiceMap: {
            tip1: 'سيتم إرسال فاتورة ضريبة القيمة المضافة الإلكترونية العادية عبر البريد الإلكتروني',
            entName: 'اسم الشركة',
            taxNum: 'الرقم الضريبي',
            amount: 'مبلغ الفاتورة',
            inbox: 'صندوق الوارد',
            plsInputEntName: 'الرجاء إدخال اسم الشركة',
            plsInputInbox: 'الرجاء إدخال صندوق الوارد',
            plsInputEmail: 'الرجاء إدخال عنوان البريد الإلكتروني الصحيح',
            plsInputTaxNum: 'الرجاء إدخال الرقم الضريبي',
            name: 'الاسم',
            plsInputName: 'الرجاء إدخال اسمك الكامل',

        },
        entSpecialInvoiceMap: {
            mailTip: 'نظراً للترويج الوطني للفواتير الإلكترونية الرقمية، لم تعد المنصة تستخدم الفواتير الورقية. يتم استلام فواتير ضريبة القيمة المضافة الخاصة أيضاً في شكل بريد إلكتروني (تعبئة افتراضية لمعلومات الفوترة الأخيرة، يرجى التحقق بعناية).',
            // tip1: 'نصائح ودية: الفواتير التي تقل عن 600 يوان سيتم إرسالها بالبريد مع الدفع عند الاستلام.',
            // plsAgreeTip: 'الفواتير التي تقل عن 600 يوان، تدعم فقط الدفع عن طريق البريد، يرجى وضع علامة',
            entAddress: 'عنوان الشركة',
            entTel: 'رقم هاتف الشركة',
            bankAccount: 'البنك المعتمد',
            invoiceAmout: 'رقم الحساب',
            tip2: 'تحميل شهادة دافع الضرائب العام (صور بتنسيق jpg أو png)',
            tip3: 'املأ معلومات المستلم، سنرسل لك الفاتورة بالبريد.',
            // recipientName: 'اسم المستلم',
            // recipientPhone: 'هاتف المستلم',
            // recipientAddress: 'عنوان المستلم',
            plsInputEntAddress: 'الرجاء إدخال عنوان الشركة.',
            plsInputEntPhone: 'الرجاء إدخال رقم هاتف الشركة.',
            plsInputBankName: 'الرجاء إدخال اسم البنك المعتمد.',
            plsInputBankAccount: 'الرجاء إدخال رقم الحساب.',
            plsUploadCertificate: 'الرجاء تحميل شهادة دافع الضرائب العام (صور بتنسيق jpg أو png).',
            // plsInputRecipientName: 'الرجاء إدخال اسم المستلم.',
            // plsInputRecipientPhone: 'الرجاء إدخال رقم هاتف المستلم.',
            // plsInputRecipientAddress: 'الرجاء إدخال عنوان المستلم.',
            picNotExceetTip: 'لا يمكن أن يتجاوز حجم الصورة 5 ميجابايت',
            picNotMeetRequire: 'تنسيق الصورة لا يلبي المتطلبات',
            clickUpload: 'انقر للتحميل',
            certifyProvePic: 'شهادة دافع الضرائب العام',
        },
        errorTip: 'خطأ',
        applyInvoiceSubmit: 'تم تقديم طلب الفوترة الخاص بك!',
        getInvoiceTip: 'ستتلقى الفاتورة خلال 7-15 يوم عمل بعد التحقق من المعلومات!',
    },
    common: {
        close: 'إغلاق',
        confirmClose: 'تأكيد الإغلاق؟',
        time: 'الوقت',
        day: 'يوم',
        month: 'شهر',
        groupAgentAuth: '(مصادقة مساعدة المجموعة)',
        plsSelectEnt: 'الرجاء اختيار الشركة أولاً',
        checkVideoTip: 'شاهد فيديو دليل التشغيل!',
        selectBusLine: 'الرجاء اختيار خط الأعمال',
        entChanged: 'الرجاء تحديث الصفحة.',
        refreshPage: 'تحديث',
    },
    commonHeader: {
        console: 'منصة إدارة المؤسسات',
        home: 'الرئيسية',
        contractManagement: 'العقود',
        templateManage: 'القوالب',
        statisticCharts: 'التقارير',
        service: 'الخدمات',
        offlineContractManage: 'العقود غير المتصلة',
        contractDrafting: 'صياغة العقود',
        fileService: 'الملفات +',
        authenticating: 'جارٍ المصادقة...',
        unAuthenticate: 'لم تتم المصادقة',
        rejectAuthenticate: 'تم رفض المصادقة',
        entAccount: 'حساب الشركة',
        personAccount: 'حساب شخصي',
        enterpriseConsole: 'لوحة تحكم الشركة',
        createEnt: 'إنشاء شركة',
        viewDetail: 'انقر للتفاصيل',
        message: 'الرسائل',
        video: 'إعداد الفيديو',
        tip: 'تنبيه',
        ok: 'فهمت',
        entName: 'اسم الشركة',
        createCompnayP: {
            hasUnAuthEnt: 'إذا كانت هناك شركات تحت حسابك لم تكمل عملية التوثيق، يرجى إكمال توثيق "الشركات التي لم تكمل التوثيق" أو إلغاء التوثيق قبل إضافة شركة جديدة.',
            toAuth: 'للتوثيق',
            toCancel: 'للإلغاء',
            p1: 'سيتم قريباً إنشاء شركة جديدة لك، يرجى تحسين اسم الشركة',
            p2: 'قدم فوراً مواد الشركة ذات الصلة (للتعريف)، واحصل على اسم الشركة',
            p3: 'لم يتم التوثيق مؤقتاً، املأ يدوياً',
            p4: 'أسماء الشركات غير الموثقة تظهر فقط لأنفسهم،',
            loadingTxt: 'جارٍ التحقق من البيانات، يرجى الانتظار.',
            p5: 'يرجى تقديم معلومات عن المؤسسة الخاصة بك شهادة تقدير وقت المراجعة 1-3 أيام عمل .',
            p6: 'إذا كنت بحاجة إلى معرفة التقدم المحرز في مراجعة الحسابات أو لديك أي استفسار ، لا تتردد في الاتصال بنا عن طريق البريد الإلكتروني : <EMAIL> ، ونحن سوف نقدم لكم المساعدة بكل إخلاص .',
            p7: 'شهادة ختم',
            p8: 'يرجى تحميل شهادة ختم',
            p9: 'مراجعة الحسابات . إذا كنت بحاجة إلى معرفة التقدم المحرز في مراجعة الحسابات يمكنك الاتصال بنا عن طريق البريد الإلكتروني : <EMAIL>',
            areaRegister: 'مكان تسجيل الأعمال',
            jp: 'اليابان .',
            cn: 'البر الرئيسى للصين',
            are: 'الإمارات العربية المتحدة',
            other: 'أخرى .',
            plsSelect: 'الرجاء اختيار',
            tip1: 'الشركات المسجلة في البر الرئيسى للصين يجب أن تكون مسجلة مع الاسم الحقيقي في ent.bestsign.cn . . . . . . . عند التوقيع على العقود مع الشركات خارج البر الرئيسى للصين ، يمكن استخدام " عبر الحدود " التوقيع على وظيفة لضمان سلامة بيانات المستخدم ، لا تسرب ، على فرضية أن العقود المتبادلة يمكن أن تكتمل على نحو فعال .',
            tip2: 'إذا كانت المؤسسة الخاصة بك قد وقعت على النسخة الصينية من الانتهاء من شهادة الاسم الحقيقي ، يمكنك تسجيل الدخول مباشرة إلى nt.bestsign.cn ، وسهولة استخدام الخدمات ذات الصلة . وتجدر الإشارة إلى أن البيانات التي تم الحصول عليها من خلال التوقيع على النسخة الأجنبية هي معزولة تماما عن النسخة الصينية .',
            tip3: 'يرجى تقديم رقم الوثيقة التي يمكنك الحصول عليها من الأعمال التجارية المحلية منظم',
            tip4: 'مواد الإثبات',
            tip5: '1 ، يرجى الاتصال مدير الحساب الخاص بك الحصري ، دليل لكم لإكمال الاسم الحقيقي للمؤسسة .',
            tip6: '2 ، يرجى إرسال الصور الخاصة بك مع توقيع العقد التجاري أو حصرية مع مدير حسابات البريد .',
            tip7: '3 ، غير اليابانية ، البر الرئيسى للصين الشركات يمكن استخدام هذه الطريقة .',
            tip8: '4 - التحقق من التوقيع في غضون ثلاثة أيام عمل بعد تقديم الطلب .',
            comNum: 'رقم شهادة المؤسسة',
            plsEnterComNum: 'يرجى إدخال رقم هوية المؤسسة',
            plsUploadBuyRecord: 'يرجى تحميل مواد الإثبات',
            uaeTip1: 'الشركات المسجلة في دولة الإمارات العربية المتحدة يجب أن تكون مسجلة في الاسم الحقيقي uae.bestsign.com . . . . . . . عند التوقيع على العقود مع الشركات خارج دولة الإمارات العربية المتحدة ، يمكن استخدام " عبر الحدود توقيع " وظيفة لضمان أمن بيانات المستخدم ، لا تسرب ، فرضية كفاءة إنجاز العقود المتبادلة .',
            uaeTip2: 'إذا كان عملك قد وقعت بالفعل على النسخة الإماراتية من الانتهاء من شهادة الاسم الحقيقي ، يمكنك تسجيل الدخول مباشرة إلى uae.bestsign.com لتسهيل استخدام الخدمات ذات الصلة . وتجدر الإشارة إلى أن البيانات التي تم الحصول عليها من خلال التوقيع على النسخة الأجنبية منفصلة تماما عن النسخة الإماراتية .',
            uaeTip3: 'الشركات المسجلة خارج دولة الإمارات العربية المتحدة والبر الرئيسى للصين ، يجب أن تكون مسجلة في ent.bestsign.com الاسم الحقيقي . عند توقيع العقود مع الشركات في دولة الإمارات العربية المتحدة ، يمكن استخدام " التوقيع عبر الحدود " وظيفة لضمان أمن بيانات المستخدم ، لا تسرب ، فرضية كفاءة إنجاز العقود المتبادلة .',
        },
        enterEnterprise: 'الرجاء إدخال اسم الشركة',
        cancel: 'إلغاء',
        chooseEnterprise: 'اختر شركة',
        noResult: 'لا توجد نتائج',
        confirm: 'تأكيد',
        plzEnterRightEnt: 'الرجاء إدخال اسم الشركة الصحيح',
        plzEnterRightCorporate: 'الرجاء إدخال رقم الشركة الصحيح',
        corporateNumber: 'رقم تعريف الشركة',
        createSuccess: 'تم الإنشاء بنجاح',
        viewAllEnt: 'عرض جميع الشركات',
        addEnt: 'إضافة شركة جديدة',
        exit: 'تسجيل الخروج',
        userCenter: 'المشرف',
        groupConsole: 'لوحة تحكم المجموعة',
        backToHome: 'العودة إلى الرئيسية',
        connectToPrivateNetTip: 'تستخدم شركتك التخزين الخاص للعقود. الشبكة الحالية متصلة بخادم تخزين العقود.',
        noConnectToPrivateNetTip: 'تستخدم شركتك التخزين الخاص للعقود، ولا يمكن للشبكة الحالية الاتصال بخادم تخزين العقود',
        advice1: 'اقتراح: ',
        checkNetTip1: 'تحقق مما إذا كانت الشبكة الحالية يمكنها الوصول إلى الشبكة الداخلية للشركة',
        ecology: 'حساب شركة من {developerName}',
        ecologyPerson: 'حساب شخصي من {developerName}',
        hybridNetworkDetail: 'تفاصيل شبكة السحابة الهجينة',
        requestUrl: 'عنوان الطلب',
        statusCode: 'رمز الحالة',
        timeCost: 'الوقت المستغرق',
        hybridStatusCodeExplain: {
            Error: 'يرجى التحقق من حالة اتصال الشبكة الحالية',
            200: 'نجاح',
            403: 'عنوان الطلب غير متاح',
            404: 'عنوان الطلب غير موجود',
            409: 'استثناء الأعمال',
            500: 'يرجى التحقق مما إذا كان خادم تخزين العقود يعمل بشكل طبيعي',
        },
        switchConfirm: {
            title: 'تنبيه تبديل الكيان',
            main: 'يرجى التأكيد، هل تريد التبديل إلى الكيان الشخصي؟',
            tip: 'بعد التبديل إلى الكيان الشخصي، لن تتمكن من استخدام وظائف الشركة مثل تكوين القوالب وإدارة الشركة. سيتم أيضاً إرسال العقود باسمك الشخصي. إذا كنت بحاجة إلى ممارسة الأعمال التجارية، يرجى العودة إلى كيان الشركة.',
            confirm: 'عدم التبديل الآن',
            cancel: 'المتابعة للتبديل إلى الشخصي',
        },
    },
    commonFooter: {
        record: 'رقم تسجيل ICP الرئيسي: Zhejiang ICP No. 14031930',
        hubbleRecordId: 'سجل شبكة المعلومات: 330106973391501230011',
        openPlatform: 'المنصة المفتوحة',
        aboutBestSign: 'حول بست ساين',
        contact: 'اتصل بنا',
        recruitment: 'التوظيف',
        help: 'مركز المساعدة',
        copyright: 'حقوق النشر',
        company: 'شركة هانغتشو بست ساين المحدودة',
        ssqLogo: 'شعار بست ساين في الشريط السفلي',
        provideTip: 'خدمة التوقيع الإلكتروني مقدمة من',
        ssq: ' بست ساين',
        provide: '',
        signHotline: 'الخط الساخن للخدمة',
        langSwitch: 'اللغة',
    },
    footerSimpler: {
        ssqDes: 'الرائد في منصة التوقيع الإلكتروني السحابية',
    },
    mainChart: {
        sendVolume: 'عدد الإرسال',
        signFinishAmout: 'عدد التوقيعات المكتملة',
        contractSendAmout: 'عدد إرسال العقود',
        contractSignAmout: 'عدد توقيعات العقود',
        billion: '100 مليون',
        wan: '10 آلاف',
        fen: 'نسخة',
        ci: 'نسخة',
    },
    resetPassword: {
        safeTip: 'تحذير أمني!',
        weakPswTip: 'كلمة المرور الخاصة بك غير آمنة، هناك مخاطر أمنية محتملة. يرجى إعادة تعيين كلمة المرور!',
        oldPsw: 'كلمة المرور القديمة',
        plsInputOldPsw: 'الرجاء إدخال كلمة المرور القديمة.',
        newPsw: 'كلمة المرور الجديدة',
        pswRule: ' 6-18 رقماً، أحرف كبيرة وصغيرة، تدعم الرموز الخاصة الشائعة',
        confirm: 'تأكيد',
        oldPswNotEmpty: 'لا يمكن أن تكون كلمة المرور القديمة فارغة.',
        newPswNotEmpty: 'لا يمكن أن تكون كلمة المرور الجديدة فارغة.',
        modifySuccess: 'تم التعديل بنجاح',
    },
    passwordExpiration: {
        notice: 'لم يتم تغيير كلمة المرور الخاصة بك لمدة 90 يوماً. يرجى تغييرها في الوقت المناسب وفقاً لمتطلبات مؤسستك.',
        btn: 'انتقل للتغيير',
    },
    giftContract: {
        expiryDateUtil: 'فترة الاستخدام حتى ',
        buyTip: 'يوصى بإكمال التحقق من الاسم الحقيقي أولاً ثم شراء حزمة التوقيع الإلكتروني. الحساب الافتراضي هو حساب فردي.',
        personAuthTip: 'تهانينا، نجح التحقق من الهوية الشخصية.',
        entAuthTip: 'تهانينا، نجح التحقق من هوية الشركة.',
        openSsqTip: 'تهانينا على إكمال تسجيل حسابك.',
        registerTip1: 'عند إكمال إرسال العقد الأول، يمكن الحصول على نسخ عقود إضافية مجاناً.',
        registerTip2: ' نسخ مجانية إضافية',
        registerTip3: 'أكمل التحقق من الاسم الحقيقي للشركة للحصول على ',
        giftTip1: 'بست ساين تهديك ',
        giftTip2: ' نسخ من "عقود للأفراد"',
        and: ' و ',
        giftTip3: ' نسخ من "عقود للشركات"',
        sendContractTip: 'عند إكمال إرسال العقد الأول، يمكن الحصول على نسخ عقود إضافية مجاناً.',
        entAuth: 'انتقل إلى توثيق الشركة',
        personAuth: 'انتقل إلى التوثيق الشخصي',
    },
    noticeMap: {
        notice: 'الرسائل ',
        notRead: ' غير مقروءة)',
        allRead: 'قراءة الكل',
        viewMore: 'عرض المزيد',
    },
    rechargeOrder: {
        searchType: 'نوع البحث',
        deductTime: 'تاريخ الخصم',
        selectDateRange: 'حدد الفترة الزمنية:',
        till: 'إلى',
        startDate: 'تاريخ البداية',
        endDate: 'تاريخ النهاية',
        search: 'بحث',
        sendTime: 'وقت الإرسال',
        contractTitle: 'اسم العقد',
        contractId: 'معرف العقد',
        user: 'المستخدم',
        belongDepartment: 'القسم',
        contractType: 'نوع العقد',
        consumedCopies: 'كمية العقود المستهلكة',
        consumedCopiesTips: 'العدد النظري للعقود المطلوبة لهذه المعاملة.',
        personConsumedCopiesTips1: '1. The consumption record shall be based on the actual contract package category used. For example, if a private contract is sent but there are no available private contract packages at the time, and they are deducted from the corporate package, the consumption record will be displayed as one corporate contract.',
        personConsumedCopiesTips2: '2. Starting from April 21, 2025, ordinary individual users will no longer enjoy the discount of returning the number of contracts due to contract expiration, withdrawal, or refusal to sign.',
        exportMaxTip: 'لا يمكن أن تتجاوز بيانات التصدير 10000',
        export: 'تصدير',
        exportTip: 'وقت التطوير طويل جداً، يرجى عدم التردد في الاتصال بنا قبل الإغلاق. افتح نافذة المعالجة في وقت فراغك',
        batchCenter: 'مركز المهام المجمعة',
        batchExportTip: 'في التحليل في الخلفية، يرجى التحقق من نتائج هذه العملية في "مركز المهام المجمعة".',
    },
    rechargePackage: {
        manage: 'إدارة الشحن',
        selectPackage: 'اختر حزمة',
        tip1: 'أكثر من ￥40000 يرجى الاتصال بخدمة العملاء: 400-993-6665',
        onlineService: 'خدمة العملاء عبر الإنترنت: ',
        tip2: 'العرض ينتهي في 31 ديسمبر',
        ccbPublicContract: 'عقد شركة بنك البناء ',
        publicContract: 'عقد للشركة',
        copiesTip: 'نسخ العقود: {num}',
        vailidityPeriodTip: 'فترة الصلاحية: {month} شهر',
        ccbPrivateContract: 'عقد فردي لبنك البناء ',
        privateContract: 'عقد للفرد',
        copiesTip1: 'نسخ العقود:',
        giveCopiesTip: '+ {num}',
        buyNow: 'اشترِ الآن',
        advancedFeature: 'شراء الميزات المتقدمة',
        learnDetail: 'اعرف المزيد',
        isUsing: 'مفتوح',
        unUsing: 'غير مفتوح',
        payAmount: 'مبلغ الدفع',
        placeOrder: 'اشترِ الآن',
        renewal: 'شراء التجديد',
        year: 'سنة',
        limitBugTip: 'عرض خاص محدود لشراء واحد',
    },
    editFieldDialog: {
        chooseFieldType: 'اختر نوع الحقل:',
        fieldType: {
            text: 'نص',
            single: 'زر راديو',
            multi: 'خانة اختيار',
            date: 'تاريخ',
            combo: 'قائمة منسدلة',
            number: 'رقم',
            datetime: 'وقت',
        },
        name: 'الاسم',
        namePlace: 'الرجاء إدخال اسم',
        options: 'العناصر الاختيارية',
        addOptions: 'عناصر اختيارية جديدة',
        contentFiller: 'معبئ المحتوى',
        sender: 'المرسل',
        dateFormate: 'تنسيق التاريخ',
        senderContent: 'إذا اخترت المرسل، سيتم تعبئة محتوى هذا العقد من قبل المرسل قبل إرساله.',
        signer: 'الموقع',
        signerContent: 'إذا اخترت الموقع، سيتم تعبئة محتوى هذا العقد من قبل الموقع عند التوقيع.',
        fontSize: 'حجم الخط',
        fontSizePlace: 'الرجاء اختيار حجم الخط',
        requirements: 'متطلبات تعبئة المعلومات',
        required: 'مطلوب',
        confirm: 'تأكيد',
        cancel: 'إلغاء',
        addField: 'إضافة حقل',
        editField: 'تحرير حقل',
        integer: 'عدد صحيح',
        decimalLimit: 'حد',
        decimal: 'أرقام عشرية',
        errorMsg: {
            optionEmpty: 'لا يمكن أن يكون اسم العنصر الاختياري فارغاً',
            optionHasEnComma: 'لا يمكن أن يحتوي اسم العنصر الاختياري على فواصل إنجليزية',
            optionNameFormat: 'أسماء العناصر الاختيارية يمكن أن تكون فقط مزيجاً من الصينية والإنجليزية والأرقام',
            optionNameRepeat: 'لا يمكن أن تكون أسماء العناصر الاختيارية متطابقة',
            enterFieldName: 'الرجاء إدخال اسم الحقل',
            fieldNameExist: 'اسم الحقل موجود بالفعل. الرجاء إعادة التسمية',
            fieldNameExistToOn: 'اسم الحقل موجود بالفعل. الرجاء تمكين الحقل الذي يحمل نفس الاسم',
            optionsNeed: 'الرجاء إضافة خيارين على الأقل',
            overCountLimit: 'يدعم حتى 500 خيار',
        },
        editSuccess: 'تم التعديل بنجاح',
        addSuccess: 'تمت الإضافة بنجاح',
        labelAlign: 'المحاذاة',
    },
    functionSupportDialog: {
        title: 'تقديم الوظائف',
        inputTip: 'إذا كانت لديك احتياجات استخدام ذات صلة، يرجى ملء احتياجاتك في النموذج التالي. ستقوم بست ساين بترتيب المهنيين للاتصال بك وتقديم إرشادات الخدمة خلال 24 ساعة.',
        useSence: 'سيناريو التطبيق',
        useSenceTip: 'مثل الموارد البشرية، الموزعين، مستندات الخدمات اللوجستية، إلخ',
        estimatedOnlineTime: 'تاريخ الإطلاق المتوقع',
        requireContent: 'المتطلبات',
        requireContentTip: 'يرجى وصف كيف ستستخدم شركتك التوقيع الإلكتروني حتى نتمكن من تقديم الحل المناسب لك',
        getSupport: 'الحصول على دعم الخدمة المهنية',
        callServiceHotline: 'الخط الساخن: 400-993-6665',
        useSenceNotEmpty: 'لا يمكن أن يكون سيناريو الاستخدام فارغاً',
        requrieContentNotEmpty: 'لا يمكن أن يكون محتوى الطلب فارغاً',
        oneWeek: 'خلال أسبوع',
        oneMonth: 'خلال شهر',
        other: 'أخرى',
        submitSuccess: 'تم التقديم بنجاح',
        submitTrial: 'تقديم طلب تجربة',
        toTrial: 'للتجربة',
        trialTip: 'بعد تقديم طلب التجربة، سيتم تنشيط الوظيفة الحالية فوراً وستكون متاحة للتجربة. لمساعدتك بشكل أفضل في استخدام الميزات، يمكنك ملء المزيد من المتطلبات في النموذج أدناه. سيتصل بك مستشار بست ساين لتقديم الخدمات.',
        applyTrial: 'طلب تجربة',
        trialSuccTip: 'تم تنشيط الوظيفة. مرحباً بك في تجربتها',
        goBuy: 'اشترِ الآن',
        buyLater: 'الشراء لاحقاً',
        trialTipMap: {
            title: 'تعليمات التجربة',
            tip1: '1. استخدام فوري، صالح لمدة 7 أيام؛',
            tip2: '2. خلال فترة التجربة، لن يتم احتساب رسوم على الوظيفة؛',
            tip3: '3. لكل كيان شركة فرصة تجربة واحدة فقط لوظيفة معينة؛',
            tip4: '4. الشراء الذاتي متاح خلال فترة التجربة، والاستخدام غير منقطع؛',
            tip5: '5. إذا انتهت فترة تجربتك، يمكنك مسح الرمز والتواصل مع مستشار بست ساين المهني للحصول على التفاصيل:',
        },
        contactAdminTip: 'للاستخدام، يرجى الاتصال بمسؤول المؤسسة {tip} للشراء والفتح',
        trialEndTip: 'بعد فترة التجربة، انقر للشراء',
        trialRemainDayTip: 'بقي {day} أيام في فترة التجربة، انقر لشراء النسخ',
        trialEnd: 'انتهاء التجربة/انتهاء صلاحية الميزة',
        trialEndMap: {
            deactivateTip: 'تم تعطيل ميزة {feature}. يرجى مسح التكوين أو تجديده قبل متابعة الاستخدام.',
            feature1: 'مرفقات العقد',
            remove1: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن بيانات مرفقات العقد الإضافية المكونة وحذفها.',
            feature2: 'التعرف على الكتابة اليدوية',
            remove2: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن التعرف على الكتابة اليدوية المكونة وحذفها.',
            feature3: 'زخرفة العقد: ختم الركوب + العلامة المائية',
            remove3: 'طريقة مسح التكوين هي: تحرير القالب - البحث عن زخرفة العقد المكونة وحذفها.',
            feature4: 'الموافقة على إرسال العقد',
            remove4: 'طريقة مسح التكوين هي: وحدة تحكم المؤسسة - تعطيل جميع عمليات الموافقة',
            feature5: 'انتهت صلاحية وظيفة تفويض القالب وتم تعطيلها. تم تعطيل جميع مخططات تفويض القالب، وفقط المسؤول الرئيسي لديه إذن باستخدامها. يمكنك إعادة إنشاء القالب للاستخدام، أو إخطار المسؤول بالتجديد قبل متابعة الاستخدام.',
            remove5: '',
        },
    },
    RechargeAccount: {
        orders: {
            pay: 'دفع',
            money: 'المبلغ',
            monyUnit: 'دولار',
            main: 'كيان الشحن',
            chargeNow: 'دفع',
            payApproachType: 'بطاقة الخصم أو الائتمان',
            poweredBy: 'مدعوم من',
            // orderId: 'رقم الطلب',
            // contractNum: 'عدد العقود',
            // moneyWithUnit: 'المبلغ (يوان)',
            // actualMoney: 'مبلغ الدفع الفعلي',
            // date: 'تاريخ الاستحقاق',
            numberUnit: 'نسخ',
            // goPay: 'ادفع الآن',
            // dueDate: 'تاريخ انتهاء الصلاحية',
            // account: 'حساب الشحن',
            // unpaied: 'لم يتم الدفع',
            // paied: 'تم الدفع',
            // paymentTime: 'وقت الدفع',
            // paymentStatus: 'حالة الدفع',
            // payResultDialogTitle: 'معلومات الدفع',
            // payResultSuccess: 'تم الدفع بنجاح',
            // useNow: 'استخدام فوري',
            // limitedTime: 'استخدام مجاني محدود',
        },
    },
};
// ترجمة المكونات الأساسية
const components = {
    helperFloat: {
        advice: 'نصيحة',
        hide: 'إخفاء',
        suggest: 'اقتراحات',
        onlineService: 'خدمة العملاء عبر الإنترنت',
        minimize: 'تصغير',
        enlarge: 'تكبير',
        zoomOut: 'تصغير',
        person: 'فرد',
        ent: 'شركة',
        checkMoreVersion: 'عرض المزيد من الإصدارات',
        submitSuccess: 'تم الإبلاغ بنجاح',
        reportLog: 'تقرير',
    },
    countDown: {
        resendCode: 'إعادة الحصول',
        getVerifyCode: 'الحصول على رمز التحقق',
    },
    messageBox: {
        confirm: 'تأكيد',
    },
    example: {
        sideDisplay: 'عرض جانبي',
        clickPreview: 'انقر للمعاينة',
        previewTitle: 'معاينة العنوان',
    },
    sealInconformityDialog: {
        errorSeal: {
            title: 'تنبيه الختم',
            tip: 'تم الكشف عن أن صورة الختم الحالية لا تتطابق مع هوية شركتك، نتيجة التعرف على صورة الختم الحالية:',
            tip2: 'هل تريد الاستمرار في استخدام صورة الختم الحالية؟',
            guide: 'كيفية تحميل الختم الصحيح >>',
            next: 'متابعة الاستخدام',
            tip3: 'واسم ختمك لا يتوافق مع المواصفات، مع الكلمات "{keyWord}".',
            tip4: 'تم الكشف عن أن اسم الختم لا يتوافق مع المواصفات ويحتوي على الكلمات "{keyWord}". هل تريد الاستمرار في استخدامه؟',
        },
        exampleSeal: {
            title: 'طريقة تحميل نمط الختم',
            way1: ['الطريقة الأولى:', '1. ضع ختماً فعلياً على ورقة بيضاء', '2. التقط صورة وقم بتحميلها إلى المنصة'],
            way2: ['الطريقة الثانية:', 'استخدم مباشرة وظيفة إنشاء الختم الإلكتروني في المنصة، كما هو موضح:'],
            errorWay: ['طريقة خاطئة:', 'حمل الختم باليد', 'صور غير ذات صلة', 'رخصة العمل'],
        },
        confirm: 'تأكيد',
        cancel: 'إلغاء',
    },
};

// ترجمة المحتويات الأخرى في المكتبة العامة
const commonother = {
    validationMsg: {
        notEmpty: 'لا يمكن أن يكون فارغاً',
        enter6to18n: 'الرجاء إدخال 6-18 رقماً، أحرف كبيرة أو صغيرة',
        enter8to18n: 'الرجاء إدخال 8-18 رقماً، أحرف كبيرة أو صغيرة',
        errEmailOrTel: 'الرجاء إدخال البريد الإلكتروني أو رقم الهاتف الصحيح!',
        verCodeFormatErr: 'رمز التحقق غير صحيح',
        signPwdType: 'الرجاء إدخال 6 أرقام',
        enterActualEntName: 'الرجاء إدخال اسم الشركة الحقيقي',
        enterCorrectName: 'الرجاء إدخال الاسم الصحيح',
        enterCorrectPhoneNum: 'الرجاء إدخال رقم الهاتف المحمول الصحيح',
        enterCorrectEmail: 'الرجاء إدخال عنوان البريد الإلكتروني الصحيح',
        imgCodeErr: 'رمز التحقق المرئي غير صحيح',
        enterCorrectIdNum: 'الرجاء إدخال رقم الهوية الصحيح',
    },
    pubTips: {
        loginOverdue: 'انتهت صلاحية تسجيل الدخول، يرجى تسجيل الدخول مرة أخرى',
        errorTip: 'رسالة خطأ',
        know: 'فهمت',
        serverError: 'الخادم لديه فجوة صغيرة، يرجى المحاولة مرة أخرى لاحقاً.',
    },
    commonNomal: {
        yesterday: 'أمس',
        ssq: 'بست ساين',
        ssqPlatform: 'منصة بست ساين للتوقيع الإلكتروني السحابي',
        ssqTestPlatform: '(لأغراض الاختبار فقط) منصة بست ساين للتوقيع الإلكتروني السحابي',
        pageExpiredTip: 'انتهت صلاحية الصفحة، يرجى التحديث والمحاولة مرة أخرى',
        pswCodeSimpleTip: 'يجب أن تحتوي كلمة المرور على 6-18 رقماً وأحرف كبيرة وصغيرة، يرجى إعادة التعيين',
    },
    codeHandlerMap: {
        tip1: 'تم سحب حقك في استخدام القالب من قبل منشئ القالب',
        tip2: 'تم حذف هذا القالب من قبل المنشئ',
        tip3: 'تم تعطيل هذا القالب من قبل المنشئ ولا يمكن استخدامه بعد الآن',
        tip4: 'غير قادر على استخدام القالب',
        tip5: 'العودة إلى قائمة القوالب',
        errorTip: 'الخادم ارتفع قليلاً، يرجى المحاولة مرة أخرى لاحقاً',
        getNewJar: 'يرجى الاتصال ببست ساين لترقية حزمة jar',
        networkNotConnect: 'الشبكة غير متاحة، يرجى التحقق من إعدادات شبكتك.',
        notAccessPage: 'لا يمكن الوصول إلى هذه الصفحة',
        page404: 'الصفحة التي زرتها غير موجودة (404)',
        goHome: 'العودة إلى الرئيسية',
        downloadApp: 'تحميل تطبيق بست ساين',
        advancedFeatureError: 'هذه الميزة متاحة فقط لمستخدمي النسخة المتقدمة، يرجى الاتصال بـ BestSign للترقية',
    },
    catchMap: {
        view: 'عرض',
        download: 'تحميل',
        reject: 'رفض',
        revoke: 'إلغاء',
        delete: 'حذف',
        cantOperate: 'غير قادر على {operate} العقد',
        hybridNetHeader: 'يستخدم المرسل طريقة تخزين العقد الخاصة، ولكن لا يمكن للشبكة الحالية الاتصال بخادم تخزين عقود المرسل.',
        hybridNetMsg: 'أقترح عليك التحقق مما إذا كانت الشبكة متصلة بالشبكة الداخلية للمرسل والمحاولة مرة أخرى.',
        checkNet: 'يرجى التحقق مما إذا كانت الشبكة متصلة بالشبكة الداخلية.',
        hybridNotConnect: 'السبب: تستخدم شركتك تخزين العقود الخاص، ولكن لا يمكن للشبكة الحالية الاتصال بخادم تخزين العقود.',
        hybridSuggest: 'موصى به: (1) تحقق مما إذا كانت الشبكة طبيعية؛ (2) تحقق مما إذا كان خادم تخزين العقود يعمل بشكل طبيعي',
        goHomePage: 'العودة إلى الرئيسية',

    },
    // التحقق من الهوية قبل التوقيع باللغة اليابانية
    authBeforeSignJa: {
        plsConfirmIdentity: 'الرجاء تأكيد هويتك',
    },
    fontSizeMap: {
        one: '26',
        smallOne: '24',
        two: '22',
        smallTwo: '18',
        three: '16',
        smallThree: '15',
        four: '14',
        smallFour: '12',
        five: '10.5',
        smallFive: '9',
    },
    replaceEmpty: 'تم اكتشاف أن اسم الشركة/اسم الشخص الذي أدخلته يحتوي على مسافات، مما قد يؤثر على التوقيع. لقد قمنا بإزالة المسافة من أجلك.',
};

export default {
    ...businessComponents,
    ...components,
    ...commonother,
    lang: 'ar',
};
