import VueI18n from 'vue-i18n';
Vue.use(VueI18n);

import VueCookie from 'vue-cookie';
import { isServer } from '../plugins/cookie/index';
import { getQueryString } from '../utils/getQueryString.js';

// element-ui的多语言路径信息
const ELEMENT_LOCALE_PATH_MAP = {
    en: 'en',
    zh: 'zh-CN',
    ru: 'ru-RU',
    ja: 'ja',
    ar: 'ar',
};
// 用户语言偏好en 英文，zh中文，账号信息 > Cookie > 浏览器默认，账号信息在head-info获取后设置
export function getDefaultLang() {
    const queryLanguage = getQueryString('language', location.href); // 支持官网入口
    let lang = queryLanguage || VueCookie.get('language') || (navigator.language || navigator.browserLanguage || navigator.systemLanguage).toLowerCase().substr(0, 2); // 常规浏览器语言和IE浏览器;
    if (!lang || ['null', 'undefined'].includes(lang) || lang.includes('zh-')) { // CFD-22186 zh-CN、zh-TW、zh-HK、zh-SG等中文分支都认定为中文
        lang = 'zh';
    }
    if (!['en', 'zh', 'ru', 'ja', 'ar'].includes(lang)) {
        lang = 'en';
    }
    VueCookie.set('language', lang, { secure: isServer() ? true : null }); // 配置cookie内默认的语言偏好
    return lang;
}

const i18n = new VueI18n({
    locale: getDefaultLang(),
    messages: {},
})

export async function getLocale(language, path) {
    // 加载自定义字段
    const customLocalePromise = import(
        /* webpackChunkName: "locale-[request]" */ `./locale/${language}`
    );
    // 加载element-ui
    const elementUILocalePromise = import(
        /* webpackChunkName: "locale-[request]" */ `element-ui/lib/locale/lang/${ELEMENT_LOCALE_PATH_MAP[language]}`
    );
    // 加载项目里的多语言
    const projectLocalePromise = import(
        /* webpackChunkName: "locale-[request]" */ `src/lang/${path}`
    );
    const res = await Promise.all([customLocalePromise, elementUILocalePromise, projectLocalePromise]);
    return {
        ...res[0].default,
        ...res[1].default,
        ...res[2].default,
    };
}

export async function loadLanguageAsync({ language, path = '' } = {}) {
    const lang = language || getDefaultLang();
    const filePath = `${lang}${path}`;
    const messages = await getLocale(lang, filePath);
    i18n.setLocaleMessage(lang, messages);
    return i18n;
}

export default function lang() {
    return i18n;
}
