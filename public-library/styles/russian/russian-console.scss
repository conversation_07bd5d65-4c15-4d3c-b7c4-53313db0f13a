// 俄文版-企业控制台样式兼容
.russian-page .console-page, .en-page .console-page{
    // 隐藏功能
    .console_2_apv, .unAuth.console-slider-item:nth-child(2), .console_2_biz li:nth-of-type(n+2),.console-slider-item.unAuth:last-child, .ent-moreAuthWay, .certification-materials-infos, .certificate-info .certifing,  .certificate-info .certified, .passInfo .resetEntInfo-button{
        display: none;
    }

    .console-setting-container{
        .change-manager-pop .change-manager-step1,.change-manager-pop .change-manager-step2{
            .change-manager-account{
                margin-bottom: 10px;
            }
            .change-manager-step-line{
                width: 70%;
                .title-step1{
                    margin-left: -20px;
                    display: inline-block;
                    width: 110px;
                    text-align: left;
                }
                .title-step2{
                    margin-right: -10px;
                    float: right;
                    width: 120px;
                    text-align: right;
                }
            }
        }
    }

    .console-block-title-padding{
        .console-members-department-block{
            .console-content-absolute-top{
                .console-members-operation-line{
                    position: relative;
                    display: -webkit-box;
                    margin-bottom: 0;
                    width: 100%;
                    height: 50px;
                    padding-bottom: 20px;
                    overflow-x: scroll;
                    overflow-y: hidden;

                    .department-member-new-btn,.department-member-upload-btn{
                        display: -webkit-box;
                    }
                    button:nth-child(2){
                        display: none;
                    }
                }
            }
            .members-department-absolute-table{
                td:last-child .cell{
                    position: relative;
                }
                .emp-status{
                    position: absolute;
                    left: 20px;
                    top: 0;
                    line-height: 16px;
                }
                .opeartionSpan{
                    display: inline-block;
                    margin-left: 60px;
                    line-height: 25px;
                    text-align: left;
                }
            }
        }
    }

    .console-slider-item{
        .console-slider-item-title{
            position: relative;
            height: auto;
            min-height: 30px;
            i{
                position: absolute;
                left: 0;
                top: 6px;
            }
            span{
                margin-left: 28px;
                display: inline-block;
                line-height: 18px;
            }
        }
        li{
            position: relative;
            height: auto;
            min-height: 30px;
            padding-top: 5px;
            padding-bottom: 5px;
            line-height: 18px;

            span:nth-child(1){
                display: inline-block;
            }
            span:nth-child(2){
                position: absolute;
                right: 0;
                top: 0;
            }
        }
    }

    .console-members-pagination, .console-log-pagination{
        .el-select .el-input{
            min-width: 110px;
            width: auto;
        }
    }

    .console-roles-detail-block{
        .console-role-permissions-list li:nth-child(4){
            line-height: 12px;
        }
    }

    .primission-range-content{
        display: block;
    }

    .console-roles-block,.console-seals-block{
        padding-left: 10px;
        padding-right: 10px;
    }

    .certification-permission-notice-static .per-permission{
        height: 178px;
    }

    .certificate-info{
        height: 550px;
    }

    .OpenAutoSealDialog{
        .el-dialog{
            width: 516px;
        }
        .el-form-item__label{
            margin-right: 60px;
            white-space: nowrap;
        }
        .el-form-item:nth-of-type(n+2) .el-form-item__content{
            padding-left: 30px;
        }
        .verify-flex{
            margin-left: 30px;
            .count-down{
                width: 192px;
            }
        }
    }

    .append-new-seal-pop{
        .upload-seal-block{
            .el-button,.el-upload__input, & + .console-btn{
                height: 38px;;
            }
        }
        .upload-seal-block + .console-btn{
            vertical-align: top;
            white-space: normal;
        }
    }
    .edit-current-seal-pop{
        .edit-seal-block{
            .el-button,.el-upload__input, & + .console-btn{
                height: 38px;;
            }
        }
        .edit-seal-block + .console-btn{
            white-space: normal;
        }
        .slide-pop-body .el-radio{
            margin-right: 0;
        }
    }
}
