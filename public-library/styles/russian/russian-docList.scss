// 俄文版页面兼容样式入口文件
.russian-page .doc-page, .en-page .doc-page {
    .doc-slider__common {
        .third-party-item {
            line-height: initial;
        }
        .items-container {
            .el-submenu {
                .el-menu-item {
                    min-height: 30px;
                    height: auto;
                    span {
                        display: inline-block;
                        line-height: initial;
                        white-space: normal;
                    }
                    .operate_title {
                        width: calc(100% - 24px);
                        white-space: normal;
                        line-height: 15px;
                        em {
                            top: 50%;
                            right: -9px;
                            transform: translateY(-50%);
                        }
                    }
                    .folderOperateBlock {
                        display: none;
                        line-height: 30px;
                        .folderHiddenOperate {
                            left: -105px;
                        }
                    }
                    &:hover {
                        .folderOperateBlock {
                            display: inline-block;
                        }
                    }
                }
                &.enterpriseFolder-submenu {
                    .el-submenu__title {
                        line-height: 13px;
                        padding-top: 5px;
                        white-space: normal;
                    }
                }
            }
        }
        .switch-contract {
            /* 企业归档隐藏 */
            line-height: initial;
            padding-top: 13px;
            display: none;
        }
    }
    .doc-content {
        .doc-content-top {
            .doc-content-filter-line {
                .doc-dropdown-moreSearch {
                    width: 130px;
                }
                .doc-export-btn {
                    width: 82px;
                }
                .doc-column-config-btn {
                    width: 170px;
                }
            }
        }
        .doc-content-pagination {
            &.el-pagination .el-select .el-input {
                width: 130px;
            }
        }
        .doc-container-dialogs {
            .doc-dialog-exportContracts .el-dialog__body h4 {
                line-height: 18px;
                height: 48px;
                padding-top: 5px;
                .el-form-item .el-date-editor.el-input {
                    width: 200px;
                }
            }
            .doc-dialog-cancelContracts {
                .el-form {
                    .verify-flex {
                        .verify-input input {
                            height: 30px;
                        }
                        .count-down {
                            line-height: 12px;
                            height: 30px;
                            width: 106px;
                        }
                    }
                    .el-form-item {
                        .el-form-item__label {
                            padding-top: 5px;
                        }
                        .refuse-description {
                            p {
                                line-height: 18px;
                                padding-top: 10px;
                            }
                        }
                    }
                }

            }

        }
        .doc-content-table {
            .doc-cloumn-dropdown-btn {
                .el-button-group {
                    background: $--background-color-regular;
                }
                width: 140px;
                .el-button:first-child {
                    width: 112px;
                    border-right: none !important;
                }
                .el-dropdown__caret-button {
                    border-left: 1px solid $--border-color-base !important;
                }
                &:hover {
                    .el-button-group {
                        background: $--color-white;
                    }
                }
            }
        }
    }
}
.el-dropdown-menu.doc-cloumn-dropdown.en-btn, .el-dropdown-menu.doc-cloumn-dropdown.ru-btn {
    width: 138px;
}
.ru-datePicker, .en-datePick {
    .el-date-table td.today {
        font-size: 7px;
    }
}
.doc-dialog-dealContracts-sign.doc-dialog-dealContracts-ru {
    .el-dialog__header {
        .el-dialog__title {
            transform: translateY(-3px);
            display: inline-block;
            width: 260px;
        }
    }
    .doc-dialog-dealContracts-absolute-btns {
        left: 251px !important;
    }
}
.doc-dialog-dealContracts-approval.doc-dialog-dealContracts-ru {
    .el-dialog__header {
        .el-dialog__title {
            transform: translateY(-11px);
            display: inline-block;
            width: 260px;
        }
    }
    .doc-dialog-dealContracts-absolute-btns {
        left: 248px !important;
    }
}
.doc-dialog-dealContracts-undo.doc-dialog-dealContracts-ru {
    .el-dialog__header {
        .el-dialog__title {
            transform: translateY(-7px);
            display: inline-block;
            width: 260px;
        }
    }
    .doc-dialog-dealContracts-absolute-btns {
        left: 275px !important;
    }
}
