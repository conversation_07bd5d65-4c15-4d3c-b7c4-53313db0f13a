.en-page,
.russian-page {
// * {
    %hidden {
        display: none;
    }
    .common-header {
        .nav-list {
            .nav-item-draft,
            .nav-item-box,
            .cursor-point[data-id="tem"],
            .cursor-point[data-id="statistics"] {
                @extend %hidden;
            }
        }
        .right-content {
            .el-icon-ssq-shu<PERSON>,
            .hybridNetStatus,
            .el-icon-ssq-tongzhi,
            .icon-play,
            .el-icon-ssq-bangzhu {
                @extend %hidden;
            }
            .console-entrance-con {
                width: 270px;
            }
        }
    }
    .HelperFloat-cpn,
    .HelperFloat-cpn-hidden,
    .chooseTemplate,
    .info .buy,
    .banner,
    .setphone-tip-info,
    .oversea-tip-text,
    .ssq-home .bottom {
        @extend %hidden;
    }

    .register-footer {
        .open-platform,
        .contact,
        .copyright,
        .recruitment {
            @extend %hidden;
        }
    }
    .home-dialog-unverifyConfirm .el-dialog,
    .box-sizing-dialog .home-dialog-createCompany.el-dialog-bg .el-dialog {
        width: 1000px;
    }
}
