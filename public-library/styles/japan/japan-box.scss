// 旗舰版日文 - 档案＋样式兼容
.ja-page-box{
    .box-dashboard .box-dashboard__tabtitles{
        h2{
            padding: 30px 10px 20px 20px;
            .el-icon-ssq-shezhi{
                padding-left: 20px;
            }
        }
        .tab-title{
            padding-left: 12px;
        }
    }
    .collection-card{
        .archiveId-line{
            height: inherit;
        }
    }
    .archive-create__save{
        button {
            width: 140px;
        }
        .audit-btn{
            width: 180px;
        }
    }
}

// 日文版 - 档案＋样式兼容
.ja-page-version-box{
    .check-contact .check-contact__form-tip, .import-content .input-info, .invite-auth.section, .apply-data .ent-auth-data, .storage-footer,
    .other-info .other-info__auth, .ja-hidden{
        display: none;
    }
    .mini-share .mini-share__qrbox_container{
        border: 1px solid $btn-disabled-font-color;
    }
}