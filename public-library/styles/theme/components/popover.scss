@charset "UTF-8";
// 三角阴影处理用filter
.el-popper{
    box-shadow: none;
    filter: drop-shadow(0 0 4px rgba(0, 0, 0, 0.1));
    &[x-placement^=bottom], &[x-placement^=top] {
        .popper__arrow{
            [dir=rtl] & {
                &::after {
                    margin-left: 0;
                    margin-right: -10px;
                }
            }
        }
    }
}
.el-cascader__dropdown.el-popper,.el-dropdown-menu.el-popper,.el-select-dropdown.el-popper,.el-cascader-menus.el-popper,.el-picker-panel.el-popper,.el-time-panel.el-popper{
    &[x-placement^="bottom"] {
        margin-top: 2px;
    }
    &[x-placement^="top"] {
        margin-bottom: 0;
    }
    &[x-placement^="right"] {
        margin-left: 0;
    }
    &[x-placement^="left"] {
        margin-right: 0;
    }

    .popper__arrow{
        display: none;
    }
    box-shadow: 0 2px 4px 0 #EEEEEE;
    filter: none;
}


