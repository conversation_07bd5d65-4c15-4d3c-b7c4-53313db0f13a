
@charset "UTF-8";
/**
 * @desc tab 样式，修改简洁头部标签页的下划线高度
 * <AUTHOR>
 * @date 2020-05-27
 */

.el-tabs__nav-wrap:after{
    height: 1px;
}
.el-tabs__item{
    font-weight: normal;
}
.el-tabs__nav-wrap::after{
    background-color: $--border-color-lighter;
}
.el-tabs__nav {
    [dir="rtl"] & {
        float: right;
        .el-tabs__active-bar {
            display: none;
        }
        .is-active {
            border-bottom: 2px solid $--color-primary;
        }
    }
}
[dir=rtl] .el-tabs--top .el-tabs__item.is-top:last-child {
    padding-left: 0;
    padding-right: 20px;
}
