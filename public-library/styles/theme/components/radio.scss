@charset "UTF-8";
/**
 * @desc radio 样式，覆盖默认 radio 按钮样式
 * <AUTHOR>
 * @date 2020-04-08
 */
 /* 默认 radio button 颜色重置为白色 */

.el-radio-group{
    .el-radio-button__inner, .is-disabled .el-radio-button__inner{
        background: $--color-white;
    }
}
.el-radio__label{
    padding-left: 5px;
}
.el-radio__input .el-radio__inner{
    outline: none;
}
.el-radio:focus:not(.is-focus):not(:active):not(.is-disabled) .el-radio__inner{
    box-shadow: none;
}
.el-radio__inner::after{
    width: 5px;
    height: 5px;
}
[dir='rtl'] .el-radio {
    margin-right: 0;
    margin-left: 30px;
    .el-radio__label {
        padding-left: 0;
        padding-right: 5px;
    }
    &:last-child {
        margin-left: 0;
    }
}
