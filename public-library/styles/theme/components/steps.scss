@charset "UTF-8";
/**
 * @desc steps 样式
 * <AUTHOR>
 * @date 2020-06-27
 */

.el-steps{
    .el-step.is-horizontal {
        .is-process{
            .el-step__line{
                background-color: $--color-primary;
            }
        }
        .el-step__line{
            top:14px
        }
    }

    .el-step:last-child{
        .el-step__icon-inner{
            position: relative;
            color: $--color-white;
            font-family: 'iconfont' !important;

            &::after{
                position: absolute;
                left: -2px;
                top: 0;
                color: $--color-text-placeholder;
                font-size: $--font-size-base;
                content: '\e671';
            }
        }
    }

    .el-step__head.is-finish{
        .el-step__icon{
            color: $--color-white;
            border-color: $--border-color-base;
            background: $--border-color-base;
        }
        .el-step__line-inner{
            border-color: $--border-color-base;
        }
    }

    .el-step__title.is-finish, .el-step__description.is-finish{
        color: $--color-text-placeholder;
    }

    .el-step__head.is-process{
        .el-step__icon{
            color: $--color-white;
            border-color: $--color-primary;
            background: $--color-primary;
        }
        .el-step__line-inner{
            border-color: $--color-primary;
        }
    }

    .el-step__title.is-process, .el-step__description.is-process{
        color: $--color-primary;
    }

    .el-step__head.is-wait{
        .el-step__icon{
            background: $--color-white;
        }
    }

    .el-step__icon{
        width: 30px;
        height: 30px;
        color: $--color-text-placeholder;
        border-color: $--border-color-light;
    }

    .el-step__title{
        font-size: $--font-size-base;
        line-height: 30px;
    }

    .el-step__title.is-wait, .el-step__description.is-wait{
        color: $--color-text-secondary;
    }

    .el-step__icon-inner, .el-step__title.is-process{
        font-weight: normal;
    }
    [dir=rtl] & {
        .el-step.is-center .el-step__line {
            left: -50%;
            right: 50%;
        }
    }
}

