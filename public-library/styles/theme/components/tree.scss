@charset "UTF-8";
/**
 * @desc tree 样式
 * <AUTHOR>
 * @date 2020-06-22
 */
.el-tree {
    overflow-x: auto;
    overflow-y: hidden;

    .el-tree-node>.el-tree-node__children {
        overflow: visible;
    }

    .el-tree-node__content > label.el-checkbox{
        margin-left: 4px;
        margin-right: 6px;
    }

    .el-tree-node__label {
        margin-left: 4px;
        font-size: $--font-size-base;
        overflow: visible;
    }

    .el-tree-node__expand-icon.is-leaf {
        color: rgba(0,0,0,0);
        cursor: default;
    }
    .el-tree-node__expand-icon{
        font-size: 14px;
    }
}