
@charset "UTF-8";
/**
 * @desc select 样式，覆盖 hover 样式、多选 tag 样式
 * <AUTHOR>
 * @date 2020-04-08
 */
div.el-select{
    .el-input .el-select__caret{
        font-weight: bold;
        color: $--border-color-base;
        font-size: $--font-size-extra-small;
        &.el-icon-arrow-up::before{
            content: "\e78f";
        }
    }
    .el-input.is-disabled .el-input__inner:hover{
        background: $--background-color-regular;
        border-color: $--border-color-base;
        box-shadow: none;
    }
    .el-tag.el-tag--info{
        height: 20px;
        line-height: 20px;
        background: $--color-primary-light-9;
        border: none;
        font-size: $--font-size-extra-small;
        color: $--color-text-primary;
    }
    .el-tag__close.el-icon-close{
        right: -5px;
        font-size: $--font-size-medium;
        background-color: transparent;
        color: $--color-text-placeholder;
        [dir=rtl] & {
            right: auto;
            left: -5px;
        }
        &:hover{
            background-color: transparent;
            color: $--color-text-secondary;
        }
    }
}

/* 选中选项的字体不加粗 */
.el-select-dropdown {
    .el-select-dropdown__item.selected{
        font-weight: normal;
    }
    &.el-popper[x-placement^="bottom"] .popper__arrow{
        border-bottom-color: $--border-color-lighter;
    }
    &.el-popper[x-placement^="top"] .popper__arrow{
        border-top-color: $--border-color-lighter;
    }
}
