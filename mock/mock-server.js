const chokidar = require('chokidar');
const bodyParser = require('body-parser');
const path = require('path');

const mockDir = path.join(process.cwd(), 'mock');

function registerMocks(app) {
    let lastI;
    const mocks = require('./index.js');
    for (const mock of mocks) {
        const { type, url, response } = mock;
        app[type](url, response);
        lastI = app._router.stack.length;
    }
    const mockLen = Object.keys(mocks).length;
    const startI = lastI - mockLen;
    return { mockLen, startI };
}

function unregisterMocks() {
    Object.keys(require.cache).forEach(i => {
        if (i.includes(mockDir)) {
            delete require.cache[require.resolve(i)];
        }
    });
}

function mockMiddleware(app) {
    require('@babel/register');
    app.use(bodyParser.json()); // application/json
    app.use(bodyParser.urlencoded({ extended: true })); // application/x-www-form-urlencoded

    const mocks = registerMocks(app);
    let { mockLen, startI } = mocks;

    chokidar
        .watch(mockDir, {
            ignored: /mock-server/,
            ignoreInitial: true,
        })
        .on('all', (event, path) => {
            if (event === 'change' || event === 'add') {
                try {
                    app._router.stack.splice(startI, mockLen);
                    unregisterMocks();
                    const mocks = registerMocks(app);
                    mockLen = mocks.mockLen;
                    startI = mocks.startI;
                } catch (error) {
                    console.log(error);
                }
            }
        });
}

module.exports = mockMiddleware;
