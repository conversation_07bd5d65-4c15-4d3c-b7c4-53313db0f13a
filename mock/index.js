const Mock = require('mockjs');

const cert = require('./cert');

let mocks = [
    ...cert,
];

mocks = mocks.map(mock => {
    const { url, type = 'get', response } = mock;
    return {
        url: new RegExp(`/mock${url}`),
        type,
        response(req, res) {
            res.json(Mock.mock(response instanceof Function ? response(req, res) : response));
        },
    };
});

module.exports = mocks;
