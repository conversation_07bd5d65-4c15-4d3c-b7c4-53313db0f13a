export const hubbleWps = {
    methods: {
        handleWpsData(totalResponse) {
            const revisionContentsMatch = totalResponse.match(/<revisionContents>([\s\S]*?)<\/revisionContents>/);
            if (revisionContentsMatch && revisionContentsMatch[1].trim()) {
                const revisionContents = revisionContentsMatch[1];
                return this.handleWpsSuggest(revisionContents);
            }
            return { originalContent: '', proposalContent: '' };
        },
        handleWpsSuggest(revisionContents) {
            let originalContent = '';
            let proposalContent = '';
            const originalMatch = revisionContents.match(/<original>([\s\S]*?)<\/original>/);
            const proposalMatch = revisionContents.match(/<proposal>([\s\S]*?)<\/proposal>/);
            if (originalMatch) {
                originalContent = this.formatString(originalMatch[1]); // 获取 <original> 标签内的内容并去除多余空白
            }
            if (proposalMatch) {
                proposalContent = this.formatString(proposalMatch[1]); // 获取 <proposal> 标签内的内容并去除多余空白
            }
            return { originalContent, proposalContent };
        },
    },
};
