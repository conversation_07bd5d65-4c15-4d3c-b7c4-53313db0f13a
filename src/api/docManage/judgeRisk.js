/*
*   hubble工具，风险判断单个记录详情
*/
export function hubbleJudgementDetail(analysisId) {
    return Vue.$http.get(`/web/hubble/agreement-analysis/${analysisId}/analysis-record-detail`);
}

/*
*   hubble工具，确定当前操作类型
*/
export function confirmOperateType(analysisId, agreementAnalysisType) {
    return Vue.$http.post(`/web/hubble/agreement-analysis/${analysisId}/confirm-analysis-type`, {
        agreementAnalysisType,
    });
}

/*
*   初始化获取analysisId
*/
export function initialAnalysis(contractId, receiverId, data) {
    return Vue.$http.post(`/contract-api/contract/${contractId}/receiver/${receiverId}/init-ai-analysis`, data);
}

/*
*   查询文档记录
*/
export function acquireDocument(contractId, receiverId) {
    return Vue.$http.get(`/contract-api/contract/${contractId}/receiver/${receiverId}/acquire-document-list`);
}

/**
 * 跳转微信小程序生成跳转链接接口
 * @param sourceType String 小程序类型 签署小程序-101
 * @param path String 小程序页面路径
 * @param query Object 小程序页面参数
 * @param envVersion String 需要跳转的小程序版本  正式版-"release"，体验版-"trial"，开发版-"develop"
 */
export function fetchAppletUrl(sourceType, path, query, envVersion = 'release') {
    return Vue.$http.post('/users/ignore/applets/url-scheme', { sourceType, path, query, envVersion });
}

