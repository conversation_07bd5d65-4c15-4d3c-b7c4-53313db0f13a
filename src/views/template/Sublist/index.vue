<template>
    <!-- 头底部布局 -->
    <Layout pageModule="tmps" class="need-base-width-page">
        <div class="template-sublist">
            <div class="template-sublist_content">
                <el-breadcrumb separator-class="el-icon-arrow-right">
                    <el-breadcrumb-item :to="{ path: '/template/list' }">{{ $t('templateCommon.tempMgmt') }}</el-breadcrumb-item>
                    <el-breadcrumb-item>{{ templateName }}</el-breadcrumb-item>
                </el-breadcrumb>
                <p class="template-sublist_name">{{ templateName }}</p>

                <el-tabs v-model="activeTab"
                    @tab-click="handleClick"
                    :before-leave="handleBeforeLeave"
                    class="template-sublist_content_tabs"
                >
                    <el-tab-pane :label="$t('templateSubList.tempDocument')" name="document">
                        <SearchBox
                            :fieldList="documentSearchField"
                            @search="handleSearch"
                            ref="documentSearch"
                        />
                        <Document :tableData="documentTableData"
                            @update="updateDocument"
                            class="template-sublist_content_tabs_table"
                        />
                    </el-tab-pane>

                    <el-tab-pane name="federation">
                        <div slot="label">
                            <CommonTip placement="top-start" manual v-model="visible" popper-class="template-sublist_content_tabs--tooltip">
                                <div slot="content">
                                    {{ $t('templateSubList.tempFederationTip') }}
                                </div>
                                <div slot="reference">{{ $t('templateSubList.tempFederation') }}</div>
                            </CommonTip>
                        </div>

                        <SearchBox
                            :fieldList="federationSearchField"
                            @search="handleSearch"
                            ref="federationSearch"
                        />
                        <Federation :tableData="federationTableData"
                            @update="updateFederation"
                            class="template-sublist_content_tabs_table"
                        />
                    </el-tab-pane>
                    <el-tab-pane v-if="showSpecialSeal"
                        name="specialSeal"
                        :label="$t('templateSubList.templateSpecialSeal')"
                    >
                        <SpecialSeal :sealConfigInfo="sealConfigInfo" :templateName="templateName" class="template-sublist_content_tabs_table"></SpecialSeal>
                    </el-tab-pane>
                    <el-tab-pane v-if="showSceneConfig"
                        name="sceneConfig"
                        :label="$t('templateSubList.tempSceneConfig')"
                    >
                        <SceneConfig :sceneData="sceneConfigData" :templateId="templateId" :templateName="templateName"></SceneConfig>
                    </el-tab-pane>
                    <el-tab-pane
                        v-if="showInvalidStatement"
                        :label="$t('templateSubList.tempInvalidStatement')"
                        name="invalidStatement"
                    >
                        <InvalidStatement :invalidStatementData.sync="invalidStatementData" :relationableTemplateId.sync="relationableTemplateId" :templateName="templateName"></InvalidStatement>
                    </el-tab-pane>
                    <el-tab-pane
                        v-if="showSupplementsAgree"
                        :label="$t('templateSubList.tempSupplement')"
                        name="supplement"
                    >
                        <Supplement :tableData="supplementData"
                            :templateName="templateName"
                            class="template-sublist_content_tabs_table"
                        ></Supplement>
                    </el-tab-pane>
                    <el-tab-pane
                        v-if="showContractConfidentiality"
                        :label="$t('templateSubList.contractConfidentiality.name')"
                        name="contractConfidentiality"
                    >
                        <ContractConfidentiality :configData="contractConfidentialityData"
                            :templateId="templateId"
                            class="template-sublist_content_tabs_table"
                        ></ContractConfidentiality>
                    </el-tab-pane>
                    <el-tab-pane
                        name="aiAgent"
                        label="AI智能体"
                        v-if="templatePermissions.stampRecommend"
                    >
                        <AIAgent
                            :templateId="templateId"
                            :templateName="templateName"
                            class="template-sublist_content_tabs_table"
                        ></AIAgent>
                    </el-tab-pane>
                </el-tabs>
            </div>
        </div>
    </Layout>
</template>

<script>
import Layout from 'components/layout/index.vue';
import SearchBox from 'components/searchBox/index.vue';
import Document from './Document/index.vue';
import Federation from './Federation/index.vue';
import SceneConfig from './SceneConfig/index.vue';
import InvalidStatement from './InvalidStatement/index.vue';
import SpecialSeal from './SpecialSeal/index.vue';
import Supplement from './Supplement';
import ContractConfidentiality from './ContractConfidentiality';
import AIAgent from './AIAgent/index.vue';
import i18n from 'src/lang';
import { getTemplateSceneConfig, httpGetTemplateDocumentList, httpGetTemplateSupplement, getTemplateContractConfidentiality } from
    'src/api/template/detail';
import { getTemplateSpecialSealConfig } from 'src/api/template/specialSeal';
import { mapGetters, mapState, mapActions, mapMutations } from 'vuex';

// 文档搜索常量
const DOCUMENT_SEARCH_FIELD = [
    {
        label: i18n.t('templateSubList.documentName'),
        key: 'documentName',
    },
];

// 文档组合搜索常量
const FEDERATION_SEARCH_FIELD = [
    {
        label: i18n.t('templateSubList.federationName'),
        key: 'documentFederationName',
    },
    {
        label: i18n.t('templateSubList.federationId'),
        key: 'documentFederationId',
    },
    {
        label: i18n.t('templateCommon.mark'),
        key: 'documentFederationRemark',
    },
];

export default {
    components: {
        Layout,
        SearchBox,
        Document,
        Federation,
        SceneConfig,
        InvalidStatement,
        SpecialSeal,
        Supplement,
        ContractConfidentiality,
        AIAgent,
    },
    data() {
        return {
            activeTab: this.$route.params.activeTab || 'document',
            documentSearchField: DOCUMENT_SEARCH_FIELD,
            federationSearchField: FEDERATION_SEARCH_FIELD,
            templateName: this.$route.query.templateName,
            templateId: this.$route.params.templateId,
            documentTableData: [],
            federationTableData: [],
            sceneConfigData: [],
            sealConfigInfo: [],
            supplementData: [],
            contractConfidentialityData: {},
            invalidStatementData: null,
            visible: true, // 3s后tooltip提示消失
            relationable: false,
            relationDocumentId: '',
            relationableTemplateId: '',
            isDynamic: this.$route.query.isDynamic === 'true',
            enterTime: 0,
            aiAgentConfig: {},
        };
    },
    computed: {
        ...mapState(['features']),
        ...mapGetters(['checkFeat', 'getUserId', 'getIsForeignVersion']),
        ...mapState('template', {
            templatePermissions: state => state.templatePermissions,
        }),
        showSceneConfig() {
            // 需要乐高城配置
            return this.checkFeat.sceneConfig && this.templatePermissions.editCustomScene;
        },
        showSpecialSeal() {
            // 需要乐高城配置且具有模板专用章权限
            return this.checkFeat.specialSeal && this.templatePermissions.templateSpecialSeal;
        },
        showInvalidStatement() {
            return this.checkFeat.invalidStatementOperation && this.templatePermissions.invalidStatementOperation && !this.isOfdTemplate;
        },
        showSupplementsAgree() {
            // 需要乐高城配置及模板补充协议权限
            return this.checkFeat.templateSupplement && this.templatePermissions.editSupplyAgree;
        },
        showContractConfidentiality() {
            // 需要乐高城配置及模板合同保密
            return this.checkFeat.contractConfidentiality && this.templatePermissions.contractConfidentiality;
        },
        isOfdTemplate() {
            return this.$route.query.isOfdTemplate === 'true';
        },
    },
    methods: {
        ...mapMutations('template', ['setTemplateStatus']),
        ...mapActions('template', ['getTemplatePermission']),
        handleBeforeLeave(activeName) {
            if (activeName === 'invalidStatement' && !this.relationable) {
                this.$MessageToast.info(this.$t('templateSubList.invalidStatementTabTip'));
                return false;
            }
        },
        handleClick() {
            this.$sensors.track({
                eventName: 'Ent_TemplateManageDetail_BtnClick',
                eventProperty: {
                    template_id: this.templateId,
                    template_name: this.templateName,
                    template_type: this.isDynamic ? '动态模板' : '静态模板',
                    icon_name: ({
                        document: '模板文档',
                        federation: '模板文档组合',
                        sceneConfig: '场景定制',
                        specialSeal: '模板专用章',
                        invalidStatement: '作废申明',
                        supplement: '补充协议',
                        contractConfidentiality: '合同保密',
                        aiAgent: 'AI智能体',
                    })[this.activeTab],
                },
            });
            this.handleSearch();
        },
        handleSearch() {
            switch (this.activeTab) {
                case 'document': // 文档列表
                    this.queryDocument();
                    break;
                case 'federation': // 组合列表
                    this.queryFederation();
                    break;
                case 'sceneConfig': // 场景定制
                    this.queryScene();
                    break;
                case 'specialSeal': // 专用章
                    this.querySpecialSealConfig();
                    break;
                case 'invalidStatement':// 作废申明
                    this.queryInvalidStatement();
                    break;
                case 'supplement': // 补充协议
                    this.querySupplement();
                    break;
                case 'contractConfidentiality': // 合同保密
                    this.queryContractConfidentiality();
                    break;
                case 'aiAgent': // AI智能体
                    this.queryAIAgentConfig();
                    break;
                default:
                    break;
            }
        },
        queryAIAgentConfig() {
            // 获取AI智能体配置
            // 这里可以添加获取AI智能体配置的API调用
            // 例如：
            // this.$http.get(`/template-api/v2/templates/${this.templateId}/ai-agent-config`)
            //     .then(res => {
            //         this.aiAgentConfig = res.data || {};
            //     });
        },
        queryContractConfidentiality() {
            getTemplateContractConfidentiality(this.templateId)
                .then((res) => {
                    this.contractConfidentialityData = res.data || {
                        newHolderAccountForSendContract: '',
                        newHolderAccountForApproveContract: '',
                        newHolderAccountForSignContract: '',
                        newHolderAccountForSupplement: '',
                        hiddenContract: false,
                    };
                });
        },
        queryScene() {
            // 场景定制中 功能 和featureId 映射
            const MODULE_ID_MAP = {
                'AUTO_SIGN': '131', // boss对应名称：签约方自动签管控
                'HIDE_REFUSE_SIGN': '132', // 签署页隐藏项控制
                'PAPER_SIGNATURE': '133', // 纸质签署
                'DISPLAYED_CONTRACT_TEXT_ALIAS': '134', // boss对应名称：专用场景文件名自定义
                'SIGN_PROGRESS_FOLLOWER': '168', // boss对应：签署进度关注人
                'INNER_RESOLUTION': '182', // 内部决议
                'FDA_SIGNATURE': '183', // FDA
            };
            // 获取场景定制配置
            getTemplateSceneConfig(this.templateId)
                .then(res => {
                    this.sceneConfigData = res.data.scenes.filter(({ sceneType }) => {
                        if (sceneType === 'CONTRACT_CONFIDENTIALITY') {
                            return false;
                        }
                        if (this.isOfdTemplate) {
                            return sceneType === 'MANAGE_VERIFY_CODE';
                        }
                        // 动态模板不支持规范业务字段
                        if (this.$route.query.isDynamic === 'true' && sceneType === 'NORMALIZE_BIZ_FIELD') {
                            return false;
                        }
                        if (MODULE_ID_MAP[sceneType]) {
                            return this.features.includes(MODULE_ID_MAP[sceneType]);
                        }
                        return true;
                    });
                    if (this.getIsForeignVersion) {
                        this.sceneConfigData = this.sceneConfigData.filter(({ sceneType }) => {
                            return !['MANAGE_VERIFY_CODE', 'FDA_SIGNATURE', 'OTHER_SIGNATURE_SUPPORT', 'VALID_PDF_FILE_FOR_CUSTOMS'].includes(sceneType);
                        });
                    }
                });
        },
        querySpecialSealConfig() {
            getTemplateSpecialSealConfig(this.templateId)
                .then((res) => {
                    this.sealConfigInfo = res.data.roleSpecialSealConfig;
                });
        },
        queryDocument() {
            const params = this.$refs.documentSearch.form;
            httpGetTemplateDocumentList(this.templateId, params).then(({ data }) => {
                const { templateDocuments, templateName } = data;
                this.documentTableData = templateDocuments;
                this.templateName = templateName;
            });
        },
        querySupplement() {
            httpGetTemplateSupplement(this.templateId).then(({ data }) => {
                this.supplementData = data;
            });
        },
        updateDocument(documentIds) {
            this.documentTableData = this.documentTableData.filter(item => !documentIds.includes(item.documentId));
            // 文档被删完了，则跳转模版列表页
            if (this.documentTableData.length === 0) {
                this.$router.push('/template/list');
            }
        },
        queryFederation() {
            const params = this.$refs.federationSearch.form;
            this.$http.post(`/template-api/v2/templates/${this.templateId}/document-federation-filter`, params).then(({ data }) => {
                this.federationTableData = data;
            });
        },
        updateFederation(index) {
            this.federationTableData.splice(index, 1);
        },
        queryInvalidStatement() {
            // 获取作废申明
            this.$http.get(`/template-api/v2/draft/find-template/relation/${this.templateId}?bizType=VOID_CONTRACT`).then(({ data }) => {
                if (data.code === '140001') {
                    this.relationDocumentId = data.result.documents && data.result.documents[0].documentId;
                    this.relationableTemplateId = data.result.templateId;
                    if (this.relationDocumentId) {
                        this.getDocument();
                    }
                }
            });
        },
        getDocument() {
            this.$http.post(`template-api/v2/templates/${this.relationableTemplateId}/template-detail/${this.relationDocumentId}`).then(({ data }) => {
                this.invalidStatementData = data.documentViewDetails[0];
            });
        },
        // 判断是否可以切换到作废tab
        getRelationable() {
            this.$http.get(`/template-api/v2/templates/${this.templateId}/relationable`).then(({ data }) => {
                this.relationable = data.result;
            });
        },
        async initData() {
            this.handleSearch();
            setTimeout(() => {
                this.visible = false;
                this.$sensors.track({
                    eventName: 'Ent_TemplateManageDetail_PageView',
                    eventProperty: {
                        template_id: this.templateId,
                        template_name: this.templateName,
                        template_type: this.isDynamic ? '动态模板' : '静态模板',
                    },
                });
            }, 3000);
            // 更新模版设置的状态
            this.setTemplateStatus('edit');
            await this.getTemplatePermission(this.templateId);
            this.getRelationable();
        },
    },
    mounted() {
        this.enterTime = new Date().getTime();
        this.initData();
    },
    beforeDestroy() {
        this.$sensors.track({
            eventName: 'Ent_TemplateManageDetail_PageLeave',
            eventProperty: {
                template_id: this.templateId,
                template_name: this.templateName,
                template_type: this.isDynamic ? '动态模板' : '静态模板',
                $event_duration: (new Date().getTime() - this.enterTime) / 1000,
            },
        });
    },
};
</script>

<style lang="scss">
    .template-sublist {
        position: relative;
        @include base-width;
        height: 100%;
        margin: 0 auto;
        box-shadow: 0px 2px 5px 1px $--border-color-base;
        background: $--background-color-regular;

        .template-sublist_content {
            padding: 30px;
            background-color: $--color-white;
            height: 100%;
            box-sizing: border-box;

            .template-sublist_name {
                height: 30px;
                margin: 30px 0;
                font-size: 24px;
                line-height: 30px;
                color: $--color-text-primary;
            }

            .template-sublist_content_tabs {
                height: calc(100% - 74px);
                .el-tabs__nav-wrap {
                    overflow: visible;
                    .el-tabs__item.is-active > div:focus {
                        outline: none;
                    }

                    .el-tabs__item:focus-visible {
                        outline: none;
                    }
                }
                .el-tabs__content {
                    height: calc(100% - 50px);
                    overflow-y: auto;
                }

                .el-tabs__nav-scroll {
                    overflow: visible;
                    &::after {
                        content: "";
                        height: 0;
                        line-height: 0;
                        display: block;
                        visibility: hidden;
                        clear: both;
                    }
                }

                .template-sublist_content_tabs_table {
                    height: calc(100vh - 380px);

                    .ssq-table--operation{
                        line-height: 47px;

                        .el-button{
                            vertical-align: middle;
                        }
                    }
                }
                .template-sublist_content_tabs_table.temp-special-seal {
                    height: calc(100vh - 290px);
                }
            }

            [dir="rtl"] & {
                .el-tabs__content .el-table .el-table__cell {
                    text-align: right;
                }
            }
        }
    }

    .template-sublist_content_tabs--tooltip.el-tooltip__popper[x-placement^=top] {
        margin-bottom: -2px;
    }
</style>
