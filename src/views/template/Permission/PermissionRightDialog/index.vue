<template>
    <el-dialog class="permission-right-cpn" width="800px" :visible.sync="dialogVisible">
        <div class="permission-right-container">
            {{ $t('templatePermission.search.account') }}: {{ searchAccount }}
            <el-table
                :data="rightSource"
            >
                <el-table-column :label="$t('permissionRight.dialog.rightName')" prop="entName">
                    <template slot-scope="scope">
                        {{ permissions[scope.row.name] }}
                    </template>
                </el-table-column>
                <el-table-column :label="$t('permissionRight.dialog.recycleRight')" width="300">
                    <template slot-scope="scope">
                        <span v-if="scope.row.toAccount">• {{ $t('permissionRight.dialog.recycleByAccount', { account:scope.row.toAccount }) }}</span><br />
                        <span v-if="scope.row.toRole">• {{ $t('permissionRight.dialog.recycleByRole', { role: scope.row.toRole }) }}</span>
                    </template>
                </el-table-column>
                <el-table-column :label="$t('permissionRight.dialog.moreTip')" width="260">
                    <template slot-scope="scope">
                        <template v-if="scope.row.name === 'sendContract'">
                            {{ $t('permissionRight.dialog.sendTip1') }}<br />
                            <el-tooltip class="item" effect="dark" :content="scope.row.templateAuth.join(',')" placement="left">
                                <p v-if="scope.row.templateAuth.length>0">• {{ $t('permissionRight.dialog.sendTip2', { ents: handleEnts(scope.row.templateAuth)} ) }}
                                    <span v-if="scope.row.templateAuth.length>5">{{ $t('permissionRight.dialog.more',{ count: scope.row.templateAuth.length}) }}</span>
                                </p>
                            </el-tooltip>
                            <el-tooltip class="item" effect="dark" :content="scope.row.adminAuth.join(',')" placement="left">
                                <p v-if="scope.row.adminAuth.length>0">• {{ $t('permissionRight.dialog.sendTip3',{ ents: handleEnts(scope.row.adminAuth) }) }}
                                    <span v-if="scope.row.adminAuth.length>5">{{ $t('permissionRight.dialog.more',{ count: scope.row.adminAuth.length}) }}</span>
                                </p>
                            </el-tooltip>
                        </template>
                        <template v-else-if="scope.row.name === 'grantManage'">
                            {{ $t('permissionRight.dialog.grantManage') }}
                        </template>

                    </template>
                </el-table-column>
            </el-table>
        </div>
    </el-dialog>
</template>
<script>
import { PERMISSION_MAP } from 'const/const';
export default {
    props: {
        visible: {
            default: false,
            type: Boolean,
        },
        rightSource: {
            default: () => [],
            type: Array,
        },
        searchAccount: {
            default: '',
            type: String,
        },
    },
    data() {
        return {
            permissions: PERMISSION_MAP,
        };
    },
    computed: {
        dialogVisible: {
            set(value) {
                this.$emit('update:visible', value);
            },
            get() {
                return this.visible;
            },
        },
    },
    methods: {
        handleEnts(list) {
            const entsList = list.slice(0, 5);
            return entsList.join(',');
        },
    },
};
</script>
<style lang="scss">
    .permission-right-cpn .el-dialog__header{
        display: none;
    }
    .permission-right-container{
        .el-table{
            margin-top:10px;
            border: 1px solid  $text-color-lighter;
            .cell{
                font-size: 12px;
                padding-left: 15px;
            }
            thead th {
                background: $--background-color-regular;
                background-clip: padding-box;
                height: 50px;
                font-size: 14px;
                font-weight: normal;
                color: $--color-black;
                .cell {
                    padding-left: 15px;
                }
                &.is-leaf{
                    border-bottom: none;
                }
            }
        }
    }
</style>
