<template>
    <div class="auto-stamp-component" v-if="shouldShowAutoStamp">
        <!-- 自动拖章按钮 -->
        <el-button
            type="primary"
            @click="handleAutoStamp"
        >
            {{ $t('autoStamp.buttonText') }}
        </el-button>

        <!-- 自动拖章进度弹框 -->
        <el-dialog
            :visible.sync="dialogVisible"
            :show-close="false"
            :close-on-click-modal="false"
            :close-on-press-escape="false"
            custom-class="auto-stamp-dialog"
            width="500px"
            :append-to-body="true"
        >
            <div class="auto-stamp-content">
                <div class="stamp-info">
                    <p class="stamp-title">
                        {{ $t('autoStamp.progressTitle', { documentNames: documentNamesText }) }}
                    </p>
                </div>

                <div class="progress-section">
                    <div class="progress-bar">
                        <div class="progress-text">{{ $t('autoStamp.progressText', { percentage: Math.round(progressPercentage) }) }}</div>
                        <div class="progress-container">
                            <div class="progress-fill" :style="{ width: progressPercentage + '%' }"></div>
                        </div>
                    </div>
                </div>

                <div class="dialog-footer">
                    <el-button
                        type="primary"
                        @click="handleCancel"
                        class="cancel-btn"
                    >
                        {{ $t('autoStamp.cancelButton') }}
                    </el-button>
                </div>
            </div>
        </el-dialog>
    </div>
</template>

<script>
import { getStampRecommendationRule, startStampRecommendationTask, getStampRecommendationTaskStatus } from 'src/api/template/index.js';
import { mapState } from 'vuex';

export default {
    name: 'AutoStampComponent',
    props: {
        templateId: {
            type: String,
            required: true,
        },
        draftId: {
            type: String,
            required: true,
        },
        docList: {
            type: Array,
            default: () => [],
        },
    },
    data() {
        return {
            dialogVisible: false,
            progressPercentage: 0,
            timer: null,
            hasConfigured: false, // 是否已配置过AIAgent
            taskId: null, // 拖章任务ID
            taskStatus: '', // 任务状态：IN_PROGRESS, COMPLETED, FAIL
            isTaskRunning: false, // 是否有任务正在运行
        };
    },
    computed: {
        ...mapState([
            'features',
        ]),
        // 检查是否显示自动拖章按钮
        shouldShowAutoStamp() {
            // 1. 检查features是否包含244
            const hasFeature244 = this.features.includes('244');
            // 2. 检查是否已配置过AIAgent（通过hasConfigured状态）
            return hasFeature244 && this.hasConfigured && !!this.documentNamesText;
        },

        // 生成文档名称文案
        documentNamesText() {
            return this.docList.filter(doc => doc.appendedForDraft).map(doc => `《${doc.documentName}》`).join('');
        },
    },
    methods: {
        // 检查AIAgent是否已配置
        async checkAIAgentConfig() {
            if (!this.templateId || !this.features.includes('244')) {
                this.hasConfigured = false;
                return;
            }
            getStampRecommendationRule(this.templateId).then(res => {
                const data = res.data;
                this.hasConfigured = data.useStampRecommendation ||
                    (data.stampStrategies && data.stampStrategies.length > 0);
            });
        },

        async handleAutoStamp() {
            // 提交拖章任务
            startStampRecommendationTask(this.draftId).then(res => {
                this.dialogVisible = true;
                this.progressPercentage = 0;
                this.isTaskRunning = true;
                this.taskId = res.data.result;
                this.startTaskPolling();
                this.startProgress();
            });
        },

        handleCancel() {
            this.dialogVisible = false;
            this.stopProgress();
            this.stopTaskPolling();
            this.resetTaskState();
        },

        // 重置任务状态
        resetTaskState() {
            this.progressPercentage = 0;
            this.taskId = null;
            this.taskStatus = '';
            this.isTaskRunning = false;
        },

        // 开始任务状态轮询
        startTaskPolling() {
            if (!this.taskId) {
                return;
            }

            this.taskPollingTimer = setInterval(async() => {
                try {
                    const res = await getStampRecommendationTaskStatus(this.draftId, this.taskId);

                    this.taskStatus = res.data.result;

                    if (this.taskStatus === 'COMPLETED') {
                        this.handleTaskComplete();
                    } else if (this.taskStatus === 'FAIL') {
                        this.handleTaskFail();
                    }
                    // IN_PROGRESS 状态继续轮询
                } catch (error) {
                    console.error('查询任务状态失败:', error);
                    this.handleTaskFail();
                }
            }, 2000); // 每2秒轮询一次
        },

        // 停止任务轮询
        stopTaskPolling() {
            if (this.taskPollingTimer) {
                clearInterval(this.taskPollingTimer);
                this.taskPollingTimer = null;
            }
        },

        startProgress() {
            // 模拟进度更新（假进度）
            this.progressTimer = setInterval(() => {
                if (this.progressPercentage < 90 && this.isTaskRunning) {
                    this.progressPercentage += Math.random() * 5;
                    if (this.progressPercentage > 90) {
                        this.progressPercentage = 90; // 最多到90%，等待真实任务完成
                    }
                }
            }, 1000);
        },

        stopProgress() {
            if (this.progressTimer) {
                clearInterval(this.progressTimer);
                this.progressTimer = null;
            }
        },

        // 任务完成处理
        handleTaskComplete() {
            this.stopProgress();
            this.stopTaskPolling();
            this.progressPercentage = 100;
            this.isTaskRunning = false;

            this.$message.success(this.$t('autoStamp.taskComplete'));

            // 延迟关闭弹框，让用户看到100%进度
            setTimeout(() => {
                this.dialogVisible = false;
                this.resetTaskState();

                // 通知父组件刷新文档视图
                this.$emit('taskComplete');
            }, 1500);
        },

        // 任务失败处理
        handleTaskFail() {
            this.stopProgress();
            this.stopTaskPolling();
            this.isTaskRunning = false;

            this.$message.error(this.$t('autoStamp.taskFailed'));
            this.dialogVisible = false;
            this.resetTaskState();
        },
    },
    mounted() {
        this.checkAIAgentConfig();
    },

    beforeDestroy() {
        this.stopProgress();
        this.stopTaskPolling();
    },
};
</script>

<style lang="scss" scoped>
.auto-stamp-component {
    display: inline-block;
    margin: 0 8px;
}

.auto-stamp-content {
    padding: 20px 0;

    .stamp-info {
        margin-bottom: 30px;

        .stamp-title {
            font-size: 14px;
            line-height: 1.6;
            color: #333;
            margin: 0;
            text-align: left;
        }
    }

    .progress-section {
        margin-bottom: 30px;

        .progress-bar {
            .progress-text {
                font-size: 14px;
                color: #333;
                margin-bottom: 15px;
                text-align: center;
            }

            .progress-container {
                width: 100%;
                height: 8px;
                background-color: #f0f0f0;
                border-radius: 4px;
                overflow: hidden;

                .progress-fill {
                    height: 100%;
                    background-color: #409eff;
                    border-radius: 4px;
                    transition: width 0.3s ease;
                }
            }
        }
    }

    .dialog-footer {
        text-align: center;

        .cancel-btn {
            background-color: #409eff;
            border-color: #409eff;
            color: #fff;
            padding: 12px 30px;
            font-size: 14px;
            border-radius: 4px;

            &:hover {
                background-color: #66b1ff;
                border-color: #66b1ff;
            }
        }
    }
}
</style>

<style lang="scss">
.auto-stamp-dialog {
    .el-dialog__header {
        display: none;
    }

    .el-dialog__body {
        padding: 30px 40px;
    }
}
</style>
