import AutoStampComponent from '../index.vue';
import { initWrapper } from 'src/testUtils';

// Mock API
jest.mock('src/api/template/index.js', () => ({
    getStampRecommendationRule: jest.fn(),
    startStampRecommendationTask: jest.fn(),
    getStampRecommendationTaskStatus: jest.fn(),
}));

describe('AutoStampComponent', () => {
    const mockGetStampRecommendationRule = require('src/api/template/index.js').getStampRecommendationRule;
    const mockStartStampRecommendationTask = require('src/api/template/index.js').startStampRecommendationTask;
    const mockGetStampRecommendationTaskStatus = require('src/api/template/index.js').getStampRecommendationTaskStatus;

    const baseStoreOptions = {
        state: {
            features: ['244'], // 包含244功能
        },
    };

    const baseWrapperOptions = {
        propsData: {
            draftId: 'test-draft-id',
            templateId: 'test-template-id',
            docList: [
                { documentName: '测试文档1' },
                { documentName: '测试文档2' },
            ],
        },
    };

    beforeEach(() => {
        jest.clearAllMocks();
    });

    test('当features包含244且已配置AIAgent时显示组件', async() => {
        // Mock API返回已配置的数据
        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: true,
                stampStrategies: [
                    { strategy: 'SEAL_ON_LAST_LINE_OF_TEXT', isOpen: true },
                ],
            },
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, baseWrapperOptions);

        // 等待异步操作完成
        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        expect(wrapper.vm.shouldShowAutoStamp).toBe(true);
        expect(wrapper.find('.auto-stamp-component').exists()).toBe(true);
        expect(wrapper.find('el-button').exists()).toBe(true);
    });

    test('当features不包含244时不显示组件', async() => {
        const storeOptions = {
            state: {
                features: [], // 不包含244功能
            },
        };

        const wrapper = initWrapper(AutoStampComponent, storeOptions, baseWrapperOptions);

        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        expect(wrapper.vm.shouldShowAutoStamp).toBe(false);
        expect(wrapper.find('.auto-stamp-component').exists()).toBe(false);
    });

    test('当未配置AIAgent时不显示组件', async() => {
        // Mock API返回未配置的数据
        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: false,
                stampStrategies: [],
            },
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, baseWrapperOptions);

        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        expect(wrapper.vm.shouldShowAutoStamp).toBe(false);
        expect(wrapper.find('.auto-stamp-component').exists()).toBe(false);
    });

    test('点击自动拖章按钮打开弹框', async() => {
        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: true,
                stampStrategies: [{ strategy: 'SEAL_ON_LAST_LINE_OF_TEXT', isOpen: true }],
            },
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, baseWrapperOptions);

        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        // 点击自动拖章按钮
        await wrapper.find('el-button').trigger('click');

        expect(wrapper.vm.dialogVisible).toBe(true);
    });

    test('点击取消按钮关闭弹框', async() => {
        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: true,
                stampStrategies: [{ strategy: 'SEAL_ON_LAST_LINE_OF_TEXT', isOpen: true }],
            },
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, baseWrapperOptions);

        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        // 打开弹框
        wrapper.vm.dialogVisible = true;
        await wrapper.vm.$nextTick();

        // 点击取消按钮
        await wrapper.find('.cancel-btn').trigger('click');

        expect(wrapper.vm.dialogVisible).toBe(false);
    });

    test('测试任务提交和轮询流程', async() => {
        // Mock API返回已配置的数据
        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: true,
                stampStrategies: [{ strategy: 'SEAL_ON_LAST_LINE_OF_TEXT', isOpen: true }],
            },
        });

        // Mock 任务提交成功
        mockStartStampRecommendationTask.mockResolvedValue({
            result: 'test-task-id-123',
        });

        // Mock 任务状态查询
        mockGetStampRecommendationTaskStatus.mockResolvedValue({
            result: 'COMPLETED',
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, baseWrapperOptions);

        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        // 点击自动拖章按钮
        await wrapper.vm.handleAutoStamp();

        expect(mockStartStampRecommendationTask).toHaveBeenCalledWith('test-draft-id');
        expect(wrapper.vm.taskId).toBe('test-task-id-123');
        expect(wrapper.vm.isTaskRunning).toBe(true);
    });

    test('测试文档名称显示', async() => {
        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: true,
                stampStrategies: [{ strategy: 'SEAL_ON_LAST_LINE_OF_TEXT', isOpen: true }],
            },
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, baseWrapperOptions);

        await wrapper.vm.$nextTick();
        await new Promise(resolve => setTimeout(resolve, 0));

        expect(wrapper.vm.documentNamesText).toBe('《测试文档1》《测试文档2》');
    });

    test('测试空文档列表时的默认显示', async() => {
        const wrapperOptions = {
            propsData: {
                draftId: 'test-draft-id',
                templateId: 'test-template-id',
                docList: [],
            },
        };

        mockGetStampRecommendationRule.mockResolvedValue({
            data: {
                useStampRecommendation: false,
                stampStrategies: [],
            },
        });

        const wrapper = initWrapper(AutoStampComponent, baseStoreOptions, wrapperOptions);

        await wrapper.vm.$nextTick();

        expect(wrapper.vm.documentNamesText).toBe('《xxx》《yyy》');
    });
});
