<template>
    <el-dialog
        :title="$t('copySealDialog.title')"
        width="450px"
        :visible.sync="visible"
        :modal-append-to-body="true"
        :close-on-click-modal="false"
        :before-close="handleClose"
        :append-to-body="true"
        class="el-dialog-bg copy-seal-dialog"
    >
        <p class="page-line">{{ $t('copySealDialog.pageDecTop') }}<el-input v-model.number="startPage" @change="validatePages" :min="1" :max="currentDoc.pageSize"></el-input> {{ $t('copySealDialog.pageDecMiddle') }}<el-input v-model.number="endPage" @change="validatePages" :max="currentDoc.pageSize"></el-input> {{ $t('copySealDialog.pageDecBottom') }}</p>
        <p>{{ $t('copySealDialog.dec') }}</p>
        <span class="more-dec" @click="showMore=!showMore">{{ $t('copySealDialog.moreBtn') }}</span>
        <p v-if="showMore">{{ $t('copySealDialog.moreDec') }}</p>
        <div slot="footer">
            <el-button
                @click="copySeal"
                type="primary"
            >{{ $t('copySealDialog.confirm') }}</el-button>
            <el-button
                @click="handleClose"
            >{{ $t('copySealDialog.cancel') }}</el-button>
        </div>
    </el-dialog>
</template>
<script>
import { mapGetters, mapState } from 'vuex';
import { saveCopySeal } from 'src/api/template/pointPosition.js';
export default {
    model: {
        prop: 'visible',
        event: 'change',
    },
    props: {
        visible: {
            default: false,
            type: Boolean,
        },
        labelId: {
            type: String,
        },
    },
    data() {
        return {
            startPage: 1,
            endPage: 1,
            lastValidStart: 1,
            lastValidEnd: 1,
            showMore: false,
        };
    },
    computed: {
        ...mapGetters('template', ['currentDoc']),
        ...mapState('template', {
            templateId: state => state.templateId,
        }),
    },
    methods: {
        handleClose() {
            this.$emit('change', false);
        },
        copySeal() {
            saveCopySeal(this.templateId, this.labelId, {
                startIndex: this.startPage,
                endIndex: this.endPage,
            }).then(({ data }) => {
                const labelList = this.currentDoc.labels || [];
                this.$set(this.currentDoc, 'decorateSealSize',  data.length);
                this.$set(this.currentDoc, 'labels', labelList.concat(data));
                this.handleClose();
            });
        },
        validatePages() {
            if (this.startPage < 0) {
                this.startPage = 1;
            }
            if (this.endPage > this.lastValidEnd) {
                this.endPage = this.lastValidEnd;
            }
            if (this.startPage > this.endPage) {
                this.startPage = this.lastValidStart;
                this.endPage = this.lastValidEnd;
                return;
            }
            this.lastValidStart = this.startPage;
            this.lastValidEnd = this.endPage;
        },
    },
    created() {
        this.endPage = this.currentDoc.pageSize;
        this.lastValidEnd = this.currentDoc.pageSize;
    },
};
</script>
<style lang="scss">
    .copy-seal-dialog{
        .page-line{
            margin-bottom: 10px;
            .el-input{
                width:40px;
                margin:0 6px;
                .el-input__inner{
                    padding: 0 4px;
                    text-align: center;
                }
            }
        }
        .more-dec{
            cursor: pointer;
            color: $theme-color;
            margin: 10px 0;
            display: block;
        }
    }
</style>
