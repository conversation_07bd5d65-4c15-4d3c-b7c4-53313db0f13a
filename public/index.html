<!DOCTYPE html>
<html lang="zh_cn">
<head>
    <meta charset="UTF-8">
    <meta name="renderer" content="webkit" />
    <meta name="msapplication-tap-highlight" content="no">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="format-detection" content="telephone=no, email=no" />
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1.0">
    <meta name="msapplication-tap-highlight" content="no">
    <meta name="screen-orientation" content="portrait">
    <meta name="x5-orientation" content="portrait">
    <meta name="full-screen" content="yes">
    <meta name="x5-fullscreen" content="true">
    <meta
        content="上上签，电子合同，电子签名，电子签约，电子缔约，数字签名，数字指纹，电子签章，电子印章，签名平台，电子文档，合同生成，合同签署，互联网签名，网上签合同，签名，签章，印章，签署，签字，盖章，CA，电子认证"
        name="keywords">
    <meta id="html-desc"
        content="上上签电子签名平台，上上签电子签名平台是由杭州尚尚签网络科技有限公司研发的一款在线互联网WEB终端及手机移动客户端，实时在线签署具有法律效力的电子签名缔约平台。 替代传统用纸笔的签署方式，提高了在线签署效率，为您在线签署省时，省力，异地签署重要合同和综合数字化方式电子签名签署平台。"
        name="description">
    <title id="html-title">上上签电子签约云平台</title>
</head>

<body>
    <script>
        let setScrollTop = function () {
            let top = document.documentElement.scrollTop || window.pageYOffset || document.body.scrollTop
            window.scrollTo(0, top + 1)
            setTimeout(function() {
                window.scrollTo(0, top)
            }, 300)
        }
        document.addEventListener('focusout', setScrollTop, false);
        /* ⚠️注意！不能随便添加外部链接，由于肯德基客户的特殊原因，cdn外部链接可能会导致用户白屏 */
        /* 以下条件满足的情况，肯德基客户并未进入，故可以添加 */
        /* 如果只是使用wx, 可以在需要的模块里直接 import wx from 'weixin-js-sdk' */
        if (window.location.search.indexOf('qywx=1') > -1 || (window.sessionStorage && window.sessionStorage.getItem &&
                window.sessionStorage.getItem('isQyWx'))) {
            document.writeln('<script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"' + '>' + '<' + '/' +
                'script>');
            document.writeln('<script src="https://js.cdn.aliyun.dcloud.net.cn/dev/uni-app/uni.webview.1.5.1.js"' +
                '>' + '<' + '/' + 'script>');
        } else if (window.location.search.indexOf('returnUrl') > -1 || document.cookie.indexOf('personAuthReturnUrl') >
            -1) {
            document.writeln('<script src="https://res.wx.qq.com/open/js/jweixin-1.3.2.js"' + '>' + '<' + '/' +
                'script>');
        }
    </script>
    <script>
        // 预发布或者线上接入前端监控
        var url = window.location.href;
        var isNeedWatch = url.indexOf('https://ent.bestsign') > -1 || url.indexOf('https://ccb.bestsign') > -1 || url.indexOf('https://ent2-k8s.bestsign') > -1;
        if (isNeedWatch) {
            var isPro = url.indexOf('bestsign.cn') > -1; // 线上环境
            !(function(c, d, a) {
                c[a] || (c[a] = {});
                c[a].config = {
                    pid: 'a4sdirdny6@2436a34ecae9017',
                    environment: isPro ? 'prod' : 'pre',
                    appType: "web",
                    imgUrl: "https://arms-retcode.aliyuncs.com/r.png?",
                    enableSPA: true,
                    disableHook:true,
                    useFmp: true,
                    sendResource: false  // 获取页面加载的静态资源信息,在页面onload时触发，上报信息量较大。如果应用对页面性能要求很高，则可以不开启该配置。
                };
                var dom = document.body.insertBefore(document.createElement("script"), document.body.firstChild);
                dom.setAttribute("crossorigin", "", dom.src = d)
            })(window, "https://retcode.alicdn.com/retcode/bl.js", "__bl");
        }
    </script>
    <div id="app"></div>
</body>
</html>
